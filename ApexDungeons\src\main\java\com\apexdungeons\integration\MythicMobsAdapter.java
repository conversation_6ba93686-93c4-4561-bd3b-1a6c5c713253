package com.apexdungeons.integration;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.LivingEntity;

/**
 * Mob adapter that integrates with MythicMobs plugin.
 * Falls back to vanilla adapter if MythicMobs is not available.
 */
public class MythicMobsAdapter implements MobAdapter {
    private final ApexDungeons plugin;
    private final VanillaAdapter fallback = new VanillaAdapter();

    public MythicMobsAdapter(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    @Override
    public LivingEntity spawnMob(String mobName, Location location) {
        if (mobName == null || location == null) return null;

        if (isAvailable()) {
            try {
                // Try to spawn using MythicMobs API
                Object mm = Bukkit.getPluginManager().getPlugin("MythicMobs");
                if (mm != null) {
                    Class<?> mythicClass = Class.forName("io.lumine.mythic.bukkit.MythicBukkit");
                    Object mythic = mythicClass.getMethod("inst").invoke(null);
                    Object mobManager = mythicClass.getMethod("getMobManager").invoke(mythic);

                    // Try to get the MythicMob
                    java.util.Optional<?> mythicMob = (java.util.Optional<?>) mobManager.getClass()
                        .getMethod("getMythicMob", String.class).invoke(mobManager, mobName);

                    if (mythicMob.isPresent()) {
                        Object mmob = mythicMob.get();
                        // Spawn the mob
                        Class<?> adapterClass = Class.forName("io.lumine.mythic.bukkit.adapter.BukkitAdapter");
                        Object spawnLoc = adapterClass.getMethod("adapt", org.bukkit.Location.class).invoke(null, location);
                        Object spawnedMob = mmob.getClass().getMethod("spawn",
                            Class.forName("io.lumine.mythic.api.world.MythicLocation"), int.class)
                            .invoke(mmob, spawnLoc, 1);

                        // Try to get the Bukkit entity
                        if (spawnedMob != null) {
                            Object entity = spawnedMob.getClass().getMethod("getEntity").invoke(spawnedMob);
                            if (entity instanceof LivingEntity) {
                                return (LivingEntity) entity;
                            }
                        }
                    }
                }
            } catch (Throwable ex) {
                plugin.getLogger().warning("Failed to spawn MythicMob '" + mobName + "': " + ex.getMessage());
            }
        }

        // Fall back to vanilla adapter
        return fallback.spawnMob(mobName, location);
    }

    @Override
    public LivingEntity spawnBoss(String bossName, Location location) {
        // For MythicMobs, bosses are just special mobs
        return spawnMob(bossName, location);
    }

    @Override
    public boolean isAvailable() {
        return Bukkit.getPluginManager().getPlugin("MythicMobs") != null;
    }

    @Override
    public String getAdapterName() {
        return "MythicMobs";
    }

    @Override
    public String[] getAvailableMobs() {
        if (isAvailable()) {
            try {
                // Try to get mob names from MythicMobs
                Object mm = Bukkit.getPluginManager().getPlugin("MythicMobs");
                if (mm != null) {
                    Class<?> mythicClass = Class.forName("io.lumine.mythic.bukkit.MythicBukkit");
                    Object mythic = mythicClass.getMethod("inst").invoke(null);
                    Object mobManager = mythicClass.getMethod("getMobManager").invoke(mythic);

                    @SuppressWarnings("unchecked")
                    java.util.Collection<String> mobNames = (java.util.Collection<String>)
                        mobManager.getClass().getMethod("getMobNames").invoke(mobManager);

                    return mobNames.toArray(new String[0]);
                }
            } catch (Throwable ex) {
                plugin.getLogger().warning("Failed to get MythicMobs mob list: " + ex.getMessage());
            }
        }

        // Fall back to vanilla mobs
        return fallback.getAvailableMobs();
    }

    @Override
    public String[] getAvailableBosses() {
        // For MythicMobs, bosses are typically just special mobs
        // We could filter by naming convention or return all mobs
        return getAvailableMobs();
    }
}