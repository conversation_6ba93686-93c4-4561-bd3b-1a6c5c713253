package com.apexdungeons.effects;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import org.bukkit.*;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.Collection;

/**
 * Manages visual and audio effects for dungeon events.
 * Provides particle effects, sounds, and notifications for various dungeon activities.
 */
public class EffectsManager {
    private final ApexDungeons plugin;

    public EffectsManager(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    /**
     * Play dungeon creation effects.
     */
    public void playDungeonCreationEffects(Player player, String dungeonName) {
        Location location = player.getLocation();

        // Sound effects
        player.playSound(location, Sound.BLOCK_BEACON_ACTIVATE, 1.0f, 1.2f);
        player.playSound(location, Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 0.8f);

        // Particle effects
        spawnParticleCircle(location, Particle.ENCHANT, 30, 3.0, 0.5);
        spawnParticleCircle(location, Particle.END_ROD, 20, 2.0, 1.0);

        // Chat notifications
        player.sendMessage(ChatColor.GOLD + "✦ " + ChatColor.GREEN + "Dungeon Creation Started!");
        player.sendMessage(ChatColor.GRAY + "Creating: " + ChatColor.AQUA + dungeonName);

        // Delayed celebration effect
        new BukkitRunnable() {
            @Override
            public void run() {
                player.playSound(location, Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f);
                spawnParticleExplosion(location, Particle.FIREWORK, 50, 2.0);
            }
        }.runTaskLater(plugin, 40L); // 2 seconds delay
    }

    /**
     * Play dungeon completion effects.
     */
    public void playDungeonCompletionEffects(Player player, DungeonInstance dungeon) {
        Location location = player.getLocation();

        // Sound effects
        player.playSound(location, Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f);
        player.playSound(location, Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.2f);

        // Particle effects
        spawnParticleExplosion(location, Particle.FIREWORK, 100, 3.0);
        spawnParticleExplosion(location, Particle.TOTEM_OF_UNDYING, 50, 2.0);

        // Chat notifications with formatting
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "★ ★ ★ " + ChatColor.GREEN + "DUNGEON COMPLETED!" + ChatColor.GOLD + " ★ ★ ★");
        player.sendMessage(ChatColor.GRAY + "Dungeon: " + ChatColor.AQUA + dungeon.getDisplayName());
        player.sendMessage(ChatColor.GRAY + "Rooms: " + ChatColor.YELLOW + dungeon.getRoomCount());
        player.sendMessage("");

        // Continuous celebration effect
        new BukkitRunnable() {
            int count = 0;
            @Override
            public void run() {
                if (count >= 5) {
                    cancel();
                    return;
                }

                player.playSound(location, Sound.ENTITY_FIREWORK_ROCKET_BLAST, 0.5f, 1.0f + (count * 0.2f));
                spawnParticleCircle(location.clone().add(0, count, 0), Particle.FIREWORK, 20, 1.0 + count, 0.1);
                count++;
            }
        }.runTaskTimer(plugin, 0L, 10L);
    }

    /**
     * Play dungeon entry effects.
     */
    public void playDungeonEntryEffects(Player player, DungeonInstance dungeon) {
        Location location = player.getLocation();

        // Sound effects
        player.playSound(location, Sound.BLOCK_PORTAL_TRAVEL, 1.0f, 0.8f);
        player.playSound(location, Sound.AMBIENT_CAVE, 0.5f, 1.2f);

        // Particle effects
        spawnParticleSpiral(location, Particle.PORTAL, 30, 2.0, 3.0);
        spawnParticleCircle(location, Particle.LARGE_SMOKE, 15, 1.5, 0.1);

        // Chat notifications
        player.sendMessage(ChatColor.DARK_PURPLE + "⚡ " + ChatColor.LIGHT_PURPLE + "Entering Dungeon...");
        player.sendMessage(ChatColor.GRAY + "Welcome to: " + ChatColor.AQUA + dungeon.getDisplayName());
        player.sendMessage(ChatColor.GRAY + "Creator: " + ChatColor.YELLOW + dungeon.getCreator());

        // Title display
        player.sendTitle(
            ChatColor.DARK_PURPLE + "Entering Dungeon",
            ChatColor.AQUA + dungeon.getDisplayName(),
            10, 40, 10
        );
    }

    /**
     * Play dungeon exit effects.
     */
    public void playDungeonExitEffects(Player player, String dungeonName) {
        Location location = player.getLocation();

        // Sound effects
        player.playSound(location, Sound.BLOCK_PORTAL_TRAVEL, 1.0f, 1.2f);
        player.playSound(location, Sound.ENTITY_ENDERMAN_TELEPORT, 0.8f, 1.0f);

        // Particle effects
        spawnParticleSpiral(location, Particle.END_ROD, 25, 1.5, 2.5);
        spawnParticleCircle(location, Particle.CLOUD, 20, 2.0, 0.2);

        // Chat notifications
        player.sendMessage(ChatColor.GREEN + "⬅ " + ChatColor.YELLOW + "Returning to Main World");
        player.sendMessage(ChatColor.GRAY + "Left dungeon: " + ChatColor.AQUA + dungeonName);

        // Title display
        player.sendTitle(
            ChatColor.GREEN + "Returning",
            ChatColor.YELLOW + "Main World",
            10, 30, 10
        );
    }

    /**
     * Play room generation effects.
     */
    public void playRoomGenerationEffects(Location location, String roomName) {
        World world = location.getWorld();
        if (world == null) return;

        // Particle effects
        spawnParticleCircle(location, Particle.BLOCK, 40, 2.0, 0.3);
        spawnParticleExplosion(location, Particle.SMOKE, 30, 1.5);

        // Sound effects for nearby players
        Collection<Player> nearbyPlayers = world.getNearbyEntities(location, 20, 20, 20)
            .stream()
            .filter(entity -> entity instanceof Player)
            .map(entity -> (Player) entity)
            .toList();

        for (Player player : nearbyPlayers) {
            player.playSound(location, Sound.BLOCK_STONE_BREAK, 0.8f, 0.8f);
            player.playSound(location, Sound.BLOCK_GRAVEL_BREAK, 0.6f, 1.2f);
        }
    }

    /**
     * Spawn particles in a circle pattern.
     */
    private void spawnParticleCircle(Location center, Particle particle, int count, double radius, double height) {
        World world = center.getWorld();
        if (world == null) return;

        for (int i = 0; i < count; i++) {
            double angle = 2 * Math.PI * i / count;
            double x = center.getX() + radius * Math.cos(angle);
            double z = center.getZ() + radius * Math.sin(angle);
            double y = center.getY() + height;

            Location particleLocation = new Location(world, x, y, z);
            world.spawnParticle(particle, particleLocation, 1, 0, 0, 0, 0);
        }
    }

    /**
     * Spawn particles in an explosion pattern.
     */
    private void spawnParticleExplosion(Location center, Particle particle, int count, double spread) {
        World world = center.getWorld();
        if (world == null) return;

        world.spawnParticle(particle, center, count, spread, spread, spread, 0.1);
    }

    /**
     * Spawn particles in a spiral pattern.
     */
    private void spawnParticleSpiral(Location center, Particle particle, int count, double radius, double height) {
        World world = center.getWorld();
        if (world == null) return;

        new BukkitRunnable() {
            int step = 0;
            @Override
            public void run() {
                if (step >= count) {
                    cancel();
                    return;
                }

                double angle = 2 * Math.PI * step / 10; // 10 particles per rotation
                double currentRadius = radius * (1.0 - (double) step / count);
                double currentHeight = height * step / count;

                double x = center.getX() + currentRadius * Math.cos(angle);
                double z = center.getZ() + currentRadius * Math.sin(angle);
                double y = center.getY() + currentHeight;

                Location particleLocation = new Location(world, x, y, z);
                world.spawnParticle(particle, particleLocation, 1, 0, 0, 0, 0);

                step++;
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }

    /**
     * Broadcast dungeon event to all players.
     */
    public void broadcastDungeonEvent(String message, DungeonInstance dungeon) {
        String formattedMessage = ChatColor.GOLD + "[Dungeons] " + ChatColor.RESET + message;

        for (Player player : Bukkit.getOnlinePlayers()) {
            player.sendMessage(formattedMessage);
        }

        plugin.getLogger().info("Dungeon Event: " + message);
    }

    /**
     * Play ambient dungeon sounds for atmosphere.
     */
    public void playAmbientDungeonSounds(Player player) {
        Location location = player.getLocation();

        new BukkitRunnable() {
            @Override
            public void run() {
                if (!plugin.getWorldManager().isDungeonWorld(player.getWorld())) {
                    cancel();
                    return;
                }

                // Random ambient sounds
                Sound[] ambientSounds = {
                    Sound.AMBIENT_CAVE,
                    Sound.BLOCK_FIRE_AMBIENT,
                    Sound.ENTITY_BAT_AMBIENT
                };

                Sound randomSound = ambientSounds[(int) (Math.random() * ambientSounds.length)];
                player.playSound(location, randomSound, 0.3f, 0.8f + (float) (Math.random() * 0.4f));
            }
        }.runTaskTimer(plugin, 200L, 400L + (long) (Math.random() * 400)); // Random intervals
    }
}