package com.apexdungeons.integration;

import org.bukkit.Location;
import org.bukkit.entity.LivingEntity;

/**
 * Interface for mob spawning adapters.
 * Allows switching between MythicMobs and vanilla mob spawning.
 */
public interface MobAdapter {

    /**
     * Spawn a mob at the specified location.
     * @param mobName The name/type of the mob to spawn
     * @param location The location to spawn the mob
     * @return The spawned entity, or null if spawning failed
     */
    LivingEntity spawnMob(String mobName, Location location);

    /**
     * Spawn a boss mob at the specified location.
     * @param bossName The name/type of the boss to spawn
     * @param location The location to spawn the boss
     * @return The spawned entity, or null if spawning failed
     */
    LivingEntity spawnBoss(String bossName, Location location);

    /**
     * Check if this adapter is available (plugin installed, etc.)
     * @return true if the adapter can be used
     */
    boolean isAvailable();

    /**
     * Get the name of this adapter.
     * @return The adapter name
     */
    String getAdapterName();

    /**
     * Get a list of available mob names for this adapter.
     * @return Array of mob names that can be spawned
     */
    String[] getAvailableMobs();

    /**
     * Get a list of available boss names for this adapter.
     * @return Array of boss names that can be spawned
     */
    String[] getAvailableBosses();
}