package org.bukkit;

import org.bukkit.entity.Player;

/**
 * Minimal stub for Location.  Provides coordinate storage and simple
 * arithmetic operations.  Does not reflect pitch/yaw or orientation.
 */
public class Location implements Cloneable {
    private final World world;
    private double x;
    private double y;
    private double z;
    public Location(World world, double x, double y, double z) {
        this.world = world;
        this.x = x;
        this.y = y;
        this.z = z;
    }
    public World getWorld() { return world; }
    public double getX() { return x; }
    public double getY() { return y; }
    public double getZ() { return z; }
    public Location add(double x, double y, double z) {
        this.x += x; this.y += y; this.z += z; return this;
    }
    public Location add(int x, int y, int z) {
        return add((double) x, (double) y, (double) z);
    }
    public Location clone() {
        return new Location(world, x, y, z);
    }
    public org.bukkit.block.Block getBlock() { return world.getBlockAt(this); }
}