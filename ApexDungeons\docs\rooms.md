# Room Blueprints

Rooms form the building blocks of every dungeon generated by ApexDungeons.  Each room is defined by a YAML file
in the `rooms/` directory and describes the blocks, connectors, loot locations and other metadata required to
instantiate it in the world.

The minimal schema for a room file looks like this:

```yaml
name: crypt_hallway            # Unique identifier used internally
theme: crypt                   # Theme tag used to group similar rooms
size: [width, height, depth]   # Dimensions of the room in blocks
connectors:
  - facing: NORTH             # Direction this connector points toward
    position: [x, y, z]       # Location of the connector relative to the room origin
palette:
  0: minecraft:air            # Block IDs are mapped to palette indices for compactness
  1: minecraft:stone_bricks
  2: minecraft:torch
layout:
  - # Layer 0 (lowest Y)
    - [[1, width]]            # Run‑length encoded rows.  Each pair is [paletteIndex, count]
    - [[1, width]]
  - # Layer 1
    - [[1,1],[0,width-2],[1,1]]
    - [[1,1],[0,width-2],[1,1]]
  # additional layers ...
```

### Connectors

Connectors are special marker positions that define where other rooms may attach to this room.  The `facing` value
can be any of the six cardinal directions (`NORTH`, `SOUTH`, `EAST`, `WEST`, `UP`, `DOWN`).  When generating a
dungeon the generator looks for compatible connectors and stitches rooms together like jigsaw pieces.  Rooms can
declare multiple connectors.

### Palette and Layout

To reduce file size and increase readability a palette is used to map small integer indices to actual Minecraft
block identifiers.  The `layout` array consists of one element per Y layer.  Each layer is described as a list of
rows, and each row is a list of `[paletteIndex, count]` pairs (a simple run‑length encoding).  Blocks are
constructed from the palette as the generator iterates over the layout.

Indices not present in the palette are treated as air.  Air blocks outside of the room's bounding box are never
placed, allowing the dungeon to blend naturally into surrounding terrain.

### Additional Metadata

Rooms may define additional keys beyond those shown here.  For example:

- `loot`: coordinates within the room where chests should be placed and which loot table to use.
- `spawners`: locations and types of mobs to spawn when a player enters the room.
- `flags`: markers such as `start`, `end`, `branch`, `hub`, `tower` or `boss`.  These influence the generator's
  selection algorithm.

You can experiment with the built in rooms in the `rooms/` folder and create your own variations.  To import a
new room at runtime place the YAML file into `plugins/ApexDungeons/rooms/` and use the `/dgn admin` GUI to reload
rooms.  There are also `/dgn exportroom` and `/dgn importroom` commands available to serialize and load rooms from
in game.