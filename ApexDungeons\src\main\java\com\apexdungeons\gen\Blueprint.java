package com.apexdungeons.gen;

import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.Plugin;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Represents a room blueprint loaded from YAML.  The blueprint stores a palette
 * of materials and a 3D layout of palette indices.  Additional metadata
 * including size, theme and connectors are also available.
 */
public class Blueprint {
    private final String name;
    private final String theme;
    private final int width;
    private final int height;
    private final int depth;
    private final List<Connector> connectors;
    private final Map<Integer, Material> palette;
    private final int[][][] layout;

    public record Connector(String facing, int x, int y, int z) {}

    public Blueprint(String name, String theme, int width, int height, int depth, List<Connector> connectors, Map<Integer, Material> palette, int[][][] layout) {
        this.name = name;
        this.theme = theme;
        this.width = width;
        this.height = height;
        this.depth = depth;
        this.connectors = connectors;
        this.palette = palette;
        this.layout = layout;
    }

    public String getName() {
        return name;
    }

    public String getTheme() {
        return theme;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public int getDepth() {
        return depth;
    }

    public List<Connector> getConnectors() {
        return connectors;
    }

    public Map<Integer, Material> getPalette() {
        return palette;
    }

    public int[][][] getLayout() {
        return layout;
    }

    /**
     * Load a blueprint from a YAML file.  If the file is invalid or missing,
     * returns null.
     */
    public static Blueprint load(File file) {
        if (file == null || !file.exists()) return null;
        FileConfiguration conf = new YamlConfiguration();
        try {
            conf.load(file);
        } catch (IOException | org.bukkit.configuration.InvalidConfigurationException e) {
            e.printStackTrace();
            return null;
        }
        String name = conf.getString("name", file.getName().replace(".yml", ""));
        String theme = conf.getString("theme", "default");

        // Support both old and new format
        int width, height, depth;

        // Try new format first (width, height, depth as separate keys)
        if (conf.contains("width") && conf.contains("height") && conf.contains("depth")) {
            width = conf.getInt("width");
            height = conf.getInt("height");
            depth = conf.getInt("depth");
        } else {
            // Fall back to old format (size list)
            List<Integer> sizeList = conf.getIntegerList("size");
            if (sizeList.size() < 3) return null;
            width = sizeList.get(0);
            height = sizeList.get(1);
            depth = sizeList.get(2);
        }
        // Palette - support both character-based and numeric palettes
        Map<Integer, Material> palette = new HashMap<>();
        Map<Character, Material> charPalette = new HashMap<>();

        if (conf.isConfigurationSection("palette")) {
            for (String key : conf.getConfigurationSection("palette").getKeys(false)) {
                String materialStr = conf.getString("palette." + key, "minecraft:air");

                // Remove minecraft: prefix if present
                if (materialStr.startsWith("minecraft:")) {
                    materialStr = materialStr.substring(10);
                }

                Material mat = Material.matchMaterial(materialStr.toUpperCase());
                if (mat == null) mat = Material.AIR;

                // Try to parse as integer (old format)
                try {
                    int id = Integer.parseInt(key);
                    palette.put(id, mat);
                } catch (NumberFormatException ex) {
                    // Character-based palette (new format)
                    if (key.length() == 1) {
                        charPalette.put(key.charAt(0), mat);
                    }
                }
            }
        }
        // Layout - support both old and new formats
        int[][][] layout = new int[height][depth][width];

        if (conf.isConfigurationSection("layout")) {
            // New format: layout as configuration section with Y levels
            for (String yKey : conf.getConfigurationSection("layout").getKeys(false)) {
                try {
                    int y = Integer.parseInt(yKey);
                    if (y >= height) continue;

                    List<String> rows = conf.getStringList("layout." + yKey);
                    for (int z = 0; z < Math.min(depth, rows.size()); z++) {
                        String row = rows.get(z);
                        for (int x = 0; x < Math.min(width, row.length()); x++) {
                            char c = row.charAt(x);

                            // Convert character to material ID
                            if (charPalette.containsKey(c)) {
                                // Find or create numeric ID for this material
                                Material mat = charPalette.get(c);
                                int id = getOrCreateMaterialId(palette, mat);
                                layout[y][z][x] = id;
                            }
                        }
                    }
                } catch (NumberFormatException e) {
                    // Skip invalid Y keys
                }
            }
        } else {
            // Old format: layout as list
            List<?> layers = conf.getList("layout");
            if (layers != null) {
                for (int y = 0; y < Math.min(height, layers.size()); y++) {
                    Object layerObj = layers.get(y);
                    if (!(layerObj instanceof List<?> rows)) continue;
                    for (int z = 0; z < Math.min(depth, ((List<?>) rows).size()); z++) {
                        Object rowObj = ((List<?>) rows).get(z);
                        if (!(rowObj instanceof List<?> runList)) continue;
                        int x = 0;
                        for (Object runObj : ((List<?>) runList)) {
                            if (runObj instanceof List<?> pair && ((List<?>) pair).size() >= 2) {
                                int id = ((Number) ((List<?>) pair).get(0)).intValue();
                                int count = ((Number) ((List<?>) pair).get(1)).intValue();
                                for (int i = 0; i < count && x < width; i++) {
                                    layout[y][z][x] = id;
                                    x++;
                                }
                            }
                        }
                    }
                }
            }
        }
        // Connectors
        List<Connector> connectors = new ArrayList<>();
        if (conf.isList("connectors")) {
            for (Object obj : conf.getList("connectors")) {
                if (obj instanceof Map<?,?> map) {
                    String facing = (String) map.get("facing");
                    List<?> pos = (List<?>) map.get("position");
                    if (pos != null && pos.size() >= 3) {
                        int x = ((Number) pos.get(0)).intValue();
                        int y = ((Number) pos.get(1)).intValue();
                        int z = ((Number) pos.get(2)).intValue();
                        connectors.add(new Connector(facing, x, y, z));
                    }
                }
            }
        }
        return new Blueprint(name, theme, width, height, depth, connectors, palette, layout);
    }

    /**
     * Helper method to get or create a numeric ID for a material in the palette.
     */
    private static int getOrCreateMaterialId(Map<Integer, Material> palette, Material material) {
        // Check if material already exists in palette
        for (Map.Entry<Integer, Material> entry : palette.entrySet()) {
            if (entry.getValue() == material) {
                return entry.getKey();
            }
        }

        // Create new ID for this material
        int newId = palette.size();
        palette.put(newId, material);
        return newId;
    }
}