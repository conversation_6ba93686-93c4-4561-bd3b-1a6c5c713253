package com.apexdungeons.chests;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Represents a loot table with weighted random item generation.
 */
public class ChestLootTable {
    private final String name;
    private final List<WeightedItem> items = new ArrayList<>();
    private final Random random = new Random();
    
    public ChestLootTable(String name) {
        this.name = name;
    }
    
    /**
     * Add an item with weight to the loot table.
     */
    public void addItem(ItemStack item, int weight) {
        items.add(new WeightedItem(item.clone(), weight));
    }
    
    /**
     * Remove an item from the loot table.
     */
    public boolean removeItem(Material material) {
        return items.removeIf(weightedItem -> weightedItem.getItem().getType() == material);
    }
    
    /**
     * Generate loot based on weighted probabilities.
     */
    public List<ItemStack> generateLoot() {
        List<ItemStack> loot = new ArrayList<>();
        
        if (items.isEmpty()) {
            return loot;
        }
        
        // Calculate total weight
        int totalWeight = items.stream().mapToInt(WeightedItem::getWeight).sum();
        
        // Generate 1-4 items
        int itemCount = 1 + random.nextInt(4);
        
        for (int i = 0; i < itemCount; i++) {
            WeightedItem selectedItem = selectRandomItem(totalWeight);
            if (selectedItem != null) {
                loot.add(selectedItem.getItem().clone());
            }
        }
        
        return loot;
    }
    
    /**
     * Select a random item based on weights.
     */
    private WeightedItem selectRandomItem(int totalWeight) {
        if (totalWeight <= 0) return null;
        
        int randomValue = random.nextInt(totalWeight);
        int currentWeight = 0;
        
        for (WeightedItem item : items) {
            currentWeight += item.getWeight();
            if (randomValue < currentWeight) {
                return item;
            }
        }
        
        // Fallback to last item
        return items.isEmpty() ? null : items.get(items.size() - 1);
    }
    
    /**
     * Get all items in the loot table.
     */
    public List<WeightedItem> getItems() {
        return new ArrayList<>(items);
    }
    
    /**
     * Get the name of this loot table.
     */
    public String getName() {
        return name;
    }
    
    /**
     * Check if the loot table is empty.
     */
    public boolean isEmpty() {
        return items.isEmpty();
    }
    
    /**
     * Get the total weight of all items.
     */
    public int getTotalWeight() {
        return items.stream().mapToInt(WeightedItem::getWeight).sum();
    }
    
    /**
     * Represents an item with a weight for random selection.
     */
    public static class WeightedItem {
        private final ItemStack item;
        private final int weight;
        
        public WeightedItem(ItemStack item, int weight) {
            this.item = item.clone();
            this.weight = Math.max(1, weight); // Ensure weight is at least 1
        }
        
        public ItemStack getItem() {
            return item.clone();
        }
        
        public int getWeight() {
            return weight;
        }
        
        /**
         * Get the probability of this item being selected (as percentage).
         */
        public double getProbability(int totalWeight) {
            return totalWeight > 0 ? (double) weight / totalWeight * 100.0 : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("WeightedItem{item=%s x%d, weight=%d}", 
                item.getType().name(), item.getAmount(), weight);
        }
    }
}
