package com.apexdungeons.wand;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.WandGUI;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.EquipmentSlot;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.plugin.java.JavaPlugin;
import java.util.List;

/**
 * Manages the Dungeon Architect Wand.  Responsible for creating the item and
 * handling basic interaction such as opening the room selection GUI.
 */
public class WandManager implements Listener {
    private final ApexDungeons plugin;
    private final NamespacedKey wandKey;

    public WandManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.wandKey = new NamespacedKey(plugin, "wand");
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Creates the wand item stack with a persistent tag.
     */
    private ItemStack createWandItem() {
        ItemStack wand = new ItemStack(Material.BRUSH);
        ItemMeta meta = wand.getItemMeta();
        meta.setDisplayName(ChatColor.GOLD + "Dungeon Architect Wand");
        // Hard coded lore; messages are loaded from messages.yml but not parsed here.
        meta.setLore(List.of(
                ChatColor.GRAY + "Right click: Preview & place room",
                ChatColor.GRAY + "Left click: Rotate 90 deg",
                ChatColor.GRAY + "Sneak + scroll: Change height",
                ChatColor.GRAY + "Sneak + right click: Room selector"
        ));
        // Mark the item with a custom tag to identify it later
        meta.getPersistentDataContainer().set(wandKey, PersistentDataType.INTEGER, 1);
        wand.setItemMeta(meta);
        return wand;
    }

    /**
     * Give the wand to a player.  If their inventory is full the item is dropped.
     */
    public void giveWand(Player player) {
        ItemStack wand = createWandItem();
        player.getInventory().addItem(wand).forEach((slot, item) -> player.getWorld().dropItem(player.getLocation(), item));
        player.sendMessage(ChatColor.GREEN + "You have been given the Dungeon Architect Wand.");
    }

    /**
     * Check whether an item stack is the wand.
     */
    private boolean isWand(ItemStack stack) {
        if (stack == null || !stack.hasItemMeta()) return false;
        ItemMeta meta = stack.getItemMeta();
        return meta.getPersistentDataContainer().has(wandKey, PersistentDataType.INTEGER);
    }

    @EventHandler
    public void onInteract(PlayerInteractEvent event) {
        // Only respond to main hand interactions with the wand
        if (event.getHand() != EquipmentSlot.HAND) return;
        if (!isWand(event.getItem())) return;
        event.setCancelled(true);
        Player player = event.getPlayer();
        // Right click opens room selection GUI
        switch (event.getAction()) {
            case RIGHT_CLICK_AIR, RIGHT_CLICK_BLOCK -> {
                WandGUI.open(player, plugin);
            }
            case LEFT_CLICK_AIR, LEFT_CLICK_BLOCK -> {
                // For now, left click rotates selected room but we simply inform the player
                player.sendMessage(ChatColor.YELLOW + "Rotation controls not implemented yet.");
            }
            default -> {}
        }
    }
}