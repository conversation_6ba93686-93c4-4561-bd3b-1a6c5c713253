package com.apexdungeons.gen;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Central manager for dungeon instances, presets and blueprints.  Provides
 * operations to create and remove dungeons, teleport players and place
 * individual rooms via the architect wand.
 */
public class DungeonManager {
    private final ApexDungeons plugin;
    private final Map<String, DungeonInstance> dungeons = new ConcurrentHashMap<>();
    private final Map<String, Blueprint> blueprints = new HashMap<>();
    private final Map<String, DungeonPreset> presets = new HashMap<>();

    public DungeonManager(ApexDungeons plugin) {
        this.plugin = plugin;
        loadBlueprints();
        loadPresets();
    }

    /**
     * Load all blueprints from the data folder into memory.  Invalid files are
     * skipped.  This method is called on plugin startup and can be triggered
     * manually via the admin GUI when import/export features are added.
     */
    public void loadBlueprints() {
        blueprints.clear();
        File roomsDir = new File(plugin.getDataFolder(), "rooms");
        if (!roomsDir.exists()) roomsDir.mkdirs();
        File[] files = roomsDir.listFiles((dir, name) -> name.endsWith(".yml") || name.endsWith(".yaml"));
        if (files != null) {
            for (File file : files) {
                Blueprint bp = Blueprint.load(file);
                if (bp != null) {
                    blueprints.put(bp.getName(), bp);
                }
            }
        }
        plugin.getLogger().info("Loaded " + blueprints.size() + " room blueprints.");
    }

    /**
     * Load presets from the data folder.
     */
    public void loadPresets() {
        presets.clear();
        File presetsDir = new File(plugin.getDataFolder(), "presets");
        if (!presetsDir.exists()) presetsDir.mkdirs();
        File[] files = presetsDir.listFiles((dir, name) -> name.endsWith(".yml") || name.endsWith(".yaml"));
        if (files != null) {
            for (File file : files) {
                DungeonPreset preset = DungeonPreset.load(file);
                if (preset != null) {
                    String key = file.getName().replace(".yml", "").replace(".yaml", "");
                    presets.put(key, preset);
                }
            }
        }
        plugin.getLogger().info("Loaded " + presets.size() + " presets.");
    }

    /**
     * Create a dungeon directly without preset selection.
     * Used when /dgn create is called.
     */
    public void createDungeon(String name, Player player) {
        if (dungeons.containsKey(name)) {
            player.sendMessage(ChatColor.RED + "A dungeon with that name already exists!");
            return;
        }

        // Validate dungeon name
        if (!isValidDungeonName(name)) {
            player.sendMessage(ChatColor.RED + "Invalid dungeon name! Names must be 3-32 characters, alphanumeric with hyphens and underscores only.");
            return;
        }

        // Check max active limit (default 10)
        int maxActiveDungeons = 10;
        if (dungeons.size() >= maxActiveDungeons) {
            player.sendMessage(ChatColor.RED + "Maximum number of active dungeons reached (" + maxActiveDungeons + ")!");
            return;
        }

        player.sendMessage(ChatColor.GREEN + "Creating dungeon world: " + ChatColor.AQUA + name);
        player.sendMessage(ChatColor.GRAY + "This may take a moment...");

        // Create the dungeon world directly
        plugin.getWorldManager().createDungeonWorld(name).thenAccept(world -> {
            if (world != null) {
                // Create dungeon instance
                Location spawnLocation = new Location(world, 0, 65, 0); // Spawn on flat surface
                DungeonInstance dungeon = new DungeonInstance(plugin, name, world, spawnLocation, 1);
                dungeons.put(name, dungeon);

                player.sendMessage(ChatColor.GREEN + "✓ Dungeon '" + ChatColor.AQUA + name + ChatColor.GREEN + "' created successfully!");
                player.sendMessage(ChatColor.YELLOW + "Use " + ChatColor.AQUA + "/dgn tp " + name + ChatColor.YELLOW + " to visit your dungeon");
                player.sendMessage(ChatColor.GRAY + "Open the Building Tools GUI to start designing your dungeon!");

                plugin.getLogger().info("Created dungeon: " + name + " by " + player.getName());
            } else {
                player.sendMessage(ChatColor.RED + "✗ Failed to create dungeon world!");
            }
        });
    }

    /**
     * Create a dungeon with a custom display name and preset.
     */
    public void createDungeonWithName(String displayName, String presetName, Player player) {
        if (!isValidDungeonName(displayName)) {
            player.sendMessage(ChatColor.RED + "Invalid dungeon name! Names must be 3-32 characters, alphanumeric with spaces, hyphens, and underscores only.");
            return;
        }

        String internalName = generateInternalName(displayName);
        if (dungeons.containsKey(internalName)) {
            player.sendMessage(ChatColor.RED + "A dungeon with that name already exists!");
            return;
        }

        createDungeonFromPresetWithDisplayName(internalName, displayName, presetName, player);
    }

    /**
     * Create a dungeon using a preset name.  The dungeon is placed relative to
     * the player's location.  A simple one room dungeon is generated in this
     * demonstration implementation.
     */
    public void createDungeonFromPreset(String name, String presetName, Player player) {
        if (dungeons.containsKey(name)) {
            player.sendMessage(ChatColor.RED + "A dungeon with that name already exists.");
            return;
        }
        // Respect max active limit
        int maxActive = plugin.getConfig().getInt("generation.maxActive", 4);
        if (dungeons.size() >= maxActive) {
            player.sendMessage(ChatColor.RED + "Maximum active dungeons reached. Please remove one before creating another.");
            return;
        }
        // Determine generation world
        World world = player.getWorld();
        // Determine origin: offset far away on X axis to avoid collision with existing structures
        Location origin = player.getLocation().getBlock().getLocation().add(100 + (dungeons.size() * 200), 0, 0);
        Blueprint startBlueprint = blueprints.get("starter_room");
        if (startBlueprint == null) {
            player.sendMessage(ChatColor.RED + "Starter room blueprint not found.");
            return;
        }
        DungeonInstance instance = new DungeonInstance(plugin, name, world, origin, 1);
        dungeons.put(name, instance);
        player.sendMessage(ChatColor.GREEN + "Generating dungeon " + name + "...");
        // Place the starter room asynchronously with a block limit per tick
        placeBlueprintGradually(startBlueprint, origin, () -> {
            instance.setGenerating(false);
            player.sendMessage(ChatColor.GREEN + "Dungeon " + name + " generated!");
        });
    }

    /**
     * Create a dungeon with both internal and display names.
     */
    public void createDungeonFromPresetWithDisplayName(String internalName, String displayName, String presetName, Player player) {
        if (dungeons.containsKey(internalName)) {
            player.sendMessage(ChatColor.RED + "A dungeon with that name already exists.");
            return;
        }
        // Respect max active limit
        int maxActive = plugin.getConfig().getInt("generation.maxActive", 4);
        if (dungeons.size() >= maxActive) {
            player.sendMessage(ChatColor.RED + "Maximum active dungeons reached. Please remove one before creating another.");
            return;
        }
        // Create isolated world for this dungeon
        player.sendMessage(ChatColor.YELLOW + "Creating isolated world for dungeon \"" + displayName + "\"...");

        plugin.getWorldManager().createDungeonWorld(internalName).thenAccept(world -> {
            if (world == null) {
                player.sendMessage(ChatColor.RED + "Failed to create world for dungeon!");
                return;
            }

            // Set safe spawn location in the new world
            Location origin = findSafeSpawnLocation(world);

            DungeonInstance instance = new DungeonInstance(plugin, internalName, displayName,
                player.getName(), System.currentTimeMillis(), world, origin, 1);
            dungeons.put(internalName, instance);

            // Play dungeon creation effects
            plugin.getEffectsManager().playDungeonCreationEffects(player, displayName);

            // Check if this is a "normal" dungeon (empty world)
            if (presetName.equalsIgnoreCase("normal")) {
                // For normal dungeons, just create empty world and teleport player
                instance.setGenerating(false);

                // Ensure spawn area is safe and flat
                prepareSafeSpawnArea(origin);

                // Play dungeon completion effects
                plugin.getEffectsManager().playDungeonCompletionEffects(player, instance);

                player.sendMessage(ChatColor.AQUA + "Empty dungeon world created successfully!");
                player.sendMessage(ChatColor.GRAY + "You can now build your custom dungeon using the Building Tools!");

                // Set spawn location and teleport player
                setDungeonSpawn(instance, origin);
                player.teleport(origin);
            } else {
                // Generate dungeon based on preset for themed dungeons
                Blueprint startBlueprint = blueprints.get("starter_room");
                if (startBlueprint == null) {
                    player.sendMessage(ChatColor.RED + "Starter room blueprint not found.");
                    return;
                }

                // Generate full dungeon structure based on preset
                generateDungeonFromPreset(instance, presetName, origin, () -> {
                    instance.setGenerating(false);

                    // Play dungeon completion effects
                    plugin.getEffectsManager().playDungeonCompletionEffects(player, instance);

                    player.sendMessage(ChatColor.AQUA + "Dungeon created successfully!");
                    player.sendMessage(ChatColor.GRAY + "You can now place mob spawns, boss spawns, and chest spawns using the Building Tools!");

                    // Set spawn location and teleport player
                    setDungeonSpawn(instance, origin);
                    player.teleport(origin);
                });
            }
        }).exceptionally(throwable -> {
            player.sendMessage(ChatColor.RED + "Failed to create dungeon world: " + throwable.getMessage());
            plugin.getLogger().severe("Error creating dungeon world: " + throwable.getMessage());
            return null;
        });
    }

    /**
     * Gradually place a blueprint at a given world location to avoid lag spikes.
     * Blocks are placed at a rate defined by blocksPerTick in config.yml.
     * When complete, the onComplete callback is executed.
     */
    private void placeBlueprintGradually(Blueprint bp, Location origin, Runnable onComplete) {
        int maxPerTick = plugin.getConfig().getInt("performance.blocksPerTick", 8000);
        // Prepare list of block placements
        List<BlockPlacement> blocks = new ArrayList<>();
        for (int y = 0; y < bp.getHeight(); y++) {
            for (int z = 0; z < bp.getDepth(); z++) {
                for (int x = 0; x < bp.getWidth(); x++) {
                    int id = bp.getLayout()[y][z][x];
                    Material mat = bp.getPalette().getOrDefault(id, Material.AIR);
                    // Skip air blocks
                    if (mat == Material.AIR) continue;
                    Location loc = origin.clone().add(x, y, z);
                    blocks.add(new BlockPlacement(loc, mat));
                }
            }
        }
        new BukkitRunnable() {
            int index = 0;
            @Override
            public void run() {
                int count = 0;
                while (index < blocks.size() && count < maxPerTick) {
                    BlockPlacement bpSet = blocks.get(index++);
                    Block block = bpSet.location.getBlock();
                    block.setType(bpSet.material, false);
                    count++;
                }
                if (index >= blocks.size()) {
                    cancel();
                    if (onComplete != null) onComplete.run();
                }
            }
        }.runTaskTimer(plugin, 1L, 1L);
    }

    private static class BlockPlacement {
        final Location location;
        final Material material;
        BlockPlacement(Location loc, Material mat) {
            this.location = loc;
            this.material = mat;
        }
    }

    /**
     * Remove a dungeon by name and clean up its isolated world.
     */
    public void removeDungeon(String name, CommandSender sender) {
        DungeonInstance inst = dungeons.remove(name);
        if (inst != null) {
            // Teleport any players out of the dungeon to main world spawn
            Location mainSpawn = plugin.getServer().getWorlds().get(0).getSpawnLocation();
            for (UUID playerId : inst.getPlayers()) {
                Player player = plugin.getServer().getPlayer(playerId);
                if (player != null) {
                    player.teleport(mainSpawn);
                    player.sendMessage(ChatColor.YELLOW + "The dungeon \"" + inst.getDisplayName() + "\" has been removed.");
                }
            }

            // Delete the isolated world
            plugin.getWorldManager().deleteDungeonWorld(name).thenAccept(success -> {
                if (success) {
                    sender.sendMessage(ChatColor.GREEN + "Removed dungeon \"" + inst.getDisplayName() + "\" and cleaned up its world.");
                    plugin.getLogger().info("Successfully cleaned up world for dungeon: " + name);
                } else {
                    sender.sendMessage(ChatColor.YELLOW + "Removed dungeon \"" + inst.getDisplayName() + "\" but failed to clean up world.");
                    plugin.getLogger().warning("Failed to clean up world for dungeon: " + name);
                }
            });
        } else {
            sender.sendMessage(ChatColor.RED + "Dungeon " + name + " not found.");
        }
    }

    public void purgeDungeons() {
        dungeons.clear();
    }

    /**
     * Teleport a player to a dungeon's origin.  Adds them to the active player set.
     */
    public void tpToDungeon(String name, Player player) {
        DungeonInstance inst = dungeons.get(name);
        if (inst == null) {
            player.sendMessage(ChatColor.RED + "Dungeon not found: " + name);
            return;
        }
        player.teleport(inst.getOrigin().clone().add(0.5, 1, 0.5));
        inst.addPlayer(player);
        player.sendMessage(ChatColor.AQUA + "Teleported to dungeon " + name);
    }



    public List<DungeonInstance> getActiveDungeons() {
        return new ArrayList<>(dungeons.values());
    }

    public DungeonInstance getDungeonByPlayer(Player player) {
        for (DungeonInstance inst : dungeons.values()) {
            if (inst.containsPlayer(player)) return inst;
        }
        return null;
    }

    public DungeonPreset loadPreset(String name) {
        return presets.get(name);
    }

    /**
     * Generate a full dungeon structure based on a preset.
     */
    private void generateDungeonFromPreset(DungeonInstance instance, String presetName, Location origin, Runnable onComplete) {
        DungeonPreset preset = presets.get(presetName);
        if (preset == null) {
            plugin.getLogger().warning("Preset not found: " + presetName + ", using default generation");
            // Fall back to single room generation
            Blueprint startBlueprint = blueprints.get("starter_room");
            if (startBlueprint != null) {
                placeBlueprintGradually(startBlueprint, origin, onComplete);
            } else {
                onComplete.run();
            }
            return;
        }

        // Generate multiple rooms based on preset
        generateMultiRoomDungeon(instance, preset, origin, onComplete);
    }

    /**
     * Generate a multi-room dungeon based on preset configuration.
     */
    private void generateMultiRoomDungeon(DungeonInstance instance, DungeonPreset preset, Location origin, Runnable onComplete) {
        List<String> availableRooms = new ArrayList<>();

        // Add rooms based on themes
        for (String theme : preset.getThemes()) {
            switch (theme.toLowerCase()) {
                case "crypt":
                    availableRooms.add("starter_room");
                    availableRooms.add("boss_room");
                    availableRooms.add("corridor");
                    break;
                case "castle":
                    availableRooms.add("castle_entrance");
                    availableRooms.add("boss_room");
                    availableRooms.add("corridor");
                    break;
                case "cave":
                    availableRooms.add("starter_room");
                    availableRooms.add("corridor");
                    break;
                case "temple":
                    availableRooms.add("ancient_temple");
                    availableRooms.add("boss_room");
                    break;
                default:
                    availableRooms.add("starter_room");
                    break;
            }
        }

        if (availableRooms.isEmpty()) {
            availableRooms.add("starter_room");
        }

        // Generate room layout
        List<RoomPlacement> roomPlacements = generateRoomLayout(preset, origin, availableRooms);

        // Place rooms sequentially
        placeRoomsSequentially(roomPlacements, 0, onComplete);
    }

    /**
     * Generate layout for rooms based on preset.
     */
    private List<RoomPlacement> generateRoomLayout(DungeonPreset preset, Location origin, List<String> availableRooms) {
        List<RoomPlacement> placements = new ArrayList<>();
        Random random = new Random();

        int maxRooms = preset.getMaxRooms(); // Remove artificial limit for MASSIVE dungeons

        // Always start with starter room
        placements.add(new RoomPlacement("starter_room", origin.clone()));

        // Generate additional rooms with enhanced layout for MASSIVE dungeons
        int gridSize = (int) Math.ceil(Math.sqrt(maxRooms)); // Dynamic grid size
        int roomSpacing = 50; // Increased spacing for larger rooms

        for (int i = 1; i < maxRooms; i++) {
            String roomType = availableRooms.get(random.nextInt(availableRooms.size()));

            // Enhanced layout algorithm for massive dungeons
            int gridX = i % gridSize;
            int gridZ = i / gridSize;

            // Add some randomization to prevent perfect grid
            int offsetX = random.nextInt(20) - 10; // ±10 block variation
            int offsetZ = random.nextInt(20) - 10;
            int offsetY = random.nextInt(10) - 5; // Vertical variation

            int x = gridX * roomSpacing + offsetX;
            int z = gridZ * roomSpacing + offsetZ;
            int y = offsetY;

            Location roomLocation = origin.clone().add(x, y, z);
            placements.add(new RoomPlacement(roomType, roomLocation));
        }

        // Ensure we have multiple boss rooms for massive dungeons
        if (maxRooms > 1) {
            RoomPlacement lastRoom = placements.get(placements.size() - 1);
            lastRoom.roomType = "boss_room";

            // Add additional boss rooms for very large dungeons
            if (maxRooms > 20) {
                int midPoint = maxRooms / 2;
                if (midPoint < placements.size()) {
                    placements.get(midPoint).roomType = "boss_room";
                }
            }

            // Add mini-boss rooms for massive dungeons
            if (maxRooms > 30) {
                for (int i = 10; i < maxRooms; i += 15) {
                    if (i < placements.size()) {
                        placements.get(i).roomType = "boss_room";
                    }
                }
            }
        }

        return placements;
    }

    /**
     * Place rooms sequentially to avoid lag.
     */
    private void placeRoomsSequentially(List<RoomPlacement> placements, int index, Runnable onComplete) {
        if (index >= placements.size()) {
            onComplete.run();
            return;
        }

        RoomPlacement placement = placements.get(index);
        Blueprint blueprint = blueprints.get(placement.roomType);

        if (blueprint != null) {
            placeBlueprintGradually(blueprint, placement.location, () -> {
                // Adaptive delay based on dungeon size - shorter delays for massive dungeons
                long delay = Math.max(2L, 10L - (placements.size() / 10)); // Faster for larger dungeons

                plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                    placeRoomsSequentially(placements, index + 1, onComplete);
                }, delay);
            });
        } else {
            // Skip this room and continue
            placeRoomsSequentially(placements, index + 1, onComplete);
        }
    }

    /**
     * Helper class for room placement.
     */
    private static class RoomPlacement {
        String roomType;
        Location location;

        RoomPlacement(String roomType, Location location) {
            this.roomType = roomType;
            this.location = location;
        }
    }

    /**
     * Place a room blueprint at the player's targeted location.  This uses ray
     * tracing to find a nearby block the player is looking at and places the
     * room on top of it.  If no block is targeted the room is placed at the
     * player's current location.
     */
    public void placeRoomAt(Player player, String roomName) {
        Blueprint bp = blueprints.get(roomName);
        if (bp == null) {
            player.sendMessage(ChatColor.RED + "Room not found: " + roomName);
            return;
        }
        Location target = player.getTargetBlockExact(5) != null ? player.getTargetBlockExact(5).getLocation().add(0, 1, 0) : player.getLocation().add(0, 1, 0);
        player.sendMessage(ChatColor.GREEN + "Placing room " + roomName + "...");
        placeBlueprintGradually(bp, target, () -> player.sendMessage(ChatColor.GREEN + "Room placed!"));
    }

    /**
     * List all blueprint names.
     */
    public List<String> listRoomNames() {
        return new ArrayList<>(blueprints.keySet());
    }

    /**
     * Validate dungeon name format.
     */
    public boolean isValidDungeonName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        String trimmed = name.trim();
        if (trimmed.length() < 3 || trimmed.length() > 32) {
            return false;
        }
        // Allow alphanumeric, spaces, hyphens, and underscores
        return trimmed.matches("^[a-zA-Z0-9 _-]+$");
    }

    /**
     * Generate internal name from display name.
     */
    public String generateInternalName(String displayName) {
        String base = displayName.toLowerCase()
            .replaceAll("[^a-z0-9_-]", "_")
            .replaceAll("_{2,}", "_")
            .replaceAll("^_+|_+$", "");

        if (base.isEmpty()) {
            base = "dungeon";
        }

        // Ensure uniqueness
        String candidate = base;
        int counter = 1;
        while (dungeons.containsKey(candidate)) {
            candidate = base + "_" + counter;
            counter++;
        }
        return candidate;
    }

    /**
     * Get all dungeons.
     */
    public Map<String, DungeonInstance> getDungeons() {
        return new HashMap<>(dungeons);
    }

    /**
     * Get a specific dungeon by name.
     */
    public DungeonInstance getDungeon(String name) {
        return dungeons.get(name);
    }

    /**
     * Get all dungeon names.
     */
    public List<String> listDungeonNames() {
        return new ArrayList<>(dungeons.keySet());
    }

    /**
     * Add a dungeon instance to the manager.
     */
    public void addDungeon(DungeonInstance dungeon) {
        dungeons.put(dungeon.getName(), dungeon);
        plugin.getLogger().info("Added dungeon: " + dungeon.getName());
    }

    /**
     * Find a safe spawn location in the world.
     */
    private Location findSafeSpawnLocation(World world) {
        // Ensure spawn chunk is loaded
        world.getChunkAt(0, 0);

        // For our custom superflat worlds: Y=63 is grass surface, spawn at Y=64
        Location spawn = new Location(world, 0, 64, 0);

        // Ensure the spawn location has solid ground at Y=63
        Location ground = new Location(world, 0, 63, 0);
        if (!ground.getBlock().getType().isSolid()) {
            ground.getBlock().setType(Material.GRASS_BLOCK);
        }

        // Ensure air above spawn location
        Location above = spawn.clone().add(0, 0, 0); // Y=64
        if (!above.getBlock().getType().isAir()) {
            above.getBlock().setType(Material.AIR);
        }

        Location above2 = spawn.clone().add(0, 1, 0); // Y=65
        if (!above2.getBlock().getType().isAir()) {
            above2.getBlock().setType(Material.AIR);
        }

        return spawn.add(0.5, 0, 0.5); // Center of block at Y=64
    }

    /**
     * Prepare a safe spawn area around the given location.
     */
    private void prepareSafeSpawnArea(Location center) {
        World world = center.getWorld();
        int centerX = center.getBlockX();
        int centerZ = center.getBlockZ();
        int spawnY = 64; // Standard superflat spawn height

        // Create a 5x5 flat spawn platform
        for (int x = centerX - 2; x <= centerX + 2; x++) {
            for (int z = centerZ - 2; z <= centerZ + 2; z++) {
                // Set surface block to grass
                Location surface = new Location(world, x, spawnY, z);
                surface.getBlock().setType(Material.GRASS_BLOCK);

                // Clear air above (2 blocks high for player)
                Location air1 = new Location(world, x, spawnY + 1, z);
                Location air2 = new Location(world, x, spawnY + 2, z);
                air1.getBlock().setType(Material.AIR);
                air2.getBlock().setType(Material.AIR);

                // Set blocks below to dirt/stone for stability
                Location dirt = new Location(world, x, spawnY - 1, z);
                dirt.getBlock().setType(Material.DIRT);
                Location stone = new Location(world, x, spawnY - 2, z);
                stone.getBlock().setType(Material.STONE);
            }
        }

        plugin.getLogger().info("Prepared safe spawn area at " + centerX + ", " + spawnY + ", " + centerZ);
    }

    /**
     * Set the spawn location for a dungeon instance.
     */
    private void setDungeonSpawn(DungeonInstance instance, Location spawn) {
        World world = instance.getWorld();

        // Set world spawn to the dungeon spawn location
        world.setSpawnLocation(spawn.getBlockX(), spawn.getBlockY(), spawn.getBlockZ());

        // Store spawn location in dungeon config if needed
        plugin.getLogger().info("Set spawn location for dungeon " + instance.getName() +
                               " at " + spawn.getBlockX() + ", " + spawn.getBlockY() + ", " + spawn.getBlockZ());
    }

    /**
     * Shutdown manager and cleanup if necessary.
     */
    public void shutdown() {
        dungeons.clear();
    }
}