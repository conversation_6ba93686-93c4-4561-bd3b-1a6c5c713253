package com.apexdungeons.chests;

import org.bukkit.Location;

/**
 * Represents a chest spawn point with loot table configuration.
 */
public class ChestSpawnPoint {
    private final Location location;
    private final String lootTable;
    private final double radius;
    
    public ChestSpawnPoint(Location location, String lootTable, double radius) {
        this.location = location.clone();
        this.lootTable = lootTable;
        this.radius = radius;
    }
    
    public Location getLocation() {
        return location.clone();
    }
    
    public String getLootTable() {
        return lootTable;
    }
    
    public double getRadius() {
        return radius;
    }
    
    @Override
    public String toString() {
        return String.format("ChestSpawnPoint{lootTable=%s, location=%s, radius=%.1f}", 
            lootTable, location, radius);
    }
}
