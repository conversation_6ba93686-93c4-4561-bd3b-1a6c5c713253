package org.bukkit.block;

import org.bukkit.Material;

/**
 * Stub for Block.  Only stores and sets the type.  Does not modify the world.
 */
public class Block {
    private Material type = Material.AIR;
    private final org.bukkit.World world = new org.bukkit.World();
    private final org.bukkit.Location location = new org.bukkit.Location(world, 0, 0, 0);
    public void setType(Material type, boolean applyPhysics) { this.type = type; }
    public void setType(Material type) { this.type = type; }
    public Material getType() { return type; }

    /**
     * Returns the location of this block.  Stub implementation returns
     * a new Location object anchored to (0,0,0) in a stub world.
     * @return the block's location
     */
    public org.bukkit.Location getLocation() { return location.clone(); }
}