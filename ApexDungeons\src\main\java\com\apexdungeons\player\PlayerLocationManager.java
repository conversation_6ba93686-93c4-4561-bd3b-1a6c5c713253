package com.apexdungeons.player;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages player locations for dungeon entry/exit functionality.
 * Tracks where players were before entering dungeons so they can return.
 */
public class PlayerLocationManager {
    private final ApexDungeons plugin;
    private final Map<UUID, Location> playerReturnLocations = new HashMap<>();
    private final Map<UUID, String> playerCurrentDungeon = new HashMap<>();

    public PlayerLocationManager(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    /**
     * Store a player's current location before they enter a dungeon.
     */
    public void storePlayerLocation(Player player, String dungeonName) {
        UUID playerId = player.getUniqueId();
        Location currentLocation = player.getLocation().clone();
        
        playerReturnLocations.put(playerId, currentLocation);
        playerCurrentDungeon.put(playerId, dungeonName);
        
        plugin.getLogger().info("Stored return location for player " + player.getName() + 
            " entering dungeon " + dungeonName + ": " + locationToString(currentLocation));
    }

    /**
     * Get the stored return location for a player.
     */
    public Location getPlayerReturnLocation(Player player) {
        UUID playerId = player.getUniqueId();
        return playerReturnLocations.get(playerId);
    }

    /**
     * Get the dungeon a player is currently in.
     */
    public String getPlayerCurrentDungeon(Player player) {
        UUID playerId = player.getUniqueId();
        return playerCurrentDungeon.get(playerId);
    }

    /**
     * Remove a player's stored location data (when they leave a dungeon).
     */
    public void clearPlayerLocation(Player player) {
        UUID playerId = player.getUniqueId();
        Location returnLoc = playerReturnLocations.remove(playerId);
        String dungeonName = playerCurrentDungeon.remove(playerId);
        
        if (returnLoc != null) {
            plugin.getLogger().info("Cleared return location for player " + player.getName() + 
                " leaving dungeon " + dungeonName);
        }
    }

    /**
     * Check if a player has a stored return location.
     */
    public boolean hasStoredLocation(Player player) {
        UUID playerId = player.getUniqueId();
        return playerReturnLocations.containsKey(playerId);
    }

    /**
     * Check if a player is currently tracked as being in a dungeon.
     */
    public boolean isPlayerInDungeon(Player player) {
        UUID playerId = player.getUniqueId();
        return playerCurrentDungeon.containsKey(playerId);
    }

    /**
     * Update a player's current dungeon (for dungeon transfers).
     */
    public void updatePlayerDungeon(Player player, String newDungeonName) {
        UUID playerId = player.getUniqueId();
        if (playerCurrentDungeon.containsKey(playerId)) {
            playerCurrentDungeon.put(playerId, newDungeonName);
            plugin.getLogger().info("Updated player " + player.getName() + 
                " current dungeon to: " + newDungeonName);
        }
    }

    /**
     * Get all players currently in dungeons.
     */
    public Map<UUID, String> getAllPlayersInDungeons() {
        return new HashMap<>(playerCurrentDungeon);
    }

    /**
     * Clean up data for offline players.
     */
    public void cleanupOfflinePlayers() {
        playerReturnLocations.entrySet().removeIf(entry -> 
            plugin.getServer().getPlayer(entry.getKey()) == null);
        playerCurrentDungeon.entrySet().removeIf(entry -> 
            plugin.getServer().getPlayer(entry.getKey()) == null);
    }

    /**
     * Shutdown and cleanup all stored data.
     */
    public void shutdown() {
        plugin.getLogger().info("Cleaning up player location data for " + 
            playerReturnLocations.size() + " players...");
        playerReturnLocations.clear();
        playerCurrentDungeon.clear();
    }

    /**
     * Convert location to readable string for logging.
     */
    private String locationToString(Location loc) {
        return String.format("%s %.1f,%.1f,%.1f", 
            loc.getWorld().getName(), loc.getX(), loc.getY(), loc.getZ());
    }
}
