package com.apexdungeons.schematics;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Manages schematic file loading and placement for dungeon generation.
 * Supports basic schematic formats and provides integration with dungeon systems.
 */
public class SchematicManager {
    private final ApexDungeons plugin;
    private final File schematicsFolder;
    private final Map<String, SchematicData> loadedSchematics = new HashMap<>();
    private final Random random = new Random();

    public SchematicManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.schematicsFolder = new File(plugin.getDataFolder(), "schematics");
        
        // Create schematics folder if it doesn't exist
        if (!schematicsFolder.exists()) {
            schematicsFolder.mkdirs();
            plugin.getLogger().info("Created schematics folder: " + schematicsFolder.getPath());
        }
        
        // Load all schematics on startup
        loadAllSchematics();
    }

    /**
     * Load all schematic files from the schematics folder.
     * Supports .schem, .schematic, and .nbt formats.
     */
    public void loadAllSchematics() {
        loadedSchematics.clear();
        plugin.getLogger().info("Loading schematics from: " + schematicsFolder.getAbsolutePath());

        File[] files = schematicsFolder.listFiles((dir, name) -> {
            String lowerName = name.toLowerCase();
            return lowerName.endsWith(".schem") ||
                   lowerName.endsWith(".schematic") ||
                   lowerName.endsWith(".nbt");
        });

        if (files == null || files.length == 0) {
            plugin.getLogger().info("No schematic files found in " + schematicsFolder.getPath());
            plugin.getLogger().info("Supported formats: .schem, .schematic, .nbt");
            plugin.getLogger().info("Creating example schematics. Place your schematic files in the schematics folder to use them.");
            createExampleSchematics();
            return;
        }

        plugin.getLogger().info("Found " + files.length + " schematic files, loading...");
        int loaded = 0;
        int failed = 0;

        // Sort files alphabetically for consistent loading order
        Arrays.sort(files, (a, b) -> a.getName().compareToIgnoreCase(b.getName()));

        for (File file : files) {
            try {
                plugin.getLogger().info("Loading: " + file.getName() + " (" + formatFileSize(file.length()) + ")");
                SchematicData schematic = loadSchematicFile(file);
                if (schematic != null) {
                    String name = file.getName().replaceFirst("[.][^.]+$", ""); // Remove extension
                    loadedSchematics.put(name, schematic);
                    loaded++;
                    plugin.getLogger().info("✓ Loaded: " + name + " [" +
                        schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getDepth() + "]");
                } else {
                    failed++;
                    plugin.getLogger().warning("✗ Failed to load: " + file.getName() + " (returned null - unsupported format?)");
                }
            } catch (Exception e) {
                plugin.getLogger().warning("✗ Failed to load: " + file.getName() + " - " + e.getMessage());
                plugin.getLogger().info("  → Creating fallback schematic for " + file.getName());

                // Create a fallback schematic so the file still appears in the list
                try {
                    String name = file.getName().replaceFirst("[.][^.]+$", "");
                    SchematicData fallback = createFallbackSchematic(file);
                    if (fallback != null) {
                        loadedSchematics.put(name, fallback);
                        loaded++;
                        plugin.getLogger().info("✓ Created fallback for: " + name);
                    } else {
                        failed++;
                    }
                } catch (Exception e2) {
                    failed++;
                    plugin.getLogger().warning("Failed to create fallback for " + file.getName() + ": " + e2.getMessage());
                }
            }
        }

        plugin.getLogger().info("Schematic loading complete: " + loaded + " loaded, " + failed + " failed");
        if (loaded > 0) {
            plugin.getLogger().info("Available schematics: " + String.join(", ", loadedSchematics.keySet()));
        }

        // If no schematics loaded successfully, create examples as fallback
        if (loaded == 0) {
            plugin.getLogger().info("No schematics loaded successfully, creating example schematics as fallback...");
            createExampleSchematics();
        }
    }

    /**
     * Reload all schematics from the folder (public method for GUI refresh).
     */
    public void loadSchematics() {
        loadAllSchematics();
    }

    /**
     * Format file size for human-readable display.
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
    }

    /**
     * Load a specific schematic file using real file parsing.
     */
    private SchematicData loadSchematicFile(File file) throws IOException {
        plugin.getLogger().info("Loading schematic file: " + file.getName());

        // Check file size first
        if (file.length() < 100) {
            throw new IOException("File too small to be a valid schematic (" + file.length() + " bytes)");
        }

        try {
            // Try to parse the actual schematic file
            SchematicData schematic = NBTSchematicReader.readSchematic(file);
            if (schematic != null) {
                plugin.getLogger().info("  → Successfully parsed " + file.getName() +
                    " (Size: " + schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getDepth() + ")");
                return schematic;
            } else {
                throw new IOException("NBT reader returned null");
            }

        } catch (Exception e) {
            plugin.getLogger().warning("  → Failed to parse " + file.getName() + ": " + e.getMessage());

            // Re-throw the exception so it can be caught by the caller
            // The caller will handle creating fallback schematics
            throw new IOException("Failed to parse schematic file: " + e.getMessage(), e);
        }
    }

    /**
     * Create fallback schematic when file parsing fails.
     */
    private SchematicData createFallbackSchematic(File file) {
        String name = file.getName().toLowerCase();
        String baseName = file.getName().replaceFirst("[.][^.]+$", "");

        plugin.getLogger().info("Creating fallback schematic for: " + file.getName());

        // Create structures based on filename patterns as fallback
        if (name.contains("treasure") || name.contains("loot")) {
            plugin.getLogger().info("  → Fallback: treasure room");
            return createTreasureRoom(baseName);
        } else if (name.contains("corridor") || name.contains("hallway") || name.contains("tunnel")) {
            plugin.getLogger().info("  → Fallback: corridor");
            return createSimpleCorridor(baseName);
        } else if (name.contains("chamber") || name.contains("hall") || name.contains("large")) {
            plugin.getLogger().info("  → Fallback: large chamber");
            return createSimpleChamber(baseName);
        } else if (name.contains("small")) {
            plugin.getLogger().info("  → Fallback: small room");
            return createSmallRoom(baseName);
        } else if (name.contains("boss") || name.contains("arena")) {
            plugin.getLogger().info("  → Fallback: boss arena");
            return createBossArena(baseName);
        } else if (name.contains("spawn") || name.contains("start")) {
            plugin.getLogger().info("  → Fallback: spawn room");
            return createSpawnRoom(baseName);
        } else {
            // Default to basic room
            plugin.getLogger().info("  → Fallback: basic room");
            return createSimpleRoom(baseName);
        }
    }

    /**
     * Create example schematic files for demonstration.
     */
    private void createExampleSchematics() {
        plugin.getLogger().info("Creating example schematics...");
        
        // Create some basic schematic data structures
        loadedSchematics.put("basic_room", createSimpleRoom("basic_room"));
        loadedSchematics.put("treasure_room", createTreasureRoom("treasure_room"));
        loadedSchematics.put("corridor", createSimpleCorridor("corridor"));
        loadedSchematics.put("large_chamber", createSimpleChamber("large_chamber"));
        
        plugin.getLogger().info("Created " + loadedSchematics.size() + " example schematics");
    }

    /**
     * Create a simple room schematic.
     */
    private SchematicData createSimpleRoom(String name) {
        int width = 7, height = 4, depth = 7;
        Material[][][] blocks = new Material[height][depth][width];
        
        // Fill with air
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    blocks[y][z][x] = Material.AIR;
                }
            }
        }
        
        // Create walls and floor
        for (int z = 0; z < depth; z++) {
            for (int x = 0; x < width; x++) {
                // Floor
                blocks[0][z][x] = Material.STONE_BRICKS;
                // Ceiling
                blocks[height-1][z][x] = Material.STONE_BRICKS;
                
                // Walls
                if (x == 0 || x == width-1 || z == 0 || z == depth-1) {
                    for (int y = 1; y < height-1; y++) {
                        blocks[y][z][x] = Material.STONE_BRICKS;
                    }
                }
            }
        }
        
        // Add doorway
        blocks[1][0][width/2] = Material.AIR;
        blocks[2][0][width/2] = Material.AIR;
        
        return new SchematicData(name, width, height, depth, blocks);
    }

    /**
     * Create a treasure room schematic.
     */
    private SchematicData createTreasureRoom(String name) {
        SchematicData room = createSimpleRoom(name);
        Material[][][] blocks = room.getBlocks();
        
        // Add treasure chest in center
        int centerX = room.getWidth() / 2;
        int centerZ = room.getDepth() / 2;
        blocks[1][centerZ][centerX] = Material.CHEST;
        
        // Add some decorative blocks
        blocks[1][centerZ-1][centerX-1] = Material.GOLD_BLOCK;
        blocks[1][centerZ-1][centerX+1] = Material.GOLD_BLOCK;
        blocks[1][centerZ+1][centerX-1] = Material.GOLD_BLOCK;
        blocks[1][centerZ+1][centerX+1] = Material.GOLD_BLOCK;
        
        return new SchematicData(name, room.getWidth(), room.getHeight(), room.getDepth(), blocks);
    }

    /**
     * Create a simple corridor schematic.
     */
    private SchematicData createSimpleCorridor(String name) {
        int width = 3, height = 4, depth = 10;
        Material[][][] blocks = new Material[height][depth][width];
        
        // Fill with air
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    blocks[y][z][x] = Material.AIR;
                }
            }
        }
        
        // Create corridor walls, floor, and ceiling
        for (int z = 0; z < depth; z++) {
            for (int x = 0; x < width; x++) {
                // Floor
                blocks[0][z][x] = Material.STONE_BRICKS;
                // Ceiling
                blocks[height-1][z][x] = Material.STONE_BRICKS;
                
                // Side walls
                if (x == 0 || x == width-1) {
                    for (int y = 1; y < height-1; y++) {
                        blocks[y][z][x] = Material.STONE_BRICKS;
                    }
                }
            }
        }
        
        return new SchematicData(name, width, height, depth, blocks);
    }

    /**
     * Create a large chamber schematic.
     */
    private SchematicData createSimpleChamber(String name) {
        int width = 11, height = 6, depth = 11;
        Material[][][] blocks = new Material[height][depth][width];
        
        // Fill with air
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    blocks[y][z][x] = Material.AIR;
                }
            }
        }
        
        // Create chamber structure
        for (int z = 0; z < depth; z++) {
            for (int x = 0; x < width; x++) {
                // Floor
                blocks[0][z][x] = Material.STONE_BRICKS;
                // Ceiling
                blocks[height-1][z][x] = Material.STONE_BRICKS;
                
                // Walls
                if (x == 0 || x == width-1 || z == 0 || z == depth-1) {
                    for (int y = 1; y < height-1; y++) {
                        blocks[y][z][x] = Material.STONE_BRICKS;
                    }
                }
            }
        }
        
        // Add pillars
        int pillarX1 = width / 3;
        int pillarX2 = 2 * width / 3;
        int pillarZ1 = depth / 3;
        int pillarZ2 = 2 * depth / 3;
        
        for (int y = 1; y < height-1; y++) {
            blocks[y][pillarZ1][pillarX1] = Material.STONE_BRICK_STAIRS;
            blocks[y][pillarZ1][pillarX2] = Material.STONE_BRICK_STAIRS;
            blocks[y][pillarZ2][pillarX1] = Material.STONE_BRICK_STAIRS;
            blocks[y][pillarZ2][pillarX2] = Material.STONE_BRICK_STAIRS;
        }
        
        return new SchematicData(name, width, height, depth, blocks);
    }

    /**
     * Create a small room schematic.
     */
    private SchematicData createSmallRoom(String name) {
        int width = 5, height = 3, depth = 5;
        Material[][][] blocks = new Material[height][depth][width];

        // Fill with air
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    blocks[y][z][x] = Material.AIR;
                }
            }
        }

        // Create walls and floor
        for (int z = 0; z < depth; z++) {
            for (int x = 0; x < width; x++) {
                // Floor
                blocks[0][z][x] = Material.COBBLESTONE;
                // Ceiling
                blocks[height-1][z][x] = Material.COBBLESTONE;

                // Walls
                if (x == 0 || x == width-1 || z == 0 || z == depth-1) {
                    for (int y = 1; y < height-1; y++) {
                        blocks[y][z][x] = Material.COBBLESTONE;
                    }
                }
            }
        }

        // Add doorway
        blocks[1][0][width/2] = Material.AIR;

        return new SchematicData(name, width, height, depth, blocks);
    }

    /**
     * Create a boss arena schematic.
     */
    private SchematicData createBossArena(String name) {
        int width = 15, height = 8, depth = 15;
        Material[][][] blocks = new Material[height][depth][width];

        // Fill with air
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    blocks[y][z][x] = Material.AIR;
                }
            }
        }

        // Create arena structure
        for (int z = 0; z < depth; z++) {
            for (int x = 0; x < width; x++) {
                // Floor
                blocks[0][z][x] = Material.BLACKSTONE;
                // Ceiling
                blocks[height-1][z][x] = Material.BLACKSTONE;

                // Walls
                if (x == 0 || x == width-1 || z == 0 || z == depth-1) {
                    for (int y = 1; y < height-1; y++) {
                        blocks[y][z][x] = Material.POLISHED_BLACKSTONE_BRICKS;
                    }
                }
            }
        }

        // Add central platform
        int centerX = width / 2;
        int centerZ = depth / 2;
        for (int x = centerX - 2; x <= centerX + 2; x++) {
            for (int z = centerZ - 2; z <= centerZ + 2; z++) {
                blocks[1][z][x] = Material.OBSIDIAN;
            }
        }

        // Add entrance
        blocks[1][0][centerX] = Material.AIR;
        blocks[2][0][centerX] = Material.AIR;
        blocks[3][0][centerX] = Material.AIR;

        return new SchematicData(name, width, height, depth, blocks);
    }

    /**
     * Create a spawn room schematic.
     */
    private SchematicData createSpawnRoom(String name) {
        int width = 9, height = 5, depth = 9;
        Material[][][] blocks = new Material[height][depth][width];

        // Fill with air
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    blocks[y][z][x] = Material.AIR;
                }
            }
        }

        // Create spawn room structure
        for (int z = 0; z < depth; z++) {
            for (int x = 0; x < width; x++) {
                // Floor
                blocks[0][z][x] = Material.QUARTZ_BLOCK;
                // Ceiling
                blocks[height-1][z][x] = Material.QUARTZ_BLOCK;

                // Walls
                if (x == 0 || x == width-1 || z == 0 || z == depth-1) {
                    for (int y = 1; y < height-1; y++) {
                        blocks[y][z][x] = Material.QUARTZ_BRICKS;
                    }
                }
            }
        }

        // Add spawn platform in center
        int centerX = width / 2;
        int centerZ = depth / 2;
        blocks[1][centerZ][centerX] = Material.BEACON;

        // Add multiple exits
        blocks[1][0][centerX] = Material.AIR; // North
        blocks[2][0][centerX] = Material.AIR;
        blocks[1][depth-1][centerX] = Material.AIR; // South
        blocks[2][depth-1][centerX] = Material.AIR;
        blocks[1][centerZ][0] = Material.AIR; // West
        blocks[2][centerZ][0] = Material.AIR;
        blocks[1][centerZ][width-1] = Material.AIR; // East
        blocks[2][centerZ][width-1] = Material.AIR;

        return new SchematicData(name, width, height, depth, blocks);
    }

    /**
     * Place a schematic at the specified location.
     */
    public CompletableFuture<Boolean> placeSchematic(String schematicName, Location location) {
        SchematicData schematic = loadedSchematics.get(schematicName);
        if (schematic == null) {
            plugin.getLogger().warning("Schematic not found: " + schematicName);
            return CompletableFuture.completedFuture(false);
        }
        
        return placeSchematicGradually(schematic, location);
    }

    /**
     * Place a random schematic from the loaded schematics.
     */
    public CompletableFuture<Boolean> placeRandomSchematic(Location location) {
        if (loadedSchematics.isEmpty()) {
            plugin.getLogger().warning("No schematics available for random placement");
            return CompletableFuture.completedFuture(false);
        }
        
        List<String> schematicNames = new ArrayList<>(loadedSchematics.keySet());
        String randomName = schematicNames.get(random.nextInt(schematicNames.size()));
        
        plugin.getLogger().info("Placing random schematic: " + randomName + " at " + 
            location.getWorld().getName() + " " + location.getBlockX() + "," + 
            location.getBlockY() + "," + location.getBlockZ());
        
        return placeSchematic(randomName, location);
    }

    /**
     * Place schematic blocks gradually to avoid lag.
     */
    private CompletableFuture<Boolean> placeSchematicGradually(SchematicData schematic, Location location) {
        CompletableFuture<Boolean> future = new CompletableFuture<>();
        
        World world = location.getWorld();
        if (world == null) {
            future.complete(false);
            return future;
        }
        
        int maxBlocksPerTick = plugin.getConfig().getInt("performance.blocksPerTick", 100);
        Material[][][] blocks = schematic.getBlocks();
        
        List<BlockPlacement> placements = new ArrayList<>();
        
        // Prepare all block placements
        for (int y = 0; y < schematic.getHeight(); y++) {
            for (int z = 0; z < schematic.getDepth(); z++) {
                for (int x = 0; x < schematic.getWidth(); x++) {
                    Material material = blocks[y][z][x];
                    if (material != Material.AIR) {
                        Location blockLoc = location.clone().add(x, y, z);
                        placements.add(new BlockPlacement(blockLoc, material));
                    }
                }
            }
        }
        
        // Place blocks gradually
        new BukkitRunnable() {
            int index = 0;
            
            @Override
            public void run() {
                int placed = 0;
                while (index < placements.size() && placed < maxBlocksPerTick) {
                    BlockPlacement placement = placements.get(index++);
                    Block block = placement.location.getBlock();
                    block.setType(placement.material, false);
                    placed++;
                }
                
                if (index >= placements.size()) {
                    cancel();
                    future.complete(true);
                }
            }
        }.runTaskTimer(plugin, 1L, 1L);
        
        return future;
    }

    /**
     * Get all loaded schematic names.
     */
    public Set<String> getLoadedSchematicNames() {
        return new HashSet<>(loadedSchematics.keySet());
    }

    /**
     * Get a specific schematic.
     */
    public SchematicData getSchematic(String name) {
        return loadedSchematics.get(name);
    }



    /**
     * Reload all schematics.
     */
    public void reloadSchematics() {
        plugin.getLogger().info("Reloading schematics...");
        loadAllSchematics();
    }

    /**
     * Shutdown and cleanup.
     */
    public void shutdown() {
        loadedSchematics.clear();
        plugin.getLogger().info("SchematicManager shutdown complete.");
    }

    /**
     * Helper class for block placement.
     */
    private static class BlockPlacement {
        final Location location;
        final Material material;
        
        BlockPlacement(Location location, Material material) {
            this.location = location;
            this.material = material;
        }
    }
}
