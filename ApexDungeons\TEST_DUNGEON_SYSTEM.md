# Comprehensive Dungeon System Test Plan

## Pre-Test Setup

1. **Install the Plugin**
   - Copy `target/ApexDungeons-0.1.0.jar` to your server's `plugins/` folder
   - Start/restart your Minecraft server
   - Verify plugin loads without errors in console

2. **Check Permissions**
   - Ensure you have OP permissions or the following permissions:
     - `apexdungeons.create`
     - `apexdungeons.teleport`
     - `apexdungeons.admin`

## Test Sequence

### Test 1: Test World Creation (Basic Functionality)
```
/dgn testworld
```

**Expected Results:**
- ✅ "Creating test superflat world..." message appears
- ✅ "This may take a moment..." message appears
- ✅ World creation succeeds OR detailed error message appears
- ✅ If successful: Immediate teleportation to test world
- ✅ Success message shows actual world name (e.g., "dungeon_test_123456789")

**Console Logs to Check:**
- World creation method attempts
- Success/failure of each method
- Final world name and configuration

### Test 2: Test World Teleportation
```
/dgn tp test
```

**Expected Results:**
- ✅ Successful teleportation to test world
- ✅ OR clear error message if test world doesn't exist
- ✅ Spawn location at Y=64 (flat surface)

### Test 3: Regular Dungeon Creation
```
/dgn create MyTestDungeon
```

**Expected Results:**
- ✅ "Creating dungeon world: MyTestDungeon" message
- ✅ "This may take a moment..." message
- ✅ World creation succeeds OR detailed error with troubleshooting hints
- ✅ If successful: "Dungeon 'MyTestDungeon' created successfully!" message
- ✅ Instructions to use `/dgn tp MyTestDungeon`
- ✅ World name shown in success message

**Console Logs to Check:**
- Dungeon creation process
- World mapping verification
- Success confirmation with world name

### Test 4: Regular Dungeon Teleportation
```
/dgn tp MyTestDungeon
```

**Expected Results:**
- ✅ Successful teleportation to dungeon world
- ✅ OR detailed error message with debug information
- ✅ If successful: "Teleported to dungeon 'MyTestDungeon'!" message

### Test 5: Error Handling Test
```
/dgn tp NonExistentDungeon
```

**Expected Results:**
- ✅ "Dungeon 'NonExistentDungeon' not found." message
- ✅ "Use /dgn list to see available dungeons." message
- ✅ Console shows available dungeons and world mappings

### Test 6: List Available Dungeons
```
/dgn list
```

**Expected Results:**
- ✅ Shows created dungeons (MyTestDungeon, test if created)
- ✅ OR "No dungeons available" if none exist

## Debugging Information

### Console Logs to Monitor

**During World Creation:**
- "Creating superflat dungeon world: [worldname]"
- "Attempting world creation with [method]..."
- "World created successfully with [method]"
- OR "All world creation methods failed: [error]"

**During Dungeon Registration:**
- "Successfully created dungeon: [name] by [player] in world: [worldname]"
- "Registered dungeon world mapping: [dungeon] -> [world]"

**During Teleportation:**
- "Successfully teleported [player] to dungeon: [dungeon]"
- OR "Teleport failed for dungeon: [dungeon]"

### Troubleshooting Failed Tests

**If World Creation Fails:**
1. Check server permissions for world creation
2. Verify disk space availability
3. Check Bukkit/Spigot version compatibility
4. Look for plugin conflicts in console
5. Check server configuration files

**If Teleportation Fails:**
1. Verify dungeon exists in `/dgn list`
2. Check console for world mapping information
3. Verify world is loaded in server
4. Check for world corruption

## Success Criteria

✅ **All tests pass**: System is working correctly
⚠️ **Some tests fail**: Check console logs and troubleshoot
❌ **Most tests fail**: Review server configuration and plugin compatibility

## Expected Improvements

With the fixes implemented, you should see:

1. **Clear Success/Failure Messages**: No more false positives
2. **Detailed Error Information**: Helpful troubleshooting hints
3. **Robust World Creation**: Multiple fallback methods
4. **Reliable Teleportation**: Proper world verification
5. **Comprehensive Logging**: Detailed console information

## Post-Test Verification

After successful tests:
1. Check server world folder for created world directories
2. Verify world files are not corrupted
3. Test leaving dungeons with `/dgn leave`
4. Test building tools in created dungeons

## Report Results

Please run through this test sequence and report:
- Which tests passed/failed
- Any error messages received
- Console log excerpts for failed tests
- Server version and plugin versions

This will help identify any remaining issues and ensure the dungeon system is fully functional.
