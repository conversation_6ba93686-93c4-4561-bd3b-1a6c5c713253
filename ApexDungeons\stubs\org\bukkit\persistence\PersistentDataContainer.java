package org.bukkit.persistence;

import org.bukkit.NamespacedKey;

import java.util.HashMap;
import java.util.Map;

/**
 * Stub for PersistentDataContainer.  Stores arbitrary key/value pairs but
 * provides no persistence beyond runtime.  Only the minimal API required by
 * ApexDungeons is implemented.
 */
public class PersistentDataContainer {
    private final Map<NamespacedKey, Object> data = new HashMap<>();
    public <T, Z> void set(NamespacedKey key, PersistentDataType<T, Z> type, Z value) {
        data.put(key, value);
    }
    public <T, Z> boolean has(NamespacedKey key, PersistentDataType<T, Z> type) {
        return data.containsKey(key);
    }
    public <T, Z> Z get(Namespaced<PERSON>ey key, PersistentDataType<T, Z> type) {
        return (Z) data.get(key);
    }
}