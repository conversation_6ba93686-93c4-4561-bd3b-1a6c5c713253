package org.bukkit.event.player;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.event.Event;

/**
 * Stub for PlayerInteractEvent.  Contains information about a player's
 * interaction such as the item in hand and the action type.
 */
public class PlayerInteractEvent extends Event {
    private final Player player;
    private final Action action;
    private final ItemStack item;
    private final org.bukkit.inventory.EquipmentSlot hand;
    public PlayerInteractEvent(Player player, Action action, ItemStack item, org.bukkit.inventory.EquipmentSlot hand) {
        this.player = player;
        this.action = action;
        this.item = item;
        this.hand = hand;
    }
    public Player getPlayer() { return player; }
    public Action getAction() { return action; }
    public ItemStack getItem() { return item; }
    public org.bukkit.inventory.EquipmentSlot getHand() { return hand; }
    public void setCancelled(boolean cancel) {}
}

/**
 * Stub for action enumeration.
 */