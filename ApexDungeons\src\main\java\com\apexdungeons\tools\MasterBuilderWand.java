package com.apexdungeons.tools;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.SchematicSelectionGUI;
import com.apexdungeons.schematics.SchematicData;
import com.apexdungeons.schematics.SchematicPreview;
import org.bukkit.*;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;

import java.util.*;

/**
 * Master Builder Wand - The ultimate unified building tool for Soaps Dungeons.
 * Provides schematic selection, 3D preview, and placement functionality.
 */
public class MasterBuilderWand implements Listener {
    private final ApexDungeons plugin;
    private final NamespacedKey wandKey;
    private final Map<UUID, String> selectedSchematics = new HashMap<>();
    private final Map<UUID, SchematicPreview> activePreviews = new HashMap<>();
    
    public MasterBuilderWand(ApexDungeons plugin) {
        this.plugin = plugin;
        this.wandKey = new NamespacedKey(plugin, "master_builder_wand");
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * Create the Master Builder Wand item.
     */
    public ItemStack createMasterBuilderWand() {
        ItemStack wand = new ItemStack(Material.GOLDEN_SHOVEL);
        ItemMeta meta = wand.getItemMeta();
        
        meta.setDisplayName(ChatColor.GOLD + "⚡ Master Builder Wand");
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "The ultimate unified building tool!");
        lore.add("");
        lore.add(ChatColor.GREEN + "✨ Features:");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "All schematics from folder");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Favorites system");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "3D wireframe preview");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "WASD movement controls");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Rotation & confirmation");
        lore.add("");
        lore.add(ChatColor.YELLOW + "🎯 Right-click: Open schematic selection");
        lore.add(ChatColor.YELLOW + "🎯 Left-click: Place selected schematic");
        lore.add("");
        lore.add(ChatColor.GREEN + "Made by Vexy");
        
        meta.setLore(lore);
        meta.getPersistentDataContainer().set(wandKey, PersistentDataType.STRING, "master_builder_wand");
        wand.setItemMeta(meta);
        
        return wand;
    }
    
    /**
     * Check if an item is a Master Builder Wand.
     */
    public boolean isMasterBuilderWand(ItemStack item) {
        if (item == null || !item.hasItemMeta()) return false;
        return item.getItemMeta().getPersistentDataContainer().has(wandKey, PersistentDataType.STRING);
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        
        if (!isMasterBuilderWand(item)) return;
        
        event.setCancelled(true);
        
        if (event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            // Open schematic selection GUI
            openSchematicSelection(player);
        } else if (event.getAction() == Action.LEFT_CLICK_BLOCK) {
            // Place selected schematic
            Block clickedBlock = event.getClickedBlock();
            if (clickedBlock != null) {
                placeSelectedSchematic(player, clickedBlock.getLocation().add(0, 1, 0));
            }
        }
    }
    
    /**
     * Open the schematic selection GUI.
     */
    private void openSchematicSelection(Player player) {
        SchematicSelectionGUI.open(player, plugin);
    }
    
    /**
     * Set the selected schematic for a player.
     */
    public void setSelectedSchematic(Player player, String schematicName) {
        selectedSchematics.put(player.getUniqueId(), schematicName);
        
        // Update wand lore to show selected schematic
        ItemStack wand = player.getInventory().getItemInMainHand();
        if (isMasterBuilderWand(wand)) {
            updateWandLore(wand, schematicName);
        }
        
        player.sendMessage(ChatColor.GREEN + "✓ Selected schematic: " + ChatColor.AQUA + schematicName);
        player.sendMessage(ChatColor.GRAY + "Left-click on a block to place it!");
    }
    
    /**
     * Update the wand's lore to show the selected schematic.
     */
    private void updateWandLore(ItemStack wand, String schematicName) {
        ItemMeta meta = wand.getItemMeta();
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "The ultimate unified building tool!");
        lore.add("");
        lore.add(ChatColor.GREEN + "✨ Currently Selected:");
        lore.add(ChatColor.AQUA + "📐 " + ChatColor.WHITE + schematicName);
        lore.add("");
        lore.add(ChatColor.GREEN + "✨ Features:");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "All schematics from folder");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Favorites system");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "3D wireframe preview");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "WASD movement controls");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Rotation & confirmation");
        lore.add("");
        lore.add(ChatColor.YELLOW + "🎯 Right-click: Change schematic");
        lore.add(ChatColor.YELLOW + "🎯 Left-click: Place " + schematicName);
        lore.add("");
        lore.add(ChatColor.GREEN + "Made by Vexy");
        
        meta.setLore(lore);
        wand.setItemMeta(meta);
    }
    
    /**
     * Place the selected schematic at the target location.
     */
    private void placeSelectedSchematic(Player player, Location targetLocation) {
        String schematicName = selectedSchematics.get(player.getUniqueId());
        if (schematicName == null) {
            player.sendMessage(ChatColor.RED + "No schematic selected! Right-click to choose one.");
            return;
        }
        
        // Get schematic data
        SchematicData schematic = plugin.getSchematicManager().getSchematic(schematicName);
        if (schematic == null) {
            player.sendMessage(ChatColor.RED + "Schematic not found: " + schematicName);
            return;
        }
        
        // Start 3D preview mode
        startPreviewMode(player, schematic, targetLocation);
    }
    
    /**
     * Start 3D preview mode for schematic placement.
     */
    private void startPreviewMode(Player player, SchematicData schematic, Location location) {
        // Cancel any existing preview
        cancelPreview(player);
        
        // Create new preview
        SchematicPreview preview = new SchematicPreview(plugin, player, schematic, location);
        activePreviews.put(player.getUniqueId(), preview);
        
        // Start the preview
        preview.startPreview();
        
        player.sendMessage(ChatColor.GREEN + "✨ 3D Preview Mode Activated!");
        player.sendMessage(ChatColor.YELLOW + "🎮 Controls:");
        player.sendMessage(ChatColor.AQUA + "• WASD " + ChatColor.WHITE + "- Move schematic");
        player.sendMessage(ChatColor.AQUA + "• R " + ChatColor.WHITE + "- Rotate 90°");
        player.sendMessage(ChatColor.AQUA + "• Enter " + ChatColor.WHITE + "- Confirm placement");
        player.sendMessage(ChatColor.AQUA + "• Esc " + ChatColor.WHITE + "- Cancel");
    }
    
    /**
     * Cancel active preview for a player.
     */
    public void cancelPreview(Player player) {
        SchematicPreview preview = activePreviews.remove(player.getUniqueId());
        if (preview != null) {
            preview.stopPreview();
        }
    }
    
    /**
     * Get the selected schematic for a player.
     */
    public String getSelectedSchematic(Player player) {
        return selectedSchematics.get(player.getUniqueId());
    }
    
    /**
     * Check if a player has an active preview.
     */
    public boolean hasActivePreview(Player player) {
        return activePreviews.containsKey(player.getUniqueId());
    }
    
    /**
     * Get the active preview for a player.
     */
    public SchematicPreview getActivePreview(Player player) {
        return activePreviews.get(player.getUniqueId());
    }
}
