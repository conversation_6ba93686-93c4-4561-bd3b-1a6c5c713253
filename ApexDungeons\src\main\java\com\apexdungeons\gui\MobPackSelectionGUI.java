package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.mobs.DungeonMobConfig;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;

/**
 * GUI for selecting pre-configured dungeon mobs from mob packs.
 * Organized by categories with detailed mob information.
 */
public class MobPackSelectionGUI {
    private static final String GUI_NAME = ChatColor.DARK_RED + "Dungeon Mob Selection " + ChatColor.GRAY + "- Made by Vexy";
    private static String currentCategory = "all";
    private static int currentPage = 0;
    
    public static void open(Player player, ApexDungeons plugin) {
        open(player, plugin, "all", 0);
    }
    
    public static void open(Player player, ApexDungeons plugin, String category, int page) {
        currentCategory = category;
        currentPage = page;
        
        Inventory inv = Bukkit.createInventory(player, 54, GUI_NAME);
        
        // Fill background
        fillBackground(inv);
        
        // Create category buttons
        createCategoryButtons(inv, plugin, category);
        
        // Populate mobs
        populateMobs(inv, plugin, player, category, page);
        
        // Add utility buttons
        addUtilityButtons(inv, plugin, player, category, page);
        
        player.openInventory(inv);
        
        // Register event listener
        registerEventListener(plugin);
    }
    
    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.RED_STAINED_GLASS_PANE);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);
        
        // Fill border slots
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
    }
    
    /**
     * Create category filter buttons.
     */
    private static void createCategoryButtons(Inventory inv, ApexDungeons plugin, String selectedCategory) {
        // All mobs button
        ItemStack allButton = new ItemStack(selectedCategory.equals("all") ? Material.LIME_STAINED_GLASS_PANE : Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta allMeta = allButton.getItemMeta();
        allMeta.setDisplayName(ChatColor.WHITE + "All Mobs");
        List<String> allLore = new ArrayList<>();
        allLore.add(ChatColor.GRAY + "Show all available mobs");
        allLore.add(ChatColor.YELLOW + "Total: " + ChatColor.WHITE + plugin.getDungeonMobManager().getLoadedMobs().size() + " mobs");
        allMeta.setLore(allLore);
        allButton.setItemMeta(allMeta);
        inv.setItem(1, allButton);
        
        // Category buttons
        List<String> categories = plugin.getDungeonMobManager().getCategories();
        int[] categorySlots = {2, 3, 4, 5, 6, 7};
        
        for (int i = 0; i < Math.min(categories.size(), categorySlots.length); i++) {
            String category = categories.get(i);
            boolean isSelected = category.equals(selectedCategory);
            
            ItemStack categoryButton = new ItemStack(isSelected ? Material.LIME_STAINED_GLASS_PANE : Material.GRAY_STAINED_GLASS_PANE);
            ItemMeta categoryMeta = categoryButton.getItemMeta();
            categoryMeta.setDisplayName(ChatColor.WHITE + formatCategoryName(category));
            
            List<String> categoryLore = new ArrayList<>();
            categoryLore.add(ChatColor.GRAY + "Filter by " + category + " mobs");
            int mobCount = plugin.getDungeonMobManager().getMobsInCategory(category).size();
            categoryLore.add(ChatColor.YELLOW + "Count: " + ChatColor.WHITE + mobCount + " mobs");
            categoryMeta.setLore(categoryLore);
            categoryButton.setItemMeta(categoryMeta);
            inv.setItem(categorySlots[i], categoryButton);
        }
    }
    
    /**
     * Populate the GUI with mobs based on category filter.
     */
    private static void populateMobs(Inventory inv, ApexDungeons plugin, Player player, String category, int page) {
        List<DungeonMobConfig> mobs;
        
        if (category.equals("all")) {
            mobs = new ArrayList<>(plugin.getDungeonMobManager().getLoadedMobs().values());
        } else {
            mobs = plugin.getDungeonMobManager().getMobsInCategory(category);
        }
        
        // Sort mobs: bosses first, then by difficulty, then alphabetically
        mobs.sort((a, b) -> {
            if (a.isBoss() && !b.isBoss()) return -1;
            if (!a.isBoss() && b.isBoss()) return 1;
            
            int diffCompare = getDifficultyOrder(a.getDifficulty()) - getDifficultyOrder(b.getDifficulty());
            if (diffCompare != 0) return diffCompare;
            
            return a.getDisplayName().compareToIgnoreCase(b.getDisplayName());
        });
        
        // Available slots for mobs (excluding border and category slots)
        int[] mobSlots = {
            9, 10, 11, 12, 13, 14, 15, 16, 17,
            18, 19, 20, 21, 22, 23, 24, 25, 26,
            27, 28, 29, 30, 31, 32, 33, 34, 35,
            36, 37, 38, 39, 40, 41, 42, 43, 44
        };
        
        int startIndex = page * mobSlots.length;
        for (int i = 0; i < mobSlots.length && (startIndex + i) < mobs.size(); i++) {
            DungeonMobConfig mob = mobs.get(startIndex + i);
            ItemStack mobItem = createMobItem(mob);
            inv.setItem(mobSlots[i], mobItem);
        }
    }
    
    /**
     * Create an item representing a mob configuration.
     */
    private static ItemStack createMobItem(DungeonMobConfig mob) {
        ItemStack item = new ItemStack(mob.getIconMaterial());
        ItemMeta meta = item.getItemMeta();
        
        String displayName = mob.getDifficultyColor() + mob.getDisplayName();
        if (mob.isBoss()) {
            displayName = ChatColor.DARK_RED + "👑 " + displayName;
        }
        meta.setDisplayName(displayName);
        
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + mob.getDescription());
        lore.add("");
        lore.add(ChatColor.YELLOW + "Category: " + mob.getCategoryColor() + formatCategoryName(mob.getCategory()));
        lore.add(ChatColor.YELLOW + "Difficulty: " + mob.getDifficultyColor() + formatDifficultyName(mob.getDifficulty()));
        lore.add(ChatColor.YELLOW + "Mob Type: " + ChatColor.WHITE + mob.getMobType());
        lore.add("");
        lore.add(ChatColor.AQUA + "⚙ Spawn Settings:");
        lore.add(ChatColor.GRAY + "• Radius: " + ChatColor.WHITE + mob.getSpawnRadius() + " blocks");
        lore.add(ChatColor.GRAY + "• Cooldown: " + ChatColor.WHITE + mob.getCooldownMin() + "-" + mob.getCooldownMax() + "s");
        lore.add(ChatColor.GRAY + "• Max Concurrent: " + ChatColor.WHITE + mob.getMaxConcurrent());
        
        if (!mob.getBuilderInfo().isEmpty()) {
            lore.add("");
            lore.add(ChatColor.GOLD + "📋 Builder Notes:");
            for (String info : mob.getBuilderInfo()) {
                lore.add(ChatColor.GRAY + "• " + ChatColor.WHITE + info);
            }
        }
        
        lore.add("");
        lore.add(ChatColor.GREEN + "▶ Click to select this mob!");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }
    
    /**
     * Add utility buttons for navigation and features.
     */
    private static void addUtilityButtons(Inventory inv, ApexDungeons plugin, Player player, String category, int page) {
        // Refresh button
        ItemStack refresh = new ItemStack(Material.EMERALD);
        ItemMeta refreshMeta = refresh.getItemMeta();
        refreshMeta.setDisplayName(ChatColor.GREEN + "🔄 Refresh Mob Configs");
        List<String> refreshLore = new ArrayList<>();
        refreshLore.add(ChatColor.GRAY + "Reload all mob configurations");
        refreshLore.add(ChatColor.YELLOW + "Total: " + ChatColor.WHITE + plugin.getDungeonMobManager().getLoadedMobs().size() + " mobs");
        refreshMeta.setLore(refreshLore);
        refresh.setItemMeta(refreshMeta);
        inv.setItem(45, refresh);
        
        // Help button
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(ChatColor.AQUA + "❓ Mob Spawner Help");
        List<String> helpLore = new ArrayList<>();
        helpLore.add(ChatColor.YELLOW + "How to use:");
        helpLore.add(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Select a mob from this GUI");
        helpLore.add(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Click where you want to place spawn point");
        helpLore.add(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Mob will spawn automatically for players");
        helpLore.add("");
        helpLore.add(ChatColor.YELLOW + "Builder Mode:");
        helpLore.add(ChatColor.GRAY + "• You see spawn point indicators");
        helpLore.add(ChatColor.GRAY + "• Players in gameplay mode don't see them");
        helpLore.add("");
        helpLore.add(ChatColor.GREEN + "Made by Vexy");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(53, help);
        
        // Back to Building Tools button
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(ChatColor.YELLOW + "← Back to Building Tools");
        back.setItemMeta(backMeta);
        inv.setItem(46, back);
        
        // Navigation arrows if needed
        List<DungeonMobConfig> allMobs = category.equals("all") ? 
            new ArrayList<>(plugin.getDungeonMobManager().getLoadedMobs().values()) :
            plugin.getDungeonMobManager().getMobsInCategory(category);
        int totalPages = (int) Math.ceil(allMobs.size() / 36.0);
        
        if (page > 0) {
            ItemStack prev = new ItemStack(Material.ARROW);
            ItemMeta prevMeta = prev.getItemMeta();
            prevMeta.setDisplayName(ChatColor.YELLOW + "← Previous Page");
            prev.setItemMeta(prevMeta);
            inv.setItem(48, prev);
        }
        
        if (page < totalPages - 1) {
            ItemStack next = new ItemStack(Material.ARROW);
            ItemMeta nextMeta = next.getItemMeta();
            nextMeta.setDisplayName(ChatColor.YELLOW + "Next Page →");
            next.setItemMeta(nextMeta);
            inv.setItem(50, next);
        }
        
        // Page indicator
        if (totalPages > 1) {
            ItemStack pageInfo = new ItemStack(Material.PAPER);
            ItemMeta pageMeta = pageInfo.getItemMeta();
            pageMeta.setDisplayName(ChatColor.WHITE + "Page " + (page + 1) + " of " + totalPages);
            pageInfo.setItemMeta(pageMeta);
            inv.setItem(49, pageInfo);
        }
    }
    
    // Helper methods
    private static String formatCategoryName(String category) {
        return category.substring(0, 1).toUpperCase() + category.substring(1).toLowerCase();
    }
    
    private static String formatDifficultyName(String difficulty) {
        return difficulty.substring(0, 1).toUpperCase() + difficulty.substring(1).toLowerCase();
    }
    
    private static int getDifficultyOrder(String difficulty) {
        switch (difficulty.toLowerCase()) {
            case "easy": return 1;
            case "normal": return 2;
            case "hard": return 3;
            case "elite": return 4;
            case "boss": return 5;
            default: return 0;
        }
    }
    
    private static void registerEventListener(ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (!e.getView().getTitle().equals(GUI_NAME) || !(e.getWhoClicked() instanceof Player)) return;
                
                e.setCancelled(true);
                Player player = (Player) e.getWhoClicked();
                int slot = e.getRawSlot();
                ItemStack clicked = e.getCurrentItem();
                
                if (clicked == null || !clicked.hasItemMeta()) return;
                
                // Handle category buttons
                if (slot >= 1 && slot <= 7) {
                    String newCategory = "all";
                    if (slot == 1) {
                        newCategory = "all";
                    } else {
                        List<String> categories = plugin.getDungeonMobManager().getCategories();
                        int categoryIndex = slot - 2;
                        if (categoryIndex < categories.size()) {
                            newCategory = categories.get(categoryIndex);
                        }
                    }
                    open(player, plugin, newCategory, 0);
                    return;
                }
                
                // Handle mob selection
                if (slot >= 9 && slot <= 44) {
                    String mobName = extractMobName(clicked.getItemMeta().getDisplayName());
                    DungeonMobConfig selectedMob = findMobByDisplayName(plugin, mobName);
                    
                    if (selectedMob != null) {
                        player.closeInventory();
                        // TODO: Integrate with mob spawn tool to place spawn point
                        player.sendMessage(ChatColor.GREEN + "✓ Selected mob: " + ChatColor.AQUA + selectedMob.getDisplayName());
                        player.sendMessage(ChatColor.YELLOW + "Right-click where you want to place the spawn point!");
                        // Store selected mob for placement
                        // This will be integrated with the existing mob spawn system
                    }
                    return;
                }
                
                // Handle utility buttons
                switch (slot) {
                    case 45: // Refresh
                        plugin.getDungeonMobManager().reloadMobConfigs();
                        open(player, plugin, currentCategory, currentPage);
                        player.sendMessage(ChatColor.GREEN + "✓ Mob configurations refreshed!");
                        break;
                    case 46: // Back to Building Tools
                        player.closeInventory();
                        BuildingToolsGUI.open(player, plugin);
                        break;
                    case 48: // Previous page
                        if (currentPage > 0) {
                            open(player, plugin, currentCategory, currentPage - 1);
                        }
                        break;
                    case 50: // Next page
                        open(player, plugin, currentCategory, currentPage + 1);
                        break;
                }
            }
        }, plugin);
    }
    
    private static String extractMobName(String displayName) {
        // Remove color codes and crown prefix
        return ChatColor.stripColor(displayName).replace("👑 ", "");
    }
    
    private static DungeonMobConfig findMobByDisplayName(ApexDungeons plugin, String displayName) {
        return plugin.getDungeonMobManager().getLoadedMobs().values().stream()
            .filter(mob -> mob.getDisplayName().equals(displayName))
            .findFirst()
            .orElse(null);
    }
}
