package com.apexdungeons.blocks;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import org.bukkit.*;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;

import java.util.*;

/**
 * Manages dungeon start and end blocks that players can interact with.
 * Start blocks begin dungeon challenges, end blocks trigger completion rewards.
 */
public class DungeonBlockManager implements Listener {
    private final ApexDungeons plugin;
    private final NamespacedKey startBlockKey;
    private final NamespacedKey endBlockKey;
    private final NamespacedKey dungeonNameKey;
    
    // Track active dungeon sessions
    private final Map<UUID, DungeonSession> activeSessions = new HashMap<>();
    
    public DungeonBlockManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.startBlockKey = new NamespacedKey(plugin, "dungeon_start_block");
        this.endBlockKey = new NamespacedKey(plugin, "dungeon_end_block");
        this.dungeonNameKey = new NamespacedKey(plugin, "dungeon_name");
        
        // Register event listener
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Create a start block item that can be placed in dungeons.
     */
    public ItemStack createStartBlockItem() {
        ItemStack item = new ItemStack(Material.EMERALD_BLOCK);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(ChatColor.GREEN + "Dungeon Start Block");
            meta.setLore(Arrays.asList(
                ChatColor.GRAY + "Place this block in a dungeon to create",
                ChatColor.GRAY + "a start point for dungeon challenges.",
                ChatColor.YELLOW + "Right-click to begin the challenge!"
            ));
            meta.getPersistentDataContainer().set(startBlockKey, PersistentDataType.BYTE, (byte) 1);
            item.setItemMeta(meta);
        }
        return item;
    }

    /**
     * Create an end block item that can be placed in dungeons.
     */
    public ItemStack createEndBlockItem() {
        ItemStack item = new ItemStack(Material.DIAMOND_BLOCK);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(ChatColor.AQUA + "Dungeon End Block");
            meta.setLore(Arrays.asList(
                ChatColor.GRAY + "Place this block in a dungeon to create",
                ChatColor.GRAY + "a completion point for dungeon challenges.",
                ChatColor.YELLOW + "Right-click to complete the challenge!"
            ));
            meta.getPersistentDataContainer().set(endBlockKey, PersistentDataType.BYTE, (byte) 1);
            item.setItemMeta(meta);
        }
        return item;
    }

    /**
     * Mark a placed block as a start block.
     */
    public void markAsStartBlock(Block block, String dungeonName) {
        block.setType(Material.EMERALD_BLOCK);
        // Store metadata in chunk persistent data or use a separate storage system
        Location loc = block.getLocation();
        plugin.getLogger().info("Marked block at " + locationToString(loc) + " as start block for dungeon: " + dungeonName);
    }

    /**
     * Mark a placed block as an end block.
     */
    public void markAsEndBlock(Block block, String dungeonName) {
        block.setType(Material.DIAMOND_BLOCK);
        // Store metadata in chunk persistent data or use a separate storage system
        Location loc = block.getLocation();
        plugin.getLogger().info("Marked block at " + locationToString(loc) + " as end block for dungeon: " + dungeonName);
    }

    /**
     * Handle player interactions with dungeon blocks.
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }

        Block block = event.getClickedBlock();
        if (block == null) {
            return;
        }

        Player player = event.getPlayer();
        
        // Check if player is in a dungeon world
        if (!plugin.getWorldManager().isDungeonWorld(player.getWorld())) {
            return;
        }

        // Handle start block interaction
        if (block.getType() == Material.EMERALD_BLOCK) {
            handleStartBlockInteraction(player, block);
            event.setCancelled(true);
        }
        // Handle end block interaction
        else if (block.getType() == Material.DIAMOND_BLOCK) {
            handleEndBlockInteraction(player, block);
            event.setCancelled(true);
        }
    }

    /**
     * Handle block break events to implement world protection.
     */
    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Block block = event.getBlock();
        Player player = event.getPlayer();

        // Check if player is in a dungeon world
        if (!plugin.getWorldManager().isDungeonWorld(player.getWorld())) {
            return;
        }

        // Get dungeon instance for this world
        String dungeonName = plugin.getWorldManager().getDungeonNameFromWorld(player.getWorld());
        if (dungeonName == null) {
            return;
        }

        // Check if player has permission to build in this dungeon
        if (!canPlayerBuild(player, dungeonName)) {
            event.setCancelled(true);
            player.sendMessage(ChatColor.RED + "You cannot break blocks in this dungeon!");
            player.sendMessage(ChatColor.GRAY + "This dungeon is in playable mode. Only the owner can edit it.");
            return;
        }

        // Check if this is a dungeon start or end block
        if (block.getType() == Material.EMERALD_BLOCK || block.getType() == Material.DIAMOND_BLOCK) {
            // Prevent the block from dropping items
            event.setDropItems(false);

            // Clear any active session if they break a start/end block
            if (hasActiveSession(player)) {
                clearSession(player);
                player.sendMessage(ChatColor.YELLOW + "Your dungeon challenge has been cancelled due to block removal.");
            }

            // Notify player
            String blockType = block.getType() == Material.EMERALD_BLOCK ? "Start Block" : "End Block";
            player.sendMessage(ChatColor.GRAY + "Dungeon " + blockType + " removed (no items dropped).");

            plugin.getLogger().info("Player " + player.getName() + " broke a dungeon " + blockType.toLowerCase() +
                " at " + locationToString(block.getLocation()));
        }
    }

    /**
     * Handle block place events to implement world protection.
     */
    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();

        // Check if player is in a dungeon world
        if (!plugin.getWorldManager().isDungeonWorld(player.getWorld())) {
            return;
        }

        // Get dungeon instance for this world
        String dungeonName = plugin.getWorldManager().getDungeonNameFromWorld(player.getWorld());
        if (dungeonName == null) {
            return;
        }

        // Check if player has permission to build in this dungeon
        if (!canPlayerBuild(player, dungeonName)) {
            event.setCancelled(true);
            player.sendMessage(ChatColor.RED + "You cannot place blocks in this dungeon!");
            player.sendMessage(ChatColor.GRAY + "This dungeon is in playable mode. Only the owner can edit it.");
        }
    }

    /**
     * Handle interaction with a start block.
     */
    private void handleStartBlockInteraction(Player player, Block block) {
        UUID playerId = player.getUniqueId();
        
        // Check if player already has an active session
        if (activeSessions.containsKey(playerId)) {
            player.sendMessage(ChatColor.YELLOW + "You already have an active dungeon challenge!");
            return;
        }

        // Get dungeon name from world
        String dungeonName = plugin.getWorldManager().getDungeonNameFromWorld(player.getWorld());
        if (dungeonName == null) {
            player.sendMessage(ChatColor.RED + "Could not determine dungeon name!");
            return;
        }

        // Start dungeon session
        DungeonSession session = new DungeonSession(dungeonName, System.currentTimeMillis());
        activeSessions.put(playerId, session);

        // Effects and notifications
        Location loc = block.getLocation();
        player.playSound(loc, Sound.BLOCK_BEACON_ACTIVATE, 1.0f, 1.2f);
        loc.getWorld().spawnParticle(Particle.ENCHANT, loc.add(0.5, 1, 0.5), 30, 0.5, 0.5, 0.5, 0.1);
        
        player.sendMessage(ChatColor.GREEN + "✦ Dungeon Challenge Started!");
        player.sendMessage(ChatColor.GRAY + "Find and activate the " + ChatColor.AQUA + "End Block" + ChatColor.GRAY + " to complete the challenge.");
        
        plugin.getLogger().info("Player " + player.getName() + " started dungeon challenge in: " + dungeonName);
    }

    /**
     * Handle interaction with an end block.
     */
    private void handleEndBlockInteraction(Player player, Block block) {
        UUID playerId = player.getUniqueId();
        
        // Check if player has an active session
        DungeonSession session = activeSessions.get(playerId);
        if (session == null) {
            player.sendMessage(ChatColor.YELLOW + "You must activate a " + ChatColor.GREEN + "Start Block" + ChatColor.YELLOW + " first!");
            return;
        }

        // Calculate completion time
        long completionTime = System.currentTimeMillis() - session.getStartTime();
        long seconds = completionTime / 1000;
        long minutes = seconds / 60;
        seconds = seconds % 60;

        // Remove active session
        activeSessions.remove(playerId);

        // Effects and notifications
        Location loc = block.getLocation();
        player.playSound(loc, Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f);
        player.playSound(loc, Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.2f);
        loc.getWorld().spawnParticle(Particle.FIREWORK, loc.add(0.5, 1, 0.5), 50, 1.0, 1.0, 1.0, 0.1);
        loc.getWorld().spawnParticle(Particle.END_ROD, loc, 20, 0.5, 0.5, 0.5, 0.1);

        player.sendMessage(ChatColor.GOLD + "✦ " + ChatColor.GREEN + "Dungeon Challenge Completed!");
        player.sendMessage(ChatColor.GRAY + "Time: " + ChatColor.YELLOW + String.format("%d:%02d", minutes, seconds));

        // Give completion rewards
        giveCompletionRewards(player, session);

        // Handle custom exit location
        handleDungeonExit(player, session.getDungeonName());

        plugin.getLogger().info("Player " + player.getName() + " completed dungeon challenge in " +
            session.getDungeonName() + " (Time: " + String.format("%d:%02d", minutes, seconds) + ")");
    }

    /**
     * Handle player exit from dungeon with custom exit location support.
     */
    private void handleDungeonExit(Player player, String dungeonName) {
        // Check for custom exit location
        Location customExit = plugin.getDungeonConfig().getCustomExitLocation(dungeonName);

        if (customExit != null) {
            // Use custom exit location
            player.teleport(customExit);
            player.sendMessage(ChatColor.GREEN + "Teleported to custom exit location!");
            plugin.getLogger().info("Player " + player.getName() + " teleported to custom exit for dungeon: " + dungeonName);
        } else {
            // Use original location from PlayerLocationManager
            Location originalLocation = plugin.getPlayerLocationManager().getPlayerReturnLocation(player);
            if (originalLocation != null) {
                player.teleport(originalLocation);
                plugin.getPlayerLocationManager().clearPlayerLocation(player);
                player.sendMessage(ChatColor.GREEN + "Returned to your original location!");
            } else {
                // Fallback to main world spawn
                Location mainSpawn = plugin.getServer().getWorlds().get(0).getSpawnLocation();
                player.teleport(mainSpawn);
                player.sendMessage(ChatColor.YELLOW + "Returned to main world spawn.");
            }
        }
    }

    /**
     * Give rewards to player for completing dungeon challenge.
     */
    private void giveCompletionRewards(Player player, DungeonSession session) {
        // Basic rewards - can be expanded later
        player.giveExp(100);

        // Give some basic items as rewards
        ItemStack reward1 = new ItemStack(Material.DIAMOND, 2);
        ItemStack reward2 = new ItemStack(Material.EMERALD, 5);
        ItemStack reward3 = new ItemStack(Material.GOLDEN_APPLE, 1);

        player.getInventory().addItem(reward1, reward2, reward3);
        player.sendMessage(ChatColor.GREEN + "Rewards: " + ChatColor.YELLOW + "2 Diamonds, 5 Emeralds, 1 Golden Apple, 100 XP");
    }

    /**
     * Get active session for a player.
     */
    public DungeonSession getActiveSession(Player player) {
        return activeSessions.get(player.getUniqueId());
    }

    /**
     * Check if a player has an active dungeon session.
     */
    public boolean hasActiveSession(Player player) {
        return activeSessions.containsKey(player.getUniqueId());
    }

    /**
     * Clear a player's active session.
     */
    public void clearSession(Player player) {
        activeSessions.remove(player.getUniqueId());
    }

    /**
     * Get all active sessions.
     */
    public Map<UUID, DungeonSession> getActiveSessions() {
        return new HashMap<>(activeSessions);
    }

    /**
     * Check if a player can build in a specific dungeon.
     */
    private boolean canPlayerBuild(Player player, String dungeonName) {
        // Admin permission always allows building
        if (player.hasPermission("apexdungeons.admin")) {
            return true;
        }

        // Get the dungeon instance
        var dungeon = plugin.getDungeonManager().getDungeon(dungeonName);
        if (dungeon == null) {
            return false;
        }

        // Check if player is the owner of the dungeon
        if (player.getName().equals(dungeon.getCreator())) {
            return true;
        }

        // Check if dungeon is in building/editing mode
        // For now, we'll consider a dungeon in "building mode" if no players have active sessions
        boolean hasActiveSessions = activeSessions.values().stream()
            .anyMatch(session -> session.getDungeonName().equals(dungeonName));

        // If there are active sessions, only allow the owner to build
        if (hasActiveSessions) {
            return false;
        }

        // If no active sessions and player has build permission, allow building
        return player.hasPermission("apexdungeons.build") || player.getName().equals(dungeon.getCreator());
    }

    /**
     * Shutdown and cleanup.
     */
    public void shutdown() {
        activeSessions.clear();
        plugin.getLogger().info("DungeonBlockManager shutdown complete.");
    }

    /**
     * Convert location to readable string.
     */
    private String locationToString(Location loc) {
        return String.format("%s %.1f,%.1f,%.1f", 
            loc.getWorld().getName(), loc.getX(), loc.getY(), loc.getZ());
    }

    /**
     * Represents an active dungeon session.
     */
    public static class DungeonSession {
        private final String dungeonName;
        private final long startTime;

        public DungeonSession(String dungeonName, long startTime) {
            this.dungeonName = dungeonName;
            this.startTime = startTime;
        }

        public String getDungeonName() {
            return dungeonName;
        }

        public long getStartTime() {
            return startTime;
        }
    }
}
