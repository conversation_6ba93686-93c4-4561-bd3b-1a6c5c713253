package com.apexdungeons.mobs;

import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Configuration data for dungeon mobs loaded from the dungeon_mobs folder.
 * Contains all information needed to spawn and display mobs in the GUI.
 */
public class DungeonMobConfig {
    private final String id;
    private final String displayName;
    private final String description;
    private final String category;
    private final String difficulty;
    private final Material iconMaterial;
    private final String mobType;
    private final int spawnRadius;
    private final int cooldownMin;
    private final int cooldownMax;
    private final int maxConcurrent;
    private final List<String> spawnCommands;
    private final List<String> builderInfo;
    private final boolean isBoss;
    
    public DungeonMobConfig(String id, String displayName, String description, String category, 
                           String difficulty, Material iconMaterial, String mobType, int spawnRadius,
                           int cooldownMin, int cooldownMax, int maxConcurrent, 
                           List<String> spawnCommands, List<String> builderInfo, boolean isBoss) {
        this.id = id;
        this.displayName = displayName;
        this.description = description;
        this.category = category;
        this.difficulty = difficulty;
        this.iconMaterial = iconMaterial;
        this.mobType = mobType;
        this.spawnRadius = spawnRadius;
        this.cooldownMin = cooldownMin;
        this.cooldownMax = cooldownMax;
        this.maxConcurrent = maxConcurrent;
        this.spawnCommands = spawnCommands;
        this.builderInfo = builderInfo;
        this.isBoss = isBoss;
    }
    
    /**
     * Load a mob configuration from a YAML file.
     */
    public static DungeonMobConfig loadFromFile(File file) {
        try {
            YamlConfiguration config = YamlConfiguration.loadConfiguration(file);
            
            String id = file.getName().replace(".yml", "");
            String displayName = config.getString("display_name", id);
            String description = config.getString("description", "A dungeon mob");
            String category = config.getString("category", "basic");
            String difficulty = config.getString("difficulty", "normal");
            
            // Parse icon material
            Material iconMaterial = Material.ZOMBIE_HEAD;
            String iconStr = config.getString("icon", "ZOMBIE_HEAD");
            try {
                iconMaterial = Material.valueOf(iconStr.toUpperCase());
            } catch (IllegalArgumentException e) {
                // Use default if invalid material
            }
            
            String mobType = config.getString("mob_type", "ZOMBIE");
            int spawnRadius = config.getInt("spawn_radius", 6);
            int cooldownMin = config.getInt("cooldown.min", 30);
            int cooldownMax = config.getInt("cooldown.max", 60);
            int maxConcurrent = config.getInt("max_concurrent", 2);
            boolean isBoss = config.getBoolean("is_boss", false);
            
            // Load spawn commands
            List<String> spawnCommands = new ArrayList<>();
            if (config.isList("spawn_commands")) {
                spawnCommands = config.getStringList("spawn_commands");
            } else if (config.isString("spawn_commands")) {
                spawnCommands.add(config.getString("spawn_commands"));
            }
            
            // Load builder info
            List<String> builderInfo = new ArrayList<>();
            if (config.isList("builder_info")) {
                builderInfo = config.getStringList("builder_info");
            } else if (config.isString("builder_info")) {
                builderInfo.add(config.getString("builder_info"));
            }
            
            return new DungeonMobConfig(id, displayName, description, category, difficulty,
                iconMaterial, mobType, spawnRadius, cooldownMin, cooldownMax, maxConcurrent,
                spawnCommands, builderInfo, isBoss);
                
        } catch (Exception e) {
            System.err.println("Failed to load mob config from " + file.getName() + ": " + e.getMessage());
            return null;
        }
    }
    
    // Getters
    public String getId() { return id; }
    public String getDisplayName() { return displayName; }
    public String getDescription() { return description; }
    public String getCategory() { return category; }
    public String getDifficulty() { return difficulty; }
    public Material getIconMaterial() { return iconMaterial; }
    public String getMobType() { return mobType; }
    public int getSpawnRadius() { return spawnRadius; }
    public int getCooldownMin() { return cooldownMin; }
    public int getCooldownMax() { return cooldownMax; }
    public int getMaxConcurrent() { return maxConcurrent; }
    public List<String> getSpawnCommands() { return spawnCommands; }
    public List<String> getBuilderInfo() { return builderInfo; }
    public boolean isBoss() { return isBoss; }
    
    /**
     * Get difficulty color for display.
     */
    public String getDifficultyColor() {
        switch (difficulty.toLowerCase()) {
            case "easy": return "§a";
            case "normal": return "§e";
            case "hard": return "§c";
            case "boss": return "§4";
            case "elite": return "§5";
            default: return "§7";
        }
    }
    
    /**
     * Get category color for display.
     */
    public String getCategoryColor() {
        switch (category.toLowerCase()) {
            case "basic": return "§7";
            case "undead": return "§8";
            case "magical": return "§d";
            case "beast": return "§6";
            case "boss": return "§4";
            case "elite": return "§5";
            default: return "§f";
        }
    }
    
    /**
     * Create a sample configuration file content.
     */
    public static String createSampleConfig() {
        return "# Dungeon Mob Configuration\n" +
               "# This file defines a custom mob for dungeon spawning\n\n" +
               "display_name: \"Dungeon Zombie\"\n" +
               "description: \"A basic undead mob for dungeon encounters\"\n" +
               "category: \"undead\"  # Categories: basic, undead, magical, beast, boss, elite\n" +
               "difficulty: \"normal\"  # Difficulties: easy, normal, hard, boss, elite\n" +
               "icon: \"ZOMBIE_HEAD\"  # Material for GUI display\n\n" +
               "# Mob spawning configuration\n" +
               "mob_type: \"ZOMBIE\"\n" +
               "spawn_radius: 6\n" +
               "cooldown:\n" +
               "  min: 30\n" +
               "  max: 60\n" +
               "max_concurrent: 2\n" +
               "is_boss: false\n\n" +
               "# Commands to execute when spawning (use %x%, %y%, %z% for coordinates)\n" +
               "spawn_commands:\n" +
               "  - \"summon zombie %x% %y% %z% {CustomName:'\\\"Dungeon Zombie\\\"'}\"\n\n" +
               "# Information shown to builders (not players)\n" +
               "builder_info:\n" +
               "  - \"Basic undead mob\"\n" +
               "  - \"Spawns every 30-60 seconds\"\n" +
               "  - \"Max 2 concurrent mobs\"\n";
    }
}
