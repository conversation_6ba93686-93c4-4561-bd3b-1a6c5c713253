# Skeleton Archer Configuration
# This is an example mob configuration for the enhanced mob spawner system

display_name: "Skeleton Archer"
description: "A skilled undead archer with enhanced bow abilities"
category: "undead"  # Categories: basic, undead, magical, beast, boss, elite
difficulty: "normal"  # Difficulties: easy, normal, hard, boss, elite
icon: "SKELETON_SKULL"  # Material for GUI display

# Mob spawning configuration
mob_type: "SKELETON"
spawn_radius: 6
cooldown:
  min: 30
  max: 60
max_concurrent: 2
is_boss: false

# Commands to execute when spawning (use %x%, %y%, %z% for coordinates)
spawn_commands:
  - "summon skeleton %x% %y% %z% {CustomName:'\"Skeleton Archer\"', HandItems:[{id:bow,Count:1,tag:{Enchantments:[{id:power,lvl:2}]}},{}], ArmorItems:[{},{},{},{id:leather_helmet,Count:1}]}"

# Information shown to builders (not players)
builder_info:
  - "Ranged undead attacker"
  - "Enhanced bow with Power II"
  - "Spawns every 30-60 seconds"
  - "Max 2 concurrent archers"
  - "Wears leather helmet for identification"
