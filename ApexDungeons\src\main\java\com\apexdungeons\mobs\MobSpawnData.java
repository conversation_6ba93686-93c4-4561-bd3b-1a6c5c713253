package com.apexdungeons.mobs;

import com.apexdungeons.ApexDungeons;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages player mob spawn selections and settings.
 */
public class MobSpawnData {
    private final ApexDungeons plugin;
    private final Map<UUID, String> playerMobSelections = new HashMap<>();
    private final Map<UUID, String> playerBossSelections = new HashMap<>();
    private final Map<UUID, Double> playerRadiusSettings = new HashMap<>();
    
    public MobSpawnData(ApexDungeons plugin) {
        this.plugin = plugin;
        loadData();
    }
    
    /**
     * Set a player's mob selection.
     */
    public void setPlayerMobSelection(UUID playerId, String mobName) {
        playerMobSelections.put(playerId, mobName);
        saveData();
    }
    
    /**
     * Get a player's mob selection.
     */
    public String getPlayerMobSelection(UUID playerId) {
        return playerMobSelections.get(playerId);
    }
    
    /**
     * Set a player's boss selection.
     */
    public void setPlayerBossSelection(UUID playerId, String bossName) {
        playerBossSelections.put(playerId, bossName);
        saveData();
    }
    
    /**
     * Get a player's boss selection.
     */
    public String getPlayerBossSelection(UUID playerId) {
        return playerBossSelections.get(playerId);
    }
    
    /**
     * Set a player's spawn radius setting.
     */
    public void setPlayerRadius(UUID playerId, double radius) {
        playerRadiusSettings.put(playerId, radius);
        saveData();
    }
    
    /**
     * Get a player's spawn radius setting.
     */
    public double getPlayerRadius(UUID playerId, double defaultRadius) {
        return playerRadiusSettings.getOrDefault(playerId, defaultRadius);
    }
    
    /**
     * Clear a player's mob selection.
     */
    public void clearPlayerMobSelection(UUID playerId) {
        playerMobSelections.remove(playerId);
        saveData();
    }
    
    /**
     * Clear a player's boss selection.
     */
    public void clearPlayerBossSelection(UUID playerId) {
        playerBossSelections.remove(playerId);
        saveData();
    }
    
    /**
     * Load data from file.
     */
    private void loadData() {
        File file = new File(plugin.getDataFolder(), "mob_spawn_data.yml");
        if (!file.exists()) return;
        
        FileConfiguration config = YamlConfiguration.loadConfiguration(file);
        
        // Load mob selections
        if (config.contains("mob_selections")) {
            for (String key : config.getConfigurationSection("mob_selections").getKeys(false)) {
                try {
                    UUID playerId = UUID.fromString(key);
                    String mobName = config.getString("mob_selections." + key);
                    playerMobSelections.put(playerId, mobName);
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to load mob selection for " + key + ": " + e.getMessage());
                }
            }
        }
        
        // Load boss selections
        if (config.contains("boss_selections")) {
            for (String key : config.getConfigurationSection("boss_selections").getKeys(false)) {
                try {
                    UUID playerId = UUID.fromString(key);
                    String bossName = config.getString("boss_selections." + key);
                    playerBossSelections.put(playerId, bossName);
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to load boss selection for " + key + ": " + e.getMessage());
                }
            }
        }
        
        // Load radius settings
        if (config.contains("radius_settings")) {
            for (String key : config.getConfigurationSection("radius_settings").getKeys(false)) {
                try {
                    UUID playerId = UUID.fromString(key);
                    double radius = config.getDouble("radius_settings." + key);
                    playerRadiusSettings.put(playerId, radius);
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to load radius setting for " + key + ": " + e.getMessage());
                }
            }
        }
        
        plugin.getLogger().info("Loaded mob spawn data for " + 
            playerMobSelections.size() + " mob selections, " +
            playerBossSelections.size() + " boss selections, " +
            playerRadiusSettings.size() + " radius settings");
    }
    
    /**
     * Save data to file.
     */
    private void saveData() {
        File file = new File(plugin.getDataFolder(), "mob_spawn_data.yml");
        FileConfiguration config = new YamlConfiguration();
        
        // Save mob selections
        for (Map.Entry<UUID, String> entry : playerMobSelections.entrySet()) {
            config.set("mob_selections." + entry.getKey().toString(), entry.getValue());
        }
        
        // Save boss selections
        for (Map.Entry<UUID, String> entry : playerBossSelections.entrySet()) {
            config.set("boss_selections." + entry.getKey().toString(), entry.getValue());
        }
        
        // Save radius settings
        for (Map.Entry<UUID, Double> entry : playerRadiusSettings.entrySet()) {
            config.set("radius_settings." + entry.getKey().toString(), entry.getValue());
        }
        
        try {
            config.save(file);
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save mob spawn data: " + e.getMessage());
        }
    }
    
    /**
     * Shutdown and save data.
     */
    public void shutdown() {
        saveData();
        playerMobSelections.clear();
        playerBossSelections.clear();
        playerRadiusSettings.clear();
    }
}
