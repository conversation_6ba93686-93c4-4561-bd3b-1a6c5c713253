package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.ArrayList;
import java.util.List;

/**
 * Admin GUI for controlling ongoing dungeons and generation.  This simplified
 * version exposes only purge and spawn boss options.  Additional buttons can
 * be added as needed.
 */
public class AdminGUI {
    private static final String GUI_NAME = ChatColor.DARK_PURPLE + "⚙️ Soaps Dungeons Admin";

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory(player, 54, GUI_NAME);

        // Fill background
        fillBackground(inv);

        // Create admin tool buttons
        createAdminButtons(inv, plugin);

        // Create dungeon tool buttons
        createDungeonToolButtons(inv, plugin);

        // Create navigation buttons
        createNavigationButtons(inv);
        JavaPlugin pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    Player clicker = (Player) e.getWhoClicked();

                    switch (slot) {
                        case 10: // Purge Dungeons
                            plugin.getDungeonManager().purgeDungeons();
                            clicker.closeInventory();
                            clicker.sendMessage(ChatColor.GREEN + "All dungeons purged.");
                            break;
                        case 12: // Spawn Boss
                            DungeonInstance inst = plugin.getDungeonManager().getDungeonByPlayer(clicker);
                            if (inst != null) {
                                inst.spawnBoss();
                                clicker.sendMessage(ChatColor.GREEN + "Boss spawned in dungeon " + inst.getName());
                            } else {
                                clicker.sendMessage(ChatColor.RED + "You are not inside a dungeon.");
                            }
                            break;
                        case 14: // Reload Schematics
                            clicker.sendMessage(ChatColor.YELLOW + "Reloading schematics...");
                            plugin.getSchematicManager().reloadSchematics();
                            int count = plugin.getSchematicManager().getLoadedSchematicNames().size();
                            clicker.sendMessage(ChatColor.GREEN + "Schematics reloaded! " + count + " available.");
                            break;
                        case 16: // Test World
                            clicker.closeInventory();
                            clicker.sendMessage(ChatColor.YELLOW + "Creating test superflat world...");
                            plugin.getWorldManager().createDungeonWorld("test").thenAccept(world -> {
                                if (world != null) {
                                    Location spawnLoc = new Location(world, 0, 64, 0);
                                    clicker.teleport(spawnLoc);
                                    clicker.sendMessage(ChatColor.GREEN + "Test world created! You've been teleported to: " + world.getName());
                                    plugin.getPlayerLocationManager().storePlayerLocation(clicker, "test");
                                } else {
                                    clicker.sendMessage(ChatColor.RED + "Failed to create test world!");
                                }
                            });
                            break;
                        case 20: // Start Block
                        case 22: // End Block
                        case 24: // Room Connector
                        case 31: case 32: case 33: case 34: case 40: // Schematic Tools
                            // Give the item to the player (check for duplicates)
                            ItemStack item = e.getCurrentItem();
                            if (item != null && item.getType() != Material.AIR) {
                                // Check if player already has this tool
                                if (!hasSimilarTool(clicker, item)) {
                                    clicker.getInventory().addItem(item.clone());
                                    clicker.sendMessage(ChatColor.GREEN + "Tool added to your inventory!");
                                } else {
                                    clicker.sendMessage(ChatColor.YELLOW + "You already have this tool!");
                                }
                            }
                            break;
                        case 45: // Back
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            break;
                        case 53: // Close
                            clicker.closeInventory();
                            break;
                    }
                }
            }
        }, pl);
        player.openInventory(inv);
    }

    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);

        // Fill border with background
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
    }

    private static void createAdminButtons(Inventory inv, ApexDungeons plugin) {
        // Purge dungeons button
        ItemStack purge = new ItemStack(Material.BARRIER);
        ItemMeta pm = purge.getItemMeta();
        pm.setDisplayName(ChatColor.RED + "Purge Dungeons");
        List<String> purgeLore = new ArrayList<>();
        purgeLore.add(ChatColor.GRAY + "Remove all active dungeons");
        purgeLore.add(ChatColor.GRAY + "and clean up world files");
        purgeLore.add("");
        purgeLore.add(ChatColor.RED + "⚠ This action cannot be undone!");
        pm.setLore(purgeLore);
        purge.setItemMeta(pm);
        inv.setItem(10, purge);

        // Spawn boss button
        ItemStack boss = new ItemStack(Material.NETHER_STAR);
        ItemMeta bm = boss.getItemMeta();
        bm.setDisplayName(ChatColor.GOLD + "Spawn Boss");
        List<String> bossLore = new ArrayList<>();
        bossLore.add(ChatColor.GRAY + "Spawn the boss in your");
        bossLore.add(ChatColor.GRAY + "current dungeon location");
        bossLore.add("");
        bossLore.add(ChatColor.YELLOW + "Must be inside a dungeon!");
        bm.setLore(bossLore);
        boss.setItemMeta(bm);
        inv.setItem(12, boss);

        // Reload schematics button
        ItemStack reload = new ItemStack(Material.KNOWLEDGE_BOOK);
        ItemMeta rm = reload.getItemMeta();
        rm.setDisplayName(ChatColor.AQUA + "Reload Schematics");
        List<String> reloadLore = new ArrayList<>();
        reloadLore.add(ChatColor.GRAY + "Reload all schematic files");
        reloadLore.add(ChatColor.GRAY + "from the schematics folder");
        reloadLore.add("");
        reloadLore.add(ChatColor.GREEN + "Use after adding new files!");
        rm.setLore(reloadLore);
        reload.setItemMeta(rm);
        inv.setItem(14, reload);

        // Test world button
        ItemStack testWorld = new ItemStack(Material.GRASS_BLOCK);
        ItemMeta tm = testWorld.getItemMeta();
        tm.setDisplayName(ChatColor.GREEN + "Test Superflat World");
        List<String> testLore = new ArrayList<>();
        testLore.add(ChatColor.GRAY + "Create a test superflat world");
        testLore.add(ChatColor.GRAY + "to verify world generation");
        testLore.add("");
        testLore.add(ChatColor.YELLOW + "Teleports you to test world");
        tm.setLore(testLore);
        testWorld.setItemMeta(tm);
        inv.setItem(16, testWorld);
    }

    private static void createDungeonToolButtons(Inventory inv, ApexDungeons plugin) {
        // Dungeon Start Block
        ItemStack startBlock = plugin.getDungeonBlockManager().createStartBlockItem();
        inv.setItem(20, startBlock);

        // Dungeon End Block
        ItemStack endBlock = plugin.getDungeonBlockManager().createEndBlockItem();
        inv.setItem(22, endBlock);

        // Room Connector Tool
        ItemStack connector = plugin.getRoomConnector().createConnectorTool();
        inv.setItem(24, connector);

        // Schematic Tools Section Header
        ItemStack schematicHeader = new ItemStack(Material.WRITTEN_BOOK);
        ItemMeta headerMeta = schematicHeader.getItemMeta();
        headerMeta.setDisplayName(ChatColor.GOLD + "📋 Schematic Tools");
        List<String> headerLore = new ArrayList<>();
        headerLore.add(ChatColor.GRAY + "Available schematic placement tools:");
        headerLore.add("");
        for (String schematicName : plugin.getSchematicManager().getLoadedSchematicNames()) {
            headerLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + schematicName);
        }
        headerLore.add("");
        headerLore.add(ChatColor.YELLOW + "Click individual tools below!");
        headerMeta.setLore(headerLore);
        schematicHeader.setItemMeta(headerMeta);
        inv.setItem(30, schematicHeader);

        // Create schematic tools (first 5 schematics)
        List<String> schematicNames = new ArrayList<>(plugin.getSchematicManager().getLoadedSchematicNames());
        int[] schematicSlots = {31, 32, 33, 34, 40};

        for (int i = 0; i < Math.min(schematicNames.size(), schematicSlots.length); i++) {
            String schematicName = schematicNames.get(i);
            ItemStack tool = plugin.getSchematicTool().createSchematicTool(schematicName);
            if (tool != null) {
                inv.setItem(schematicSlots[i], tool);
            }
        }
    }

    private static void createNavigationButtons(Inventory inv) {
        // Back button
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(ChatColor.YELLOW + "← Back to Main Menu");
        List<String> backLore = new ArrayList<>();
        backLore.add(ChatColor.GRAY + "Return to the main");
        backLore.add(ChatColor.GRAY + "Soaps Dungeons interface");
        backMeta.setLore(backLore);
        back.setItemMeta(backMeta);
        inv.setItem(45, back);

        // Close button
        ItemStack close = new ItemStack(Material.BARRIER);
        ItemMeta closeMeta = close.getItemMeta();
        closeMeta.setDisplayName(ChatColor.RED + "✖ Close Menu");
        List<String> closeLore = new ArrayList<>();
        closeLore.add(ChatColor.GRAY + "Close this interface");
        closeMeta.setLore(closeLore);
        close.setItemMeta(closeMeta);
        inv.setItem(53, close);
    }

    /**
     * Check if player already has a similar tool in their inventory.
     */
    private static boolean hasSimilarTool(Player player, ItemStack tool) {
        if (tool == null || !tool.hasItemMeta()) return false;

        String toolName = tool.getItemMeta().getDisplayName();
        if (toolName == null) return false;

        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.hasItemMeta()) {
                ItemMeta meta = item.getItemMeta();
                if (meta.hasDisplayName() && toolName.equals(meta.getDisplayName())) {
                    return true;
                }
            }
        }
        return false;
    }
}