package org.bukkit;

/**
 * Minimal stub of the Bukkit Material enum.  Only the constants referenced
 * within the ApexDungeons plugin are defined.  Additional entries can be
 * added as needed.
 */
public enum Material {
    AIR,
    STONE_BRICKS,
    OAK_PLANKS,
    TORCH,
    <PERSON><PERSON><PERSON>ST<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>_COBBLESTONE,
    <PERSON><PERSON><PERSON><PERSON>_STONE_BRICKS,
    <PERSON><PERSON><PERSON><PERSON><PERSON>_ANDESITE,
    CHEST,
    NE<PERSON>ER_STAR,
    ENDER_EYE,
    BOOK,
    BA<PERSON><PERSON>ER,
    PAPER,
    FILLED_MAP,
    BRUSH,
    IRON_INGOT,
    BREAD,
    GOLDEN_APPLE,
    ENDER_PEARL,
    DIAMOND,
    NETHERITE_INGOT,
    ELYTRA,
    TORCHES;

    /**
     * Match a material by name.  This stub returns null if the name does not
     * correspond to a defined constant.
     */
    public static Material matchMaterial(String name) {
        try {
            return Material.valueOf(name.replace(":", "_"));
        } catch (IllegalArgumentException ex) {
            return null;
        }
    }
}