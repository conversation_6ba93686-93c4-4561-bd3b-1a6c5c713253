package org.bukkit.inventory.meta;

import org.bukkit.persistence.PersistentDataContainer;
import java.util.List;

/**
 * Stub for the Bukkit ItemMeta interface located in the inventory.meta
 * package.  In this simplified environment it extends the ItemMeta class
 * defined under org.bukkit.inventory, so plugin code referencing
 * org.bukkit.inventory.meta.ItemMeta can compile without issues.
 */
public class ItemMeta extends org.bukkit.inventory.ItemMeta {
    // no additional methods; inherits functionality from parent stub
}