package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.ArrayList;
import java.util.List;

/**
 * GUI for accessing dungeon building tools including blocks, schematics, and connectors.
 * Available to all players for creative dungeon building.
 */
public class BuildingToolsGUI {
    private static final String GUI_NAME = ChatColor.DARK_PURPLE + "🔨 Building Tools";

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory(player, 54, GUI_NAME);
        
        // Fill background
        fillBackground(inv);
        
        // Create tool sections with proper organization
        createMasterBuilderWand(inv, plugin);
        createDungeonBlocks(inv, plugin);
        createMobSpawnerTools(inv, plugin);
        createSchematicTools(inv, plugin);
        createConnectorTools(inv, plugin);
        createNavigationButtons(inv);
        
        player.openInventory(inv);
        
        // Register event listener
        registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.PURPLE_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        
        // Fill border with background
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
    }

    /**
     * Create the Master Builder Wand section.
     */
    private static void createMasterBuilderWand(Inventory inv, ApexDungeons plugin) {
        // Master Builder Wand - Featured prominently
        ItemStack masterWand = plugin.getMasterBuilderWand().createMasterBuilderWand();
        inv.setItem(13, masterWand); // Center top row

        // Description item
        ItemStack description = new ItemStack(Material.BOOK);
        ItemMeta descMeta = description.getItemMeta();
        descMeta.setDisplayName(ChatColor.GOLD + "⚡ Master Builder Wand");
        List<String> descLore = new ArrayList<>();
        descLore.add(ChatColor.GRAY + "The ultimate unified building tool!");
        descLore.add("");
        descLore.add(ChatColor.GREEN + "✨ Features:");
        descLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "ALL schematics from folder");
        descLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Favorites system");
        descLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "3D wireframe preview");
        descLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "WASD movement controls");
        descLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Rotation & confirmation");
        descLore.add("");
        descLore.add(ChatColor.YELLOW + "🎯 Click the wand to get it!");
        descLore.add("");
        descLore.add(ChatColor.GREEN + "Made by Vexy");
        descMeta.setLore(descLore);
        description.setItemMeta(descMeta);
        inv.setItem(22, description); // Below the wand
    }



    private static void createDungeonBlocks(Inventory inv, ApexDungeons plugin) {
        // Section header - prominently featured in top row
        ItemStack header = new ItemStack(Material.EMERALD_BLOCK);
        ItemMeta headerMeta = header.getItemMeta();
        headerMeta.setDisplayName(ChatColor.GREEN + "🎯 Essential Dungeon Blocks");
        List<String> headerLore = new ArrayList<>();
        headerLore.add(ChatColor.GRAY + "Core blocks for creating interactive");
        headerLore.add(ChatColor.GRAY + "dungeon challenges and experiences");
        headerLore.add("");
        headerLore.add(ChatColor.YELLOW + "📋 Quick Setup Guide:");
        headerLore.add(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Place Start Block at entrance");
        headerLore.add(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Place End Block at final room");
        headerLore.add(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Players right-click to interact");
        headerLore.add("");
        headerLore.add(ChatColor.GREEN + "✨ Made by Vexy");
        headerMeta.setLore(headerLore);
        header.setItemMeta(headerMeta);
        inv.setItem(10, header); // Left side of top row

        // Dungeon Start Block - symmetrically placed
        ItemStack startBlock = plugin.getDungeonBlockManager().createStartBlockItem();
        inv.setItem(11, startBlock);

        // Dungeon End Block - symmetrically placed
        ItemStack endBlock = plugin.getDungeonBlockManager().createEndBlockItem();
        inv.setItem(15, endBlock);
    }

    /**
     * Create the mob spawner tools section.
     */
    private static void createMobSpawnerTools(Inventory inv, ApexDungeons plugin) {
        // Mob Pack Selection button
        ItemStack mobPackButton = new ItemStack(Material.ZOMBIE_HEAD);
        ItemMeta mobPackMeta = mobPackButton.getItemMeta();
        mobPackMeta.setDisplayName(ChatColor.DARK_RED + "⚔ Mob Pack Selection");
        List<String> mobPackLore = new ArrayList<>();
        mobPackLore.add(ChatColor.GRAY + "Select from pre-configured");
        mobPackLore.add(ChatColor.GRAY + "dungeon mob packs");
        mobPackLore.add("");
        mobPackLore.add(ChatColor.YELLOW + "📦 Available Categories:");

        // Show available categories
        List<String> categories = plugin.getDungeonMobManager().getCategories();
        for (String category : categories.subList(0, Math.min(categories.size(), 3))) {
            mobPackLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE +
                category.substring(0, 1).toUpperCase() + category.substring(1));
        }
        if (categories.size() > 3) {
            mobPackLore.add(ChatColor.AQUA + "• " + ChatColor.GRAY + "And " + (categories.size() - 3) + " more...");
        }

        mobPackLore.add("");
        mobPackLore.add(ChatColor.GREEN + "▶ Click to browse mob packs!");
        mobPackMeta.setLore(mobPackLore);
        mobPackButton.setItemMeta(mobPackMeta);
        inv.setItem(16, mobPackButton);

        // Traditional Mob Spawn Tool
        ItemStack mobSpawnTool = plugin.getMobSpawnTool().createMobSpawnTool();
        inv.setItem(25, mobSpawnTool);

        // Info item
        ItemStack mobInfo = new ItemStack(Material.BOOK);
        ItemMeta mobInfoMeta = mobInfo.getItemMeta();
        mobInfoMeta.setDisplayName(ChatColor.AQUA + "📋 Mob Spawner Guide");
        List<String> mobInfoLore = new ArrayList<>();
        mobInfoLore.add(ChatColor.YELLOW + "Two placement methods:");
        mobInfoLore.add("");
        mobInfoLore.add(ChatColor.GREEN + "Method 1: GUI Selection");
        mobInfoLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Use Mob Pack Selection");
        mobInfoLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Browse pre-configured mobs");
        mobInfoLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Click to select & place");
        mobInfoLore.add("");
        mobInfoLore.add(ChatColor.GREEN + "Method 2: Traditional");
        mobInfoLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Use Mob Spawn Tool");
        mobInfoLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Configure with commands");
        mobInfoLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "/dgn mobspawn set <mob>");
        mobInfoLore.add("");
        mobInfoLore.add(ChatColor.YELLOW + "Builder Mode:");
        mobInfoLore.add(ChatColor.GRAY + "• See spawn point indicators");
        mobInfoLore.add(ChatColor.GRAY + "• Players don't see them in gameplay");
        mobInfoMeta.setLore(mobInfoLore);
        mobInfo.setItemMeta(mobInfoMeta);
        inv.setItem(34, mobInfo);
    }

    private static void createSchematicTools(Inventory inv, ApexDungeons plugin) {
        // Section header
        ItemStack header = new ItemStack(Material.GOLDEN_SHOVEL);
        ItemMeta headerMeta = header.getItemMeta();
        headerMeta.setDisplayName(ChatColor.GOLD + "📐 Advanced Schematic Tools");
        List<String> headerLore = new ArrayList<>();
        headerLore.add(ChatColor.GRAY + "Professional-grade schematic placement");
        headerLore.add(ChatColor.GRAY + "with 3D preview and rotation controls");
        headerLore.add("");
        headerLore.add(ChatColor.GREEN + "✨ Features:");
        headerLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Real-time 3D preview");
        headerLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Interactive rotation (90° increments)");
        headerLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Keyboard movement controls");
        headerLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Visual confirmation system");
        headerLore.add("");
        headerLore.add(ChatColor.YELLOW + "Available Schematics (" + plugin.getSchematicManager().getLoadedSchematicNames().size() + "):");
        int count = 0;
        for (String schematicName : plugin.getSchematicManager().getLoadedSchematicNames()) {
            if (count < 5) { // Show first 5
                headerLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + schematicName);
                count++;
            } else if (count == 5) {
                headerLore.add(ChatColor.GRAY + "• ... and " + (plugin.getSchematicManager().getLoadedSchematicNames().size() - 5) + " more");
                break;
            }
        }
        headerMeta.setLore(headerLore);
        header.setItemMeta(headerMeta);
        inv.setItem(19, header);
        
        // Create schematic tools (organized layout avoiding dungeon blocks)
        List<String> schematicNames = new ArrayList<>(plugin.getSchematicManager().getLoadedSchematicNames());
        int[] schematicSlots = {20, 21, 22, 23, 24, 29, 30, 31, 32, 33}; // Two clean rows
        
        for (int i = 0; i < Math.min(schematicNames.size(), schematicSlots.length); i++) {
            String schematicName = schematicNames.get(i);
            ItemStack tool = plugin.getSchematicTool().createSchematicTool(schematicName);
            if (tool != null) {
                inv.setItem(schematicSlots[i], tool);
            }
        }
        
        // More schematics indicator if needed
        if (schematicNames.size() > schematicSlots.length) {
            ItemStack more = new ItemStack(Material.BOOK);
            ItemMeta moreMeta = more.getItemMeta();
            moreMeta.setDisplayName(ChatColor.YELLOW + "📚 More Schematics");
            List<String> moreLore = new ArrayList<>();
            moreLore.add(ChatColor.GRAY + "There are " + (schematicNames.size() - schematicSlots.length) + " more schematics available");
            moreLore.add(ChatColor.GRAY + "Use /dgn admin for full list");
            moreMeta.setLore(moreLore);
            more.setItemMeta(moreMeta);
            inv.setItem(34, more); // Moved to avoid conflicts
        }
    }

    private static void createConnectorTools(Inventory inv, ApexDungeons plugin) {
        // Section header
        ItemStack header = new ItemStack(Material.BLAZE_ROD);
        ItemMeta headerMeta = header.getItemMeta();
        headerMeta.setDisplayName(ChatColor.RED + "🔗 Connection & Mob Tools");
        List<String> headerLore = new ArrayList<>();
        headerLore.add(ChatColor.GRAY + "Professional tools for linking rooms");
        headerLore.add(ChatColor.GRAY + "and placing mob spawn points");
        headerLore.add("");
        headerLore.add(ChatColor.YELLOW + "🔧 Room Connections:");
        headerLore.add(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Left-click on first room wall");
        headerLore.add(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Right-click on second room wall");
        headerLore.add(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Type " + ChatColor.YELLOW + "1" + ChatColor.WHITE + ", " + ChatColor.YELLOW + "2" + ChatColor.WHITE + ", or " + ChatColor.YELLOW + "3" + ChatColor.WHITE + " in chat");
        headerLore.add("");
        headerLore.add(ChatColor.YELLOW + "👹 Mob Spawning:");
        headerLore.add(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Right-click blocks to set spawn points");
        headerLore.add(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Use commands to configure mobs");
        headerLore.add(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Players trigger spawns by proximity");
        headerMeta.setLore(headerLore);
        header.setItemMeta(headerMeta);
        inv.setItem(37, header);
        
        // Room Connector Tool
        ItemStack connector = plugin.getRoomConnector().createConnectorTool();
        inv.setItem(38, connector);

        // Mob Spawn Tool
        ItemStack mobSpawnTool = plugin.getMobSpawnTool().createMobSpawnTool();
        inv.setItem(39, mobSpawnTool);

        // Boss Spawn Tool
        ItemStack bossSpawnTool = plugin.getMobSpawnTool().createBossSpawnTool();
        inv.setItem(40, bossSpawnTool);

        // Chest Spawn Tool
        ItemStack chestSpawnTool = plugin.getChestSpawnTool().createChestSpawnTool();
        inv.setItem(41, chestSpawnTool);

        // Wand Tool (existing)
        ItemStack wand = new ItemStack(Material.STICK);
        ItemMeta wandMeta = wand.getItemMeta();
        wandMeta.setDisplayName(ChatColor.BLUE + "Selection Wand");
        List<String> wandLore = new ArrayList<>();
        wandLore.add(ChatColor.GRAY + "Original selection tool");
        wandLore.add(ChatColor.GRAY + "for basic area operations");
        wandLore.add("");
        wandLore.add(ChatColor.GREEN + "Left-click: " + ChatColor.WHITE + "Set position 1");
        wandLore.add(ChatColor.GREEN + "Right-click: " + ChatColor.WHITE + "Set position 2");
        wandMeta.setLore(wandLore);
        wand.setItemMeta(wandMeta);
        inv.setItem(42, wand);
    }

    private static void createNavigationButtons(Inventory inv) {
        // Back button
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(ChatColor.YELLOW + "← Back to Main Menu");
        List<String> backLore = new ArrayList<>();
        backLore.add(ChatColor.GRAY + "Return to the main");
        backLore.add(ChatColor.GRAY + "Soaps Dungeons interface");
        backMeta.setLore(backLore);
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        
        // Help button
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(ChatColor.AQUA + "📖 Tool Help");
        List<String> helpLore = new ArrayList<>();
        helpLore.add(ChatColor.GRAY + "Learn how to use");
        helpLore.add(ChatColor.GRAY + "the building tools");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(49, help);
        
        // Close button
        ItemStack close = new ItemStack(Material.BARRIER);
        ItemMeta closeMeta = close.getItemMeta();
        closeMeta.setDisplayName(ChatColor.RED + "✖ Close Menu");
        List<String> closeLore = new ArrayList<>();
        closeLore.add(ChatColor.GRAY + "Close this interface");
        closeMeta.setLore(closeLore);
        close.setItemMeta(closeMeta);
        inv.setItem(53, close);
    }

    private static void registerEventListener(ApexDungeons plugin) {
        JavaPlugin pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    Player clicker = (Player) e.getWhoClicked();
                    
                    switch (slot) {
                        case 13: // Master Builder Wand
                            clicker.closeInventory();
                            ItemStack masterWand = plugin.getMasterBuilderWand().createMasterBuilderWand();
                            clicker.getInventory().addItem(masterWand);
                            clicker.sendMessage(ChatColor.GREEN + "✓ You received the Master Builder Wand!");
                            clicker.sendMessage(ChatColor.GRAY + "Right-click to open schematic selection, left-click to place!");
                            break;
                        case 16: // Mob Pack Selection
                            clicker.closeInventory();
                            MobPackSelectionGUI.open(clicker, plugin);
                            break;
                        case 11: // Start Block
                        case 15: // End Block
                        case 20: case 21: case 22: case 23: case 24: // Schematic Tools Row 1
                        case 29: case 30: case 31: case 32: case 33: // Schematic Tools Row 2
                        case 38: // Room Connector
                        case 39: // Mob Spawn Tool
                        case 40: // Boss Spawn Tool
                        case 41: // Chest Spawn Tool
                        case 42: // Selection Wand Tool
                            // Give the item to the player (check for duplicates)
                            ItemStack item = e.getCurrentItem();
                            if (item != null && item.getType() != Material.AIR) {
                                if (slot == 39) { // Mob Spawn tool
                                    if (!hasMobSpawnToolInInventory(clicker)) {
                                        plugin.getMobSpawnTool().giveMobSpawnTool(clicker);
                                        clicker.sendMessage(ChatColor.GREEN + "✓ Mob spawn tool added to your inventory!");
                                        showToolGuidance(clicker, "mob_spawn");
                                    } else {
                                        clicker.sendMessage(ChatColor.YELLOW + "You already have a mob spawn tool!");
                                    }
                                } else if (slot == 40) { // Boss Spawn tool
                                    if (!hasBossSpawnToolInInventory(clicker)) {
                                        plugin.getMobSpawnTool().giveBossSpawnTool(clicker);
                                        clicker.sendMessage(ChatColor.GREEN + "✓ Boss spawn tool added to your inventory!");
                                        showToolGuidance(clicker, "boss_spawn");
                                    } else {
                                        clicker.sendMessage(ChatColor.YELLOW + "You already have a boss spawn tool!");
                                    }
                                } else if (slot == 41) { // Chest Spawn tool
                                    if (!hasChestSpawnToolInInventory(clicker)) {
                                        plugin.getChestSpawnTool().giveChestSpawnTool(clicker);
                                        clicker.sendMessage(ChatColor.GREEN + "✓ Chest spawn tool added to your inventory!");
                                        showToolGuidance(clicker, "chest_spawn");
                                    } else {
                                        clicker.sendMessage(ChatColor.YELLOW + "You already have a chest spawn tool!");
                                    }
                                } else if (slot == 42) { // Selection Wand tool - use plugin's wand manager
                                    if (!hasWandInInventory(clicker)) {
                                        plugin.getWandManager().giveWand(clicker);
                                        clicker.sendMessage(ChatColor.GREEN + "✓ Selection wand added to your inventory!");
                                        showToolGuidance(clicker, "wand");
                                    } else {
                                        clicker.sendMessage(ChatColor.YELLOW + "You already have a selection wand!");
                                    }
                                } else {
                                    // Check if player already has this tool
                                    if (!hasSimilarTool(clicker, item)) {
                                        ItemStack giveItem = item.clone();
                                        clicker.getInventory().addItem(giveItem);
                                        clicker.sendMessage(ChatColor.GREEN + "✓ Tool added to your inventory!");

                                        // Show specific guidance based on tool type
                                        if (slot == 11) showToolGuidance(clicker, "start_block");
                                        else if (slot == 12) showToolGuidance(clicker, "end_block");
                                        else if (slot == 38) showToolGuidance(clicker, "connector");
                                        else showToolGuidance(clicker, "schematic");
                                    } else {
                                        clicker.sendMessage(ChatColor.YELLOW + "You already have this tool!");
                                    }
                                }
                            }
                            break;
                        case 34: // More schematics
                            clicker.closeInventory();
                            if (clicker.hasPermission("apexdungeons.admin")) {
                                AdminGUI.open(clicker, plugin);
                            } else {
                                clicker.sendMessage(ChatColor.RED + "You need admin permissions to access all schematics.");
                            }
                            break;
                        case 45: // Back
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            break;
                        case 49: // Help
                            clicker.closeInventory();
                            showToolHelp(clicker);
                            break;
                        case 53: // Close
                            clicker.closeInventory();
                            break;
                    }
                }
            }
        }, pl);
    }

    private static void showToolHelp(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════════════");
        player.sendMessage(ChatColor.GOLD + "      🔨 BUILDING TOOLS GUIDE 🔨");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════════════");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🏗️ STEP-BY-STEP DUNGEON BUILDING:");
        player.sendMessage(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Plan your dungeon layout on paper first");
        player.sendMessage(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Build or place your start room");
        player.sendMessage(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Add a " + ChatColor.GREEN + "Start Block" + ChatColor.WHITE + " in the entrance");
        player.sendMessage(ChatColor.AQUA + "4. " + ChatColor.WHITE + "Build additional rooms for challenges");
        player.sendMessage(ChatColor.AQUA + "5. " + ChatColor.WHITE + "Connect rooms using the " + ChatColor.YELLOW + "Room Connector");
        player.sendMessage(ChatColor.AQUA + "6. " + ChatColor.WHITE + "Build your final/boss room");
        player.sendMessage(ChatColor.AQUA + "7. " + ChatColor.WHITE + "Add an " + ChatColor.AQUA + "End Block" + ChatColor.WHITE + " in the final room");
        player.sendMessage(ChatColor.AQUA + "8. " + ChatColor.WHITE + "Test your dungeon with " + ChatColor.YELLOW + "/dgn start <name>");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🎯 DUNGEON BLOCKS:");
        player.sendMessage(ChatColor.AQUA + "• Start Block (Emerald): " + ChatColor.WHITE + "Players right-click to begin");
        player.sendMessage(ChatColor.AQUA + "• End Block (Diamond): " + ChatColor.WHITE + "Players right-click to complete");
        player.sendMessage(ChatColor.GRAY + "  ⚠ Players must activate Start Block before End Block");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "📐 SCHEMATIC TOOLS:");
        player.sendMessage(ChatColor.AQUA + "• Right-click: " + ChatColor.WHITE + "Show 3D preview with controls");
        player.sendMessage(ChatColor.AQUA + "• Left-click: " + ChatColor.WHITE + "Place immediately (no preview)");
        player.sendMessage(ChatColor.AQUA + "• Preview Controls: " + ChatColor.YELLOW + "W/A/S/D" + ChatColor.WHITE + " move, " + ChatColor.YELLOW + "R" + ChatColor.WHITE + " rotate, " + ChatColor.YELLOW + "Enter" + ChatColor.WHITE + " confirm");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🔗 ROOM CONNECTOR:");
        player.sendMessage(ChatColor.AQUA + "• Left-click: " + ChatColor.WHITE + "Select first wall");
        player.sendMessage(ChatColor.AQUA + "• Right-click: " + ChatColor.WHITE + "Select second wall");
        player.sendMessage(ChatColor.AQUA + "• Type in chat: " + ChatColor.YELLOW + "1" + ChatColor.WHITE + " (doorway), " + ChatColor.YELLOW + "2" + ChatColor.WHITE + " (decorated), " + ChatColor.YELLOW + "3" + ChatColor.WHITE + " (corridor)");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🔧 SELECTION WAND:");
        player.sendMessage(ChatColor.AQUA + "• Left-click: " + ChatColor.WHITE + "Set position 1");
        player.sendMessage(ChatColor.AQUA + "• Right-click: " + ChatColor.WHITE + "Set position 2");
        player.sendMessage(ChatColor.AQUA + "• Use for: " + ChatColor.WHITE + "Area selection and measurements");
        player.sendMessage("");
        player.sendMessage(ChatColor.YELLOW + "💡 PRO TIPS:");
        player.sendMessage(ChatColor.GRAY + "• Use " + ChatColor.YELLOW + "/dgn help guide" + ChatColor.GRAY + " for complete room system guide");
        player.sendMessage(ChatColor.GRAY + "• Leave 3-block high spaces for doorway connections");
        player.sendMessage(ChatColor.GRAY + "• Test your dungeon before sharing with players");
        player.sendMessage(ChatColor.GRAY + "• Use schematics for consistent room designs");
    }

    /**
     * Check if player already has a wand in their inventory.
     */
    private static boolean hasWandInInventory(Player player) {
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.hasItemMeta()) {
                ItemMeta meta = item.getItemMeta();
                if (meta.hasDisplayName() && meta.getDisplayName().contains("Dungeon Architect Wand")) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Check if player has a mob spawn tool in their inventory.
     */
    private static boolean hasMobSpawnToolInInventory(Player player) {
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.getType() == Material.ZOMBIE_HEAD) {
                ItemMeta meta = item.getItemMeta();
                if (meta != null && meta.hasDisplayName() &&
                    meta.getDisplayName().equals(ChatColor.RED + "Mob Spawn Tool")) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Check if player has a boss spawn tool in their inventory.
     */
    private static boolean hasBossSpawnToolInInventory(Player player) {
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.getType() == Material.WITHER_SKELETON_SKULL) {
                ItemMeta meta = item.getItemMeta();
                if (meta != null && meta.hasDisplayName() &&
                    meta.getDisplayName().equals(ChatColor.DARK_RED + "Boss Spawn Tool")) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Check if player has a chest spawn tool in their inventory.
     */
    private static boolean hasChestSpawnToolInInventory(Player player) {
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.getType() == Material.CHEST) {
                ItemMeta meta = item.getItemMeta();
                if (meta != null && meta.hasDisplayName() &&
                    meta.getDisplayName().equals(ChatColor.GOLD + "Chest Spawn Tool")) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Check if player already has a similar tool in their inventory.
     */
    private static boolean hasSimilarTool(Player player, ItemStack tool) {
        if (tool == null || !tool.hasItemMeta()) return false;

        String toolName = tool.getItemMeta().getDisplayName();
        if (toolName == null) return false;

        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.hasItemMeta()) {
                ItemMeta meta = item.getItemMeta();
                if (meta.hasDisplayName() && toolName.equals(meta.getDisplayName())) {
                    return true;
                }
            }
        }
        return false;
    }



    /**
     * Show contextual guidance when a player receives a tool.
     */
    private static void showToolGuidance(Player player, String toolType) {
        switch (toolType) {
            case "start_block":
                player.sendMessage("");
                player.sendMessage(ChatColor.GREEN + "🎯 START BLOCK GUIDANCE:");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Place this in your entrance/start room");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Players right-click to begin challenges");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Only one per dungeon recommended");
                player.sendMessage(ChatColor.GRAY + "💡 Tip: Make it visible and accessible!");
                break;
            case "end_block":
                player.sendMessage("");
                player.sendMessage(ChatColor.GREEN + "🏆 END BLOCK GUIDANCE:");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Place this in your final/boss room");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Players right-click to complete & get rewards");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Must activate Start Block first");
                player.sendMessage(ChatColor.GRAY + "💡 Tip: Place after all challenges are complete!");
                break;
            case "connector":
                player.sendMessage("");
                player.sendMessage(ChatColor.GREEN + "🔗 ROOM CONNECTOR GUIDANCE:");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Left-click first wall, right-click second wall");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Type 1, 2, or 3 in chat for connection type");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Leave 3-block high spaces for doorways");
                player.sendMessage(ChatColor.GRAY + "💡 Tip: Plan your room layout first!");
                break;
            case "schematic":
                player.sendMessage("");
                player.sendMessage(ChatColor.GREEN + "📐 SCHEMATIC TOOL GUIDANCE:");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Right-click for 3D preview with controls");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Use W/A/S/D to move, R to rotate");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Press Enter to confirm placement");
                player.sendMessage(ChatColor.GRAY + "💡 Tip: Preview before placing to avoid mistakes!");
                break;
            case "mob_spawn":
                player.sendMessage("");
                player.sendMessage(ChatColor.GREEN + "👹 MOB SPAWN TOOL GUIDANCE:");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Right-click blocks to set spawn points");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Use /dgn mobspawn set <mob_name> to configure");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Players trigger spawns by proximity");
                player.sendMessage(ChatColor.GRAY + "💡 Tip: Works with MythicMobs if installed!");
                break;
            case "boss_spawn":
                player.sendMessage("");
                player.sendMessage(ChatColor.GREEN + "💀 BOSS SPAWN TOOL GUIDANCE:");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Right-click blocks to set boss spawn points");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Use /dgn bossspawn set <boss_name> to configure");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Players trigger boss spawns by proximity");
                player.sendMessage(ChatColor.GRAY + "💡 Tip: Perfect for dungeon boss rooms!");
                break;
            case "chest_spawn":
                player.sendMessage("");
                player.sendMessage(ChatColor.GREEN + "💰 CHEST SPAWN TOOL GUIDANCE:");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Right-click blocks to set chest spawn points");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Use /dgn chestspawn set <loot_table> to configure");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Players trigger chests by walking near");
                player.sendMessage(ChatColor.GRAY + "💡 Tip: Available tables: common, rare, epic, boss_rewards");
                break;
            case "wand":
                player.sendMessage("");
                player.sendMessage(ChatColor.GREEN + "🔧 SELECTION WAND GUIDANCE:");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Left-click to set position 1");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Right-click to set position 2");
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Use for area selection and measurements");
                player.sendMessage(ChatColor.GRAY + "💡 Tip: Great for planning room sizes!");
                break;
        }
    }
}
