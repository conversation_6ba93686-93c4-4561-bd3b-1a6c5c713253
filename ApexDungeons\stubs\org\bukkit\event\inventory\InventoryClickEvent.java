package org.bukkit.event.inventory;

import org.bukkit.event.Event;
import org.bukkit.inventory.Inventory;
import org.bukkit.entity.Player;

/**
 * Stub for InventoryClickEvent.  Holds raw slot and clicked inventory.
 */
public class InventoryClickEvent extends Event {
    private final Inventory inventory;
    private final Player player;
    private final int slot;
    private boolean cancelled;
    private final String title;
    public InventoryClickEvent(Inventory inventory, Player player, int slot, String title) {
        this.inventory = inventory;
        this.player = player;
        this.slot = slot;
        this.title = title;
    }
    public Inventory getInventory() { return inventory; }
    public Player getWhoClicked() { return player; }
    public int getRawSlot() { return slot; }
    public void setCancelled(boolean cancelled) { this.cancelled = cancelled; }
    public boolean isCancelled() { return cancelled; }
    public org.bukkit.event.inventory.InventoryView getView() {
        return new org.bukkit.event.inventory.InventoryView(title);
    }
}