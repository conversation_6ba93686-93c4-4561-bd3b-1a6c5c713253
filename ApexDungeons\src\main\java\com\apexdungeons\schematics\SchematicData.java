package com.apexdungeons.schematics;

import org.bukkit.Material;

/**
 * Represents schematic data including dimensions and block layout.
 * Stores the 3D array of materials that make up a schematic structure.
 */
public class SchematicData {
    private final String name;
    private final int width;
    private final int height;
    private final int depth;
    private final Material[][][] blocks;

    public SchematicData(String name, int width, int height, int depth, Material[][][] blocks) {
        this.name = name;
        this.width = width;
        this.height = height;
        this.depth = depth;
        this.blocks = blocks;
    }

    public String getName() {
        return name;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public int getDepth() {
        return depth;
    }

    public Material[][][] getBlocks() {
        return blocks;
    }

    /**
     * Get the material at specific coordinates.
     */
    public Material getBlockAt(int x, int y, int z) {
        if (x < 0 || x >= width || y < 0 || y >= height || z < 0 || z >= depth) {
            return Material.AIR;
        }
        return blocks[y][z][x];
    }

    /**
     * Set the material at specific coordinates.
     */
    public void setBlockAt(int x, int y, int z, Material material) {
        if (x >= 0 && x < width && y >= 0 && y < height && z >= 0 && z < depth) {
            blocks[y][z][x] = material;
        }
    }

    /**
     * Get the total number of blocks in the schematic.
     */
    public int getTotalBlocks() {
        return width * height * depth;
    }

    /**
     * Get the number of non-air blocks in the schematic.
     */
    public int getSolidBlocks() {
        int count = 0;
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    if (blocks[y][z][x] != Material.AIR) {
                        count++;
                    }
                }
            }
        }
        return count;
    }

    /**
     * Create a copy of this schematic data.
     */
    public SchematicData copy() {
        Material[][][] newBlocks = new Material[height][depth][width];
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    newBlocks[y][z][x] = blocks[y][z][x];
                }
            }
        }
        return new SchematicData(name + "_copy", width, height, depth, newBlocks);
    }

    /**
     * Rotate the schematic 90 degrees clockwise around the Y axis.
     */
    public SchematicData rotateClockwise() {
        Material[][][] rotatedBlocks = new Material[height][width][depth];
        
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    // Rotate coordinates: new_x = z, new_z = width - 1 - x
                    rotatedBlocks[y][x][depth - 1 - z] = blocks[y][z][x];
                }
            }
        }
        
        return new SchematicData(name + "_rotated", depth, height, width, rotatedBlocks);
    }

    /**
     * Mirror the schematic along the X axis.
     */
    public SchematicData mirrorX() {
        Material[][][] mirroredBlocks = new Material[height][depth][width];
        
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    mirroredBlocks[y][z][width - 1 - x] = blocks[y][z][x];
                }
            }
        }
        
        return new SchematicData(name + "_mirrored", width, height, depth, mirroredBlocks);
    }

    /**
     * Mirror the schematic along the Z axis.
     */
    public SchematicData mirrorZ() {
        Material[][][] mirroredBlocks = new Material[height][depth][width];
        
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    mirroredBlocks[y][depth - 1 - z][x] = blocks[y][z][x];
                }
            }
        }
        
        return new SchematicData(name + "_mirrored_z", width, height, depth, mirroredBlocks);
    }

    @Override
    public String toString() {
        return String.format("SchematicData{name='%s', dimensions=%dx%dx%d, solidBlocks=%d}", 
            name, width, height, depth, getSolidBlocks());
    }
}
