package org.bukkit.command;

import org.bukkit.plugin.Plugin;

/**
 * Stub for PluginCommand.  Holds an executor and a tab completer.
 */
public class PluginCommand extends Command {
    private CommandExecutor executor;
    private TabCompleter completer;
    public PluginCommand(String name, Plugin plugin) { super(name); }
    public void setExecutor(CommandExecutor executor) { this.executor = executor; }
    public void setTabCompleter(TabCompleter completer) { this.completer = completer; }
    public CommandExecutor getExecutor() { return executor; }
    public TabCompleter getTabCompleter() { return completer; }
}