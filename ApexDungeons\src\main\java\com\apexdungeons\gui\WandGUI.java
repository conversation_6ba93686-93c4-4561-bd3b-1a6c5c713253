package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.ArrayList;
import java.util.List;

/**
 * GUI shown when the player uses the Architect Wand.  Lists all available room
 * blueprints from the rooms/ folder.  Clicking on a room will place it at
 * the player's targeted block.
 */
public class WandGUI {
    private static final String GUI_NAME = ChatColor.DARK_GRAY + "Select Room";

    public static void open(Player player, ApexDungeons plugin) {
        List<String> roomNames = plugin.getDungeonManager().listRoomNames();
        int rows = Math.max(1, (int) Math.ceil(roomNames.size() / 9.0));
        Inventory inv = Bukkit.createInventory(player, rows * 9, GUI_NAME);
        for (int i = 0; i < roomNames.size(); i++) {
            String room = roomNames.get(i);
            ItemStack paper = new ItemStack(Material.PAPER);
            ItemMeta meta = paper.getItemMeta();
            meta.setDisplayName(ChatColor.GREEN + room);
            meta.setLore(List.of(ChatColor.GRAY + "Place this room"));
            paper.setItemMeta(meta);
            inv.setItem(i, paper);
        }
        JavaPlugin pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    if (slot >= 0 && slot < roomNames.size()) {
                        String selected = roomNames.get(slot);
                        e.getWhoClicked().closeInventory();
                        plugin.getDungeonManager().placeRoomAt((Player) e.getWhoClicked(), selected);
                    }
                }
            }
        }, pl);
        player.openInventory(inv);
    }
}