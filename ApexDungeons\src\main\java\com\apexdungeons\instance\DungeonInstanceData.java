package com.apexdungeons.instance;

import org.bukkit.World;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Represents data for a dungeon instance.
 */
public class DungeonInstanceData {
    private final String instanceId;
    private final String templateName;
    private final World instanceWorld;
    private final List<Player> team;
    private final long createdTime;
    private long startTime;
    private boolean started;
    private boolean completed;

    public DungeonInstanceData(String instanceId, String templateName, World instanceWorld, List<Player> team, long createdTime) {
        this.instanceId = instanceId;
        this.templateName = templateName;
        this.instanceWorld = instanceWorld;
        this.team = new ArrayList<>(team);
        this.createdTime = createdTime;
        this.started = false;
        this.completed = false;
    }

    /**
     * Start the dungeon instance.
     */
    public void start() {
        if (!started) {
            this.started = true;
            this.startTime = System.currentTimeMillis();
        }
    }

    /**
     * Complete the dungeon instance.
     */
    public void complete() {
        this.completed = true;
    }

    /**
     * Add player to the team.
     */
    public void addPlayer(Player player) {
        if (!team.contains(player)) {
            team.add(player);
        }
    }

    /**
     * Remove player from the team.
     */
    public void removePlayer(Player player) {
        team.remove(player);
    }

    /**
     * Check if player is in this instance.
     */
    public boolean hasPlayer(Player player) {
        return team.contains(player);
    }

    /**
     * Check if player is in this instance by UUID.
     */
    public boolean hasPlayer(UUID playerId) {
        return team.stream().anyMatch(p -> p.getUniqueId().equals(playerId));
    }

    /**
     * Get elapsed time since start.
     */
    public long getElapsedTime() {
        if (!started) {
            return 0;
        }
        return System.currentTimeMillis() - startTime;
    }

    /**
     * Get formatted elapsed time.
     */
    public String getFormattedElapsedTime() {
        long elapsed = getElapsedTime();
        long minutes = elapsed / 60000;
        long seconds = (elapsed % 60000) / 1000;
        return String.format("%d:%02d", minutes, seconds);
    }

    /**
     * Check if instance is empty.
     */
    public boolean isEmpty() {
        return team.isEmpty();
    }

    /**
     * Get team size.
     */
    public int getTeamSize() {
        return team.size();
    }

    // Getters
    public String getInstanceId() { return instanceId; }
    public String getTemplateName() { return templateName; }
    public World getInstanceWorld() { return instanceWorld; }
    public List<Player> getTeam() { return new ArrayList<>(team); }
    public long getCreatedTime() { return createdTime; }
    public long getStartTime() { return startTime; }
    public boolean isStarted() { return started; }
    public boolean isCompleted() { return completed; }

    @Override
    public String toString() {
        return "DungeonInstanceData{" +
                "instanceId='" + instanceId + '\'' +
                ", templateName='" + templateName + '\'' +
                ", teamSize=" + team.size() +
                ", started=" + started +
                ", completed=" + completed +
                '}';
    }
}
