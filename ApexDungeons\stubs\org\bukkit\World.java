package org.bukkit;

import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;

/**
 * Minimal stub for World.  Methods either return default values or no-ops.
 */
public class World {
    public void spawnEntity(Location location, EntityType type) {}
    public org.bukkit.block.Block getBlockAt(int x, int y, int z) { return new org.bukkit.block.Block(); }
    public org.bukkit.block.Block getBlockAt(Location location) { return new org.bukkit.block.Block(); }
    public Location getSpawnLocation() { return new Location(this,0,0,0); }

    /**
     * Drops an item at a location.  Stubbed implementation simply
     * discards the item and returns null.
     * @param location drop location
     * @param item item to drop
     * @return null (no entity created)
     */
    public Entity dropItem(Location location, org.bukkit.inventory.ItemStack item) {
        return null;
    }
}