package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * GUI for previewing dungeon themes with detailed information.
 */
public class ThemePreviewGUI {
    private static final String GUI_NAME = ChatColor.LIGHT_PURPLE + "🎨 Theme Preview";

    public static void open(Player player, ApexDungeons plugin, String theme) {
        Inventory inv = Bukkit.createInventory(player, 54, GUI_NAME + " - " + theme);
        
        // Fill background
        fillBackground(inv);
        
        // Create theme preview
        createThemePreview(inv, theme);
        
        // Create navigation buttons
        createNavigationButtons(inv, plugin, theme);
        
        player.openInventory(inv);
        
        // Register event listener
        registerEventListener(plugin, theme);
    }

    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.PURPLE_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        
        // Fill border
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
    }

    private static void createThemePreview(Inventory inv, String theme) {
        // Theme preview based on type
        switch (theme.toLowerCase()) {
            case "castle":
                createCastlePreview(inv);
                break;
            case "cave":
                createCavePreview(inv);
                break;
            case "temple":
                createTemplePreview(inv);
                break;
        }
    }

    private static void createCastlePreview(Inventory inv) {
        ItemStack preview = new ItemStack(Material.STONE_BRICKS);
        ItemMeta meta = preview.getItemMeta();
        meta.setDisplayName(ChatColor.GRAY + "🏰 Castle Theme Preview");
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.YELLOW + "Medieval fortress dungeons with:");
        lore.add(ChatColor.AQUA + "• Stone brick architecture");
        lore.add(ChatColor.AQUA + "• Multi-level towers");
        lore.add(ChatColor.AQUA + "• Throne rooms & armories");
        lore.add(ChatColor.AQUA + "• Medieval atmosphere");
        meta.setLore(lore);
        preview.setItemMeta(meta);
        inv.setItem(22, preview);
    }

    private static void createCavePreview(Inventory inv) {
        ItemStack preview = new ItemStack(Material.COBBLESTONE);
        ItemMeta meta = preview.getItemMeta();
        meta.setDisplayName(ChatColor.DARK_GRAY + "🕳 Cave Theme Preview");
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.YELLOW + "Underground cave systems with:");
        lore.add(ChatColor.AQUA + "• Natural stone formations");
        lore.add(ChatColor.AQUA + "• Underground pools & rivers");
        lore.add(ChatColor.AQUA + "• Ore veins & crystals");
        lore.add(ChatColor.AQUA + "• Dark atmosphere");
        meta.setLore(lore);
        preview.setItemMeta(meta);
        inv.setItem(22, preview);
    }

    private static void createTemplePreview(Inventory inv) {
        ItemStack preview = new ItemStack(Material.SANDSTONE);
        ItemMeta meta = preview.getItemMeta();
        meta.setDisplayName(ChatColor.GOLD + "🏛 Temple Theme Preview");
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.YELLOW + "Ancient mystical temples with:");
        lore.add(ChatColor.AQUA + "• Golden decorations");
        lore.add(ChatColor.AQUA + "• Mystical chambers");
        lore.add(ChatColor.AQUA + "• Ancient traps & puzzles");
        lore.add(ChatColor.AQUA + "• Sacred atmosphere");
        meta.setLore(lore);
        preview.setItemMeta(meta);
        inv.setItem(22, preview);
    }

    private static void createNavigationButtons(Inventory inv, ApexDungeons plugin, String theme) {
        // Back button
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(ChatColor.RED + "← Back to Browser");
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        
        // Create with this theme button
        ItemStack create = new ItemStack(Material.NETHER_STAR);
        ItemMeta createMeta = create.getItemMeta();
        createMeta.setDisplayName(ChatColor.GREEN + "✚ Create " + theme + " Dungeon");
        List<String> createLore = new ArrayList<>();
        createLore.add(ChatColor.GRAY + "Create a new dungeon with this theme");
        createMeta.setLore(createLore);
        create.setItemMeta(createMeta);
        inv.setItem(53, create);
    }

    private static void registerEventListener(ApexDungeons plugin, String theme) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().contains(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player) e.getWhoClicked();
                    int slot = e.getRawSlot();
                    
                    switch (slot) {
                        case 45: // Back
                            clicker.closeInventory();
                            DungeonBrowserGUI.open(clicker, plugin);
                            break;
                        case 53: // Create
                            clicker.closeInventory();
                            PresetGUI.open(clicker, plugin);
                            break;
                    }
                }
            }
        }, plugin);
    }
}
