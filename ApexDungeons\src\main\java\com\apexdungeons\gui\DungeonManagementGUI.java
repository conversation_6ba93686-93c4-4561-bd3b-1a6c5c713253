package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Advanced GUI for comprehensive dungeon management including deletion, editing,
 * teleportation, and detailed information display.
 */
public class DungeonManagementGUI {
    private static final String GUI_NAME = ChatColor.DARK_BLUE + "⚔ Dungeon Management";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MMM dd, yyyy HH:mm");

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory(player, 54, GUI_NAME);
        
        // Fill background
        fillBackground(inv);
        
        // Create dungeon list
        createDungeonList(inv, player, plugin);
        
        // Create management buttons
        createManagementButtons(inv, player, plugin);
        
        // Create navigation buttons
        createNavigationButtons(inv);
        
        player.openInventory(inv);
        
        // Register event listener
        registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.BLUE_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        
        // Fill border
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
    }

    private static void createDungeonList(Inventory inv, Player player, ApexDungeons plugin) {
        Map<String, DungeonInstance> dungeons = plugin.getDungeonManager().getDungeons();
        
        if (dungeons.isEmpty()) {
            // No dungeons message
            ItemStack noDungeons = new ItemStack(Material.BARRIER);
            ItemMeta noMeta = noDungeons.getItemMeta();
            noMeta.setDisplayName(ChatColor.RED + "No Active Dungeons");
            List<String> noLore = new ArrayList<>();
            noLore.add(ChatColor.GRAY + "You haven't created any dungeons yet!");
            noLore.add("");
            noLore.add(ChatColor.YELLOW + "Create your first dungeon to");
            noLore.add(ChatColor.YELLOW + "start managing your creations.");
            noMeta.setLore(noLore);
            noDungeons.setItemMeta(noMeta);
            inv.setItem(22, noDungeons);
            return;
        }

        // Display dungeons (up to 28 slots: 9-17, 18-26, 27-35, 36-44)
        int[] dungeonSlots = {
            9, 10, 11, 12, 13, 14, 15, 16, 17,
            18, 19, 20, 21, 22, 23, 24, 25, 26,
            27, 28, 29, 30, 31, 32, 33, 34, 35,
            36, 37, 38, 39, 40, 41, 42, 43, 44
        };
        
        int index = 0;
        for (Map.Entry<String, DungeonInstance> entry : dungeons.entrySet()) {
            if (index >= dungeonSlots.length) break;
            
            DungeonInstance dungeon = entry.getValue();
            ItemStack dungeonItem = createDungeonItem(dungeon, player);
            inv.setItem(dungeonSlots[index], dungeonItem);
            index++;
        }
    }

    private static ItemStack createDungeonItem(DungeonInstance dungeon, Player player) {
        // Choose material based on dungeon status and ownership
        Material material;
        if (dungeon.isGenerating()) {
            material = Material.YELLOW_CONCRETE;
        } else if (dungeon.getCreator().equals(player.getName())) {
            material = Material.GREEN_CONCRETE;
        } else {
            material = Material.LIGHT_BLUE_CONCRETE;
        }
        
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(ChatColor.AQUA + "🏰 " + dungeon.getDisplayName());
        
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        lore.add(ChatColor.YELLOW + "Creator: " + ChatColor.WHITE + dungeon.getCreator());
        lore.add(ChatColor.YELLOW + "World: " + ChatColor.WHITE + dungeon.getWorld().getName());
        lore.add(ChatColor.YELLOW + "Rooms: " + ChatColor.WHITE + dungeon.getRoomCount());
        lore.add(ChatColor.YELLOW + "Created: " + ChatColor.WHITE + DATE_FORMAT.format(new Date(dungeon.getCreationTime())));
        
        // Status
        if (dungeon.isGenerating()) {
            lore.add(ChatColor.YELLOW + "Status: " + ChatColor.GOLD + "⚠ Generating...");
        } else {
            lore.add(ChatColor.YELLOW + "Status: " + ChatColor.GREEN + "✓ Ready");
        }
        
        // Player count
        int playerCount = dungeon.getPlayers().size();
        lore.add(ChatColor.YELLOW + "Players: " + ChatColor.WHITE + playerCount + " online");
        
        lore.add(ChatColor.GRAY + "━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        
        // Actions based on ownership and permissions
        if (dungeon.getCreator().equals(player.getName()) || player.hasPermission("apexdungeons.admin")) {
            lore.add(ChatColor.GREEN + "▶ Left Click: " + ChatColor.WHITE + "Teleport");
            lore.add(ChatColor.YELLOW + "▶ Right Click: " + ChatColor.WHITE + "Management Options");
            if (player.hasPermission("apexdungeons.admin")) {
                lore.add(ChatColor.RED + "▶ Shift+Right Click: " + ChatColor.WHITE + "Force Delete");
            }
        } else {
            lore.add(ChatColor.GREEN + "▶ Left Click: " + ChatColor.WHITE + "Teleport");
            lore.add(ChatColor.GRAY + "▶ Right Click: " + ChatColor.GRAY + "View Details");
        }
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    private static void createManagementButtons(Inventory inv, Player player, ApexDungeons plugin) {
        // Create New Dungeon button
        ItemStack create = new ItemStack(Material.NETHER_STAR);
        ItemMeta createMeta = create.getItemMeta();
        createMeta.setDisplayName(ChatColor.GREEN + "✚ Create New Dungeon");
        List<String> createLore = new ArrayList<>();
        createLore.add(ChatColor.GRAY + "Start creating a new dungeon");
        createLore.add(ChatColor.GREEN + "Click to open preset selection!");
        createMeta.setLore(createLore);
        create.setItemMeta(createMeta);
        inv.setItem(1, create);
        
        // Refresh List button
        ItemStack refresh = new ItemStack(Material.LIME_DYE);
        ItemMeta refreshMeta = refresh.getItemMeta();
        refreshMeta.setDisplayName(ChatColor.GREEN + "🔄 Refresh List");
        List<String> refreshLore = new ArrayList<>();
        refreshLore.add(ChatColor.GRAY + "Update the dungeon list");
        refreshLore.add(ChatColor.GRAY + "with the latest information");
        refreshMeta.setLore(refreshLore);
        refresh.setItemMeta(refreshMeta);
        inv.setItem(3, refresh);
        
        // Sort Options button
        ItemStack sort = new ItemStack(Material.HOPPER);
        ItemMeta sortMeta = sort.getItemMeta();
        sortMeta.setDisplayName(ChatColor.YELLOW + "📋 Sort Options");
        List<String> sortLore = new ArrayList<>();
        sortLore.add(ChatColor.GRAY + "Change how dungeons are sorted");
        sortLore.add("");
        sortLore.add(ChatColor.AQUA + "• By Creation Date");
        sortLore.add(ChatColor.AQUA + "• By Name (A-Z)");
        sortLore.add(ChatColor.AQUA + "• By Creator");
        sortLore.add(ChatColor.AQUA + "• By Status");
        sortMeta.setLore(sortLore);
        sort.setItemMeta(sortMeta);
        inv.setItem(5, sort);
        
        // Admin Tools (if admin)
        if (player.hasPermission("apexdungeons.admin")) {
            ItemStack admin = new ItemStack(Material.COMMAND_BLOCK);
            ItemMeta adminMeta = admin.getItemMeta();
            adminMeta.setDisplayName(ChatColor.RED + "🔧 Admin Tools");
            List<String> adminLore = new ArrayList<>();
            adminLore.add(ChatColor.GRAY + "Administrative functions");
            adminLore.add("");
            adminLore.add(ChatColor.AQUA + "• Force delete any dungeon");
            adminLore.add(ChatColor.AQUA + "• Teleport to any dungeon");
            adminLore.add(ChatColor.AQUA + "• View detailed statistics");
            adminLore.add(ChatColor.AQUA + "• Manage server resources");
            adminMeta.setLore(adminLore);
            admin.setItemMeta(adminMeta);
            inv.setItem(7, admin);
        }
    }

    private static void createNavigationButtons(Inventory inv) {
        // Back button
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(ChatColor.RED + "← Back to Main Menu");
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        
        // Help button
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(ChatColor.AQUA + "❓ Management Help");
        List<String> helpLore = new ArrayList<>();
        helpLore.add(ChatColor.GRAY + "Learn about dungeon management");
        helpLore.add("");
        helpLore.add(ChatColor.YELLOW + "Tips:");
        helpLore.add(ChatColor.AQUA + "• Left click to teleport");
        helpLore.add(ChatColor.AQUA + "• Right click for options");
        helpLore.add(ChatColor.AQUA + "• Green = Your dungeons");
        helpLore.add(ChatColor.AQUA + "• Blue = Other players'");
        helpLore.add(ChatColor.AQUA + "• Yellow = Generating");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(53, help);
    }

    private static void registerEventListener(ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player) e.getWhoClicked();
                    int slot = e.getRawSlot();
                    
                    // Handle navigation buttons
                    switch (slot) {
                        case 1: // Create New Dungeon
                            clicker.closeInventory();
                            PresetGUI.open(clicker, plugin);
                            return;
                        case 3: // Refresh List
                            clicker.closeInventory();
                            DungeonManagementGUI.open(clicker, plugin);
                            clicker.sendMessage(ChatColor.GREEN + "Dungeon list refreshed!");
                            return;
                        case 5: // Sort Options
                            clicker.closeInventory();
                            // TODO: Implement sort options GUI
                            clicker.sendMessage(ChatColor.YELLOW + "Sort options coming soon!");
                            DungeonManagementGUI.open(clicker, plugin);
                            return;
                        case 7: // Admin Tools
                            if (clicker.hasPermission("apexdungeons.admin")) {
                                clicker.closeInventory();
                                AdminGUI.open(clicker, plugin);
                            }
                            return;
                        case 45: // Back
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            return;
                        case 53: // Help
                            clicker.closeInventory();
                            HelpGUI.open(clicker, plugin);
                            return;
                    }
                    
                    // Handle dungeon item clicks
                    ItemStack clickedItem = e.getCurrentItem();
                    if (clickedItem != null && clickedItem.hasItemMeta() && clickedItem.getItemMeta().hasDisplayName()) {
                        String displayName = clickedItem.getItemMeta().getDisplayName();
                        if (displayName.contains("🏰")) {
                            // Extract dungeon name and handle click
                            String dungeonDisplayName = ChatColor.stripColor(displayName).replace("🏰 ", "");
                            handleDungeonClick(clicker, plugin, dungeonDisplayName, e.getClick());
                        }
                    }
                }
            }
        }, plugin);
    }

    private static void handleDungeonClick(Player player, ApexDungeons plugin, String dungeonDisplayName, org.bukkit.event.inventory.ClickType clickType) {
        // Find dungeon by display name
        DungeonInstance dungeon = null;
        for (DungeonInstance d : plugin.getDungeonManager().getDungeons().values()) {
            if (d.getDisplayName().equals(dungeonDisplayName)) {
                dungeon = d;
                break;
            }
        }
        
        if (dungeon == null) {
            player.sendMessage(ChatColor.RED + "Dungeon not found!");
            return;
        }
        
        switch (clickType) {
            case LEFT:
                // Teleport to dungeon
                if (dungeon.isGenerating()) {
                    player.sendMessage(ChatColor.YELLOW + "Dungeon is still generating. Please wait...");
                    return;
                }
                
                org.bukkit.Location spawnLoc = plugin.getWorldManager().getDungeonSpawnLocation(dungeon.getName());
                if (spawnLoc != null) {
                    plugin.getEffectsManager().playDungeonEntryEffects(player, dungeon);
                    player.teleport(spawnLoc);
                    dungeon.addPlayer(player);
                } else {
                    player.sendMessage(ChatColor.RED + "Failed to find dungeon spawn location!");
                }
                break;
                
            case RIGHT:
                // Open dungeon options
                player.closeInventory();
                DungeonOptionsGUI.open(player, plugin, dungeon);
                break;
                
            case SHIFT_RIGHT:
                // Force delete (admin only)
                if (player.hasPermission("apexdungeons.admin")) {
                    player.closeInventory();
                    plugin.getDungeonManager().removeDungeon(dungeon.getName(), player);
                    player.sendMessage(ChatColor.RED + "Dungeon '" + dungeon.getDisplayName() + "' has been force deleted!");
                } else {
                    player.sendMessage(ChatColor.RED + "You don't have permission to force delete dungeons!");
                }
                break;
        }
    }
}
