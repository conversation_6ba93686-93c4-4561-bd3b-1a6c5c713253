package com.apexdungeons.saved;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Represents a saved dungeon that can be loaded and reused.
 */
public class SavedDungeon {
    private String name;
    private String originalName;
    private String description;
    private String creator;
    private long createdTime;
    private Location spawnLocation;
    private Location exitLocation;
    private List<Location> startBlocks = new ArrayList<>();
    private List<Location> endBlocks = new ArrayList<>();
    private List<Location> mobSpawns = new ArrayList<>();
    private List<Location> bossSpawns = new ArrayList<>();
    private List<Location> chestSpawns = new ArrayList<>();
    private Map<String, Object> settings = new HashMap<>();
    
    public SavedDungeon() {
        this.createdTime = System.currentTimeMillis();
    }
    
    // Getters and setters
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getOriginalName() { return originalName; }
    public void setOriginalName(String originalName) { this.originalName = originalName; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getCreator() { return creator; }
    public void setCreator(String creator) { this.creator = creator; }
    
    public long getCreatedTime() { return createdTime; }
    public void setCreatedTime(long createdTime) { this.createdTime = createdTime; }
    
    public Location getSpawnLocation() { return spawnLocation; }
    public void setSpawnLocation(Location spawnLocation) { this.spawnLocation = spawnLocation; }
    
    public Location getExitLocation() { return exitLocation; }
    public void setExitLocation(Location exitLocation) { this.exitLocation = exitLocation; }
    
    public List<Location> getStartBlocks() { return startBlocks; }
    public void setStartBlocks(List<Location> startBlocks) { this.startBlocks = startBlocks; }
    
    public List<Location> getEndBlocks() { return endBlocks; }
    public void setEndBlocks(List<Location> endBlocks) { this.endBlocks = endBlocks; }
    
    public List<Location> getMobSpawns() { return mobSpawns; }
    public void setMobSpawns(List<Location> mobSpawns) { this.mobSpawns = mobSpawns; }
    
    public List<Location> getBossSpawns() { return bossSpawns; }
    public void setBossSpawns(List<Location> bossSpawns) { this.bossSpawns = bossSpawns; }
    
    public List<Location> getChestSpawns() { return chestSpawns; }
    public void setChestSpawns(List<Location> chestSpawns) { this.chestSpawns = chestSpawns; }
    
    public Map<String, Object> getSettings() { return settings; }
    public void setSettings(Map<String, Object> settings) { this.settings = settings; }
    
    /**
     * Save to configuration section.
     */
    public void saveToConfig(ConfigurationSection config) {
        config.set("name", name);
        config.set("original_name", originalName);
        config.set("description", description);
        config.set("creator", creator);
        config.set("created_time", createdTime);
        
        // Save spawn location
        if (spawnLocation != null) {
            saveLocationToConfig(config, "spawn_location", spawnLocation);
        }
        
        // Save exit location
        if (exitLocation != null) {
            saveLocationToConfig(config, "exit_location", exitLocation);
        }
        
        // Save start blocks
        List<Map<String, Object>> startBlocksList = new ArrayList<>();
        for (Location loc : startBlocks) {
            startBlocksList.add(locationToMap(loc));
        }
        config.set("start_blocks", startBlocksList);
        
        // Save end blocks
        List<Map<String, Object>> endBlocksList = new ArrayList<>();
        for (Location loc : endBlocks) {
            endBlocksList.add(locationToMap(loc));
        }
        config.set("end_blocks", endBlocksList);
        
        // Save mob spawns
        List<Map<String, Object>> mobSpawnsList = new ArrayList<>();
        for (Location loc : mobSpawns) {
            mobSpawnsList.add(locationToMap(loc));
        }
        config.set("mob_spawns", mobSpawnsList);
        
        // Save boss spawns
        List<Map<String, Object>> bossSpawnsList = new ArrayList<>();
        for (Location loc : bossSpawns) {
            bossSpawnsList.add(locationToMap(loc));
        }
        config.set("boss_spawns", bossSpawnsList);
        
        // Save chest spawns
        List<Map<String, Object>> chestSpawnsList = new ArrayList<>();
        for (Location loc : chestSpawns) {
            chestSpawnsList.add(locationToMap(loc));
        }
        config.set("chest_spawns", chestSpawnsList);
        
        // Save settings
        for (Map.Entry<String, Object> entry : settings.entrySet()) {
            config.set("settings." + entry.getKey(), entry.getValue());
        }
    }
    
    /**
     * Load from configuration section.
     */
    public static SavedDungeon fromConfig(ConfigurationSection config) {
        SavedDungeon savedDungeon = new SavedDungeon();
        
        savedDungeon.name = config.getString("name", "Unknown");
        savedDungeon.originalName = config.getString("original_name", "");
        savedDungeon.description = config.getString("description", "");
        savedDungeon.creator = config.getString("creator", "");
        savedDungeon.createdTime = config.getLong("created_time", System.currentTimeMillis());
        
        // Load spawn location
        if (config.contains("spawn_location")) {
            savedDungeon.spawnLocation = loadLocationFromConfig(config, "spawn_location");
        }
        
        // Load exit location
        if (config.contains("exit_location")) {
            savedDungeon.exitLocation = loadLocationFromConfig(config, "exit_location");
        }
        
        // Load start blocks
        if (config.contains("start_blocks")) {
            List<Map<?, ?>> startBlocksList = config.getMapList("start_blocks");
            for (Map<?, ?> locMap : startBlocksList) {
                Location loc = mapToLocation(locMap);
                if (loc != null) {
                    savedDungeon.startBlocks.add(loc);
                }
            }
        }
        
        // Load end blocks
        if (config.contains("end_blocks")) {
            List<Map<?, ?>> endBlocksList = config.getMapList("end_blocks");
            for (Map<?, ?> locMap : endBlocksList) {
                Location loc = mapToLocation(locMap);
                if (loc != null) {
                    savedDungeon.endBlocks.add(loc);
                }
            }
        }
        
        // Load mob spawns
        if (config.contains("mob_spawns")) {
            List<Map<?, ?>> mobSpawnsList = config.getMapList("mob_spawns");
            for (Map<?, ?> locMap : mobSpawnsList) {
                Location loc = mapToLocation(locMap);
                if (loc != null) {
                    savedDungeon.mobSpawns.add(loc);
                }
            }
        }
        
        // Load boss spawns
        if (config.contains("boss_spawns")) {
            List<Map<?, ?>> bossSpawnsList = config.getMapList("boss_spawns");
            for (Map<?, ?> locMap : bossSpawnsList) {
                Location loc = mapToLocation(locMap);
                if (loc != null) {
                    savedDungeon.bossSpawns.add(loc);
                }
            }
        }
        
        // Load chest spawns
        if (config.contains("chest_spawns")) {
            List<Map<?, ?>> chestSpawnsList = config.getMapList("chest_spawns");
            for (Map<?, ?> locMap : chestSpawnsList) {
                Location loc = mapToLocation(locMap);
                if (loc != null) {
                    savedDungeon.chestSpawns.add(loc);
                }
            }
        }
        
        // Load settings
        if (config.contains("settings")) {
            ConfigurationSection settingsSection = config.getConfigurationSection("settings");
            for (String key : settingsSection.getKeys(false)) {
                savedDungeon.settings.put(key, settingsSection.get(key));
            }
        }
        
        return savedDungeon;
    }
    
    private void saveLocationToConfig(ConfigurationSection config, String path, Location location) {
        config.set(path + ".world", location.getWorld().getName());
        config.set(path + ".x", location.getX());
        config.set(path + ".y", location.getY());
        config.set(path + ".z", location.getZ());
        config.set(path + ".yaw", location.getYaw());
        config.set(path + ".pitch", location.getPitch());
    }
    
    private static Location loadLocationFromConfig(ConfigurationSection config, String path) {
        String worldName = config.getString(path + ".world");
        if (worldName == null) return null;
        
        World world = Bukkit.getWorld(worldName);
        if (world == null) return null;
        
        double x = config.getDouble(path + ".x");
        double y = config.getDouble(path + ".y");
        double z = config.getDouble(path + ".z");
        float yaw = (float) config.getDouble(path + ".yaw");
        float pitch = (float) config.getDouble(path + ".pitch");
        
        return new Location(world, x, y, z, yaw, pitch);
    }
    
    private Map<String, Object> locationToMap(Location location) {
        Map<String, Object> map = new HashMap<>();
        map.put("world", location.getWorld().getName());
        map.put("x", location.getX());
        map.put("y", location.getY());
        map.put("z", location.getZ());
        map.put("yaw", location.getYaw());
        map.put("pitch", location.getPitch());
        return map;
    }
    
    private static Location mapToLocation(Map<?, ?> map) {
        try {
            String worldName = (String) map.get("world");
            World world = Bukkit.getWorld(worldName);
            if (world == null) return null;
            
            double x = ((Number) map.get("x")).doubleValue();
            double y = ((Number) map.get("y")).doubleValue();
            double z = ((Number) map.get("z")).doubleValue();
            float yaw = ((Number) map.get("yaw")).floatValue();
            float pitch = ((Number) map.get("pitch")).floatValue();
            
            return new Location(world, x, y, z, yaw, pitch);
        } catch (Exception e) {
            return null;
        }
    }
}
