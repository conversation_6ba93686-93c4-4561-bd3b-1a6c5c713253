package com.apexdungeons.chests;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.inventory.ItemStack;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * Manages chest loot tables with weighted RNG.
 */
public class ChestLootManager {
    private final ApexDungeons plugin;
    private final Map<String, ChestLootTable> lootTables = new HashMap<>();
    
    public ChestLootManager(ApexDungeons plugin) {
        this.plugin = plugin;
        loadLootTables();
        createDefaultLootTables();
    }
    
    /**
     * Get a loot table by name.
     */
    public ChestLootTable getLootTable(String name) {
        return lootTables.get(name);
    }
    
    /**
     * Get default loot table.
     */
    public ChestLootTable getDefaultLootTable() {
        return lootTables.get("common");
    }
    
    /**
     * Add or update a loot table.
     */
    public void setLootTable(String name, ChestLootTable lootTable) {
        lootTables.put(name, lootTable);
        saveLootTables();
    }
    
    /**
     * Get all loot table names.
     */
    public Set<String> getLootTableNames() {
        return new HashSet<>(lootTables.keySet());
    }
    
    /**
     * Add item to loot table.
     */
    public boolean addItemToLootTable(String tableName, Material material, int amount, int weight) {
        ChestLootTable table = lootTables.get(tableName);
        if (table == null) {
            table = new ChestLootTable(tableName);
            lootTables.put(tableName, table);
        }
        
        table.addItem(new ItemStack(material, amount), weight);
        saveLootTables();
        return true;
    }
    
    /**
     * Remove item from loot table.
     */
    public boolean removeItemFromLootTable(String tableName, Material material) {
        ChestLootTable table = lootTables.get(tableName);
        if (table == null) return false;
        
        boolean removed = table.removeItem(material);
        if (removed) {
            saveLootTables();
        }
        return removed;
    }
    
    /**
     * Create default loot tables.
     */
    private void createDefaultLootTables() {
        if (lootTables.isEmpty()) {
            // Common loot table
            ChestLootTable common = new ChestLootTable("common");
            common.addItem(new ItemStack(Material.BREAD, 3), 50);
            common.addItem(new ItemStack(Material.APPLE, 2), 40);
            common.addItem(new ItemStack(Material.ARROW, 16), 35);
            common.addItem(new ItemStack(Material.COAL, 8), 30);
            common.addItem(new ItemStack(Material.IRON_INGOT, 2), 20);
            common.addItem(new ItemStack(Material.GOLD_INGOT, 1), 10);
            lootTables.put("common", common);
            
            // Rare loot table
            ChestLootTable rare = new ChestLootTable("rare");
            rare.addItem(new ItemStack(Material.IRON_SWORD), 25);
            rare.addItem(new ItemStack(Material.IRON_HELMET), 20);
            rare.addItem(new ItemStack(Material.IRON_CHESTPLATE), 15);
            rare.addItem(new ItemStack(Material.IRON_LEGGINGS), 15);
            rare.addItem(new ItemStack(Material.IRON_BOOTS), 20);
            rare.addItem(new ItemStack(Material.BOW), 25);
            rare.addItem(new ItemStack(Material.DIAMOND, 2), 10);
            rare.addItem(new ItemStack(Material.EMERALD, 3), 15);
            rare.addItem(new ItemStack(Material.ENCHANTED_BOOK), 8);
            lootTables.put("rare", rare);
            
            // Epic loot table
            ChestLootTable epic = new ChestLootTable("epic");
            epic.addItem(new ItemStack(Material.DIAMOND_SWORD), 20);
            epic.addItem(new ItemStack(Material.DIAMOND_HELMET), 15);
            epic.addItem(new ItemStack(Material.DIAMOND_CHESTPLATE), 10);
            epic.addItem(new ItemStack(Material.DIAMOND_LEGGINGS), 10);
            epic.addItem(new ItemStack(Material.DIAMOND_BOOTS), 15);
            epic.addItem(new ItemStack(Material.DIAMOND, 5), 25);
            epic.addItem(new ItemStack(Material.EMERALD, 8), 20);
            epic.addItem(new ItemStack(Material.ENCHANTED_BOOK), 15);
            epic.addItem(new ItemStack(Material.GOLDEN_APPLE), 12);
            epic.addItem(new ItemStack(Material.EXPERIENCE_BOTTLE, 5), 18);
            lootTables.put("epic", epic);
            
            // Boss rewards loot table
            ChestLootTable bossRewards = new ChestLootTable("boss_rewards");
            bossRewards.addItem(new ItemStack(Material.NETHERITE_SCRAP), 15);
            bossRewards.addItem(new ItemStack(Material.DIAMOND, 10), 25);
            bossRewards.addItem(new ItemStack(Material.EMERALD, 15), 20);
            bossRewards.addItem(new ItemStack(Material.ENCHANTED_GOLDEN_APPLE), 8);
            bossRewards.addItem(new ItemStack(Material.ENCHANTED_BOOK), 20);
            bossRewards.addItem(new ItemStack(Material.EXPERIENCE_BOTTLE, 10), 15);
            bossRewards.addItem(new ItemStack(Material.TOTEM_OF_UNDYING), 5);
            bossRewards.addItem(new ItemStack(Material.ELYTRA), 3);
            bossRewards.addItem(new ItemStack(Material.BEACON), 2);
            lootTables.put("boss_rewards", bossRewards);
            
            saveLootTables();
            plugin.getLogger().info("Created default loot tables: common, rare, epic, boss_rewards");
        }
    }
    
    /**
     * Load loot tables from file.
     */
    private void loadLootTables() {
        File file = new File(plugin.getDataFolder(), "chest_loot_tables.yml");
        if (!file.exists()) return;
        
        FileConfiguration config = YamlConfiguration.loadConfiguration(file);
        
        for (String tableName : config.getKeys(false)) {
            try {
                ChestLootTable table = new ChestLootTable(tableName);
                
                if (config.contains(tableName + ".items")) {
                    List<Map<?, ?>> items = config.getMapList(tableName + ".items");
                    for (Map<?, ?> itemMap : items) {
                        String materialName = (String) itemMap.get("material");
                        int amount = (Integer) itemMap.get("amount");
                        int weight = (Integer) itemMap.get("weight");
                        
                        Material material = Material.valueOf(materialName);
                        table.addItem(new ItemStack(material, amount), weight);
                    }
                }
                
                lootTables.put(tableName, table);
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to load loot table '" + tableName + "': " + e.getMessage());
            }
        }
        
        plugin.getLogger().info("Loaded " + lootTables.size() + " chest loot tables");
    }
    
    /**
     * Save loot tables to file.
     */
    private void saveLootTables() {
        File file = new File(plugin.getDataFolder(), "chest_loot_tables.yml");
        FileConfiguration config = new YamlConfiguration();
        
        for (Map.Entry<String, ChestLootTable> entry : lootTables.entrySet()) {
            String tableName = entry.getKey();
            ChestLootTable table = entry.getValue();
            
            List<Map<String, Object>> items = new ArrayList<>();
            for (ChestLootTable.WeightedItem weightedItem : table.getItems()) {
                Map<String, Object> itemMap = new HashMap<>();
                itemMap.put("material", weightedItem.getItem().getType().name());
                itemMap.put("amount", weightedItem.getItem().getAmount());
                itemMap.put("weight", weightedItem.getWeight());
                items.add(itemMap);
            }
            
            config.set(tableName + ".items", items);
        }
        
        try {
            config.save(file);
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save chest loot tables: " + e.getMessage());
        }
    }
    
    /**
     * Shutdown and save data.
     */
    public void shutdown() {
        saveLootTables();
        lootTables.clear();
    }
}
