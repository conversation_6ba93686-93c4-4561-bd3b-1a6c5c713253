package org.bukkit.inventory;

import org.bukkit.entity.Player;

/**
 * Stub for Inventory.  Provides methods for setting items but does not
 * implement full Bukkit inventory behaviour.
 */
public class Inventory {
    private final int size;
    private final ItemStack[] contents;
    private final String title;
    public Inventory(Player owner, int size, String title) {
        this.size = size;
        this.contents = new ItemStack[size];
        this.title = title;
    }
    public int getSize() { return size; }
    public String getTitle() { return title; }
    public void setItem(int index, ItemStack item) {
        if (index >= 0 && index < size) contents[index] = item;
    }
    public ItemStack getItem(int index) {
        return (index >= 0 && index < size) ? contents[index] : null;
    }

    /**
     * Add one or more items to the inventory.  This naive implementation will
     * fill the first available empty slots and return any leftover items.
     * @param items items to add
     * @return map of slot -> item that could not be added
     */
    public java.util.Map<Integer, ItemStack> addItem(ItemStack... items) {
        java.util.Map<Integer, ItemStack> remaining = new java.util.HashMap<>();
        int slot = 0;
        for (ItemStack item : items) {
            boolean placed = false;
            for (; slot < size; slot++) {
                if (contents[slot] == null) {
                    contents[slot] = item;
                    placed = true;
                    slot++;
                    break;
                }
            }
            if (!placed) {
                remaining.put(-1, item);
            }
        }
        return remaining;
    }
}