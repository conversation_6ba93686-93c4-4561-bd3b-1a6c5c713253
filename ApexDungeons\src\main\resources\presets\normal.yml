# Normal dungeon preset - creates empty superflat world for custom building
name: "normal"
description: "Empty superflat world for custom dungeon building"

# Generation parameters - minimal for normal dungeons
generation:
  min_rooms: 0
  max_rooms: 0
  room_spacing: 0
  corridor_width: 0
  vertical_variation: false
  multi_level: false
  max_levels: 1
  natural_generation: false
  empty_world: true  # Special flag for normal dungeons

# No room types for normal dungeons
room_types: {}

# No blueprints for normal dungeons - completely empty
blueprints: []

# Theme settings for normal dungeons
theme:
  name: "normal"
  primary_material: "GRASS_BLOCK"
  secondary_material: "DIRT"
  accent_material: "STONE"
  lighting: "NONE"
  atmosphere: "peaceful"

# No mob spawning for normal dungeons by default
mobs:
  enabled: false
  density: "none"
  types: []

# No boss for normal dungeons
boss:
  enabled: false

# World generation settings
world:
  type: "FLAT"
  generate_structures: false
  spawn_height: 64
  surface_material: "GRASS_BLOCK"
  subsurface_material: "DIRT"
  base_material: "STONE"
