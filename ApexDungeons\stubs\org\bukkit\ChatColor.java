package org.bukkit;

/**
 * Minimal stub for ChatColor used for colouring text.  All values return an
 * empty string so that strings remain unaffected at compile time.  At runtime
 * the real Bukkit ChatColor enum will be used.
 */
public enum ChatColor {
    BLACK(""), DARK_BLUE(""), DARK_GREEN(""), DARK_AQUA(""), DARK_RED(""),
    DARK_PURPLE(""), GOLD(""), GRAY(""), DARK_GRAY(""), BLUE(""),
    GREEN(""), AQUA(""), RED(""), LIGHT_PURPLE(""), YELLOW(""), WHITE(""),
    MAGIC(""), BOLD(""), STRIKETHROUGH(""), UNDERLINE(""), ITALIC(""), RESET("");

    private final String code;
    ChatColor(String code) { this.code = code; }
    @Override public String toString() { return code; }
}