package com.apexdungeons.party;

import com.apexdungeons.ApexDungeons;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

import java.util.*;

/**
 * Manages party creation, invitations, and operations.
 */
public class PartyManager {
    private final ApexDungeons plugin;
    private final Map<UUID, Party> parties = new HashMap<>();
    private final Map<UUID, Party> playerParties = new HashMap<>();
    private final Map<UUID, Set<UUID>> pendingInvites = new HashMap<>(); // Player -> Set of party IDs

    public PartyManager(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    /**
     * Create a new party with the player as leader.
     */
    public Party createParty(Player leader) {
        // Check if player is already in a party
        if (isInParty(leader)) {
            return null;
        }

        Party party = new Party(leader);
        parties.put(party.getPartyId(), party);
        playerParties.put(leader.getUniqueId(), party);
        
        plugin.getLogger().info("Created party " + party.getPartyId() + " with leader " + leader.getName());
        return party;
    }

    /**
     * Invite a player to a party.
     */
    public boolean invitePlayer(Party party, Player inviter, Player target) {
        // Validate inviter is party leader
        if (!party.isLeader(inviter)) {
            return false;
        }

        // Check if party is full
        if (party.isFull()) {
            return false;
        }

        // Check if target is already in a party
        if (isInParty(target)) {
            return false;
        }

        // Check if target already has a pending invite from this party
        Set<UUID> targetInvites = pendingInvites.computeIfAbsent(target.getUniqueId(), k -> new HashSet<>());
        if (targetInvites.contains(party.getPartyId())) {
            return false;
        }

        // Add pending invite
        targetInvites.add(party.getPartyId());
        
        // Send invitation messages
        target.sendMessage(ChatColor.GREEN + "🎉 Party Invitation!");
        target.sendMessage(ChatColor.YELLOW + "You've been invited to join " + inviter.getName() + "'s party!");
        target.sendMessage(ChatColor.AQUA + "Type " + ChatColor.WHITE + "/dgn party accept " + inviter.getName() + 
                          ChatColor.AQUA + " to accept");
        target.sendMessage(ChatColor.AQUA + "Type " + ChatColor.WHITE + "/dgn party decline " + inviter.getName() + 
                          ChatColor.AQUA + " to decline");
        
        // Notify party members
        for (Player member : party.getMembers()) {
            member.sendMessage(ChatColor.GREEN + "Invited " + target.getName() + " to the party!");
        }
        
        plugin.getLogger().info("Player " + inviter.getName() + " invited " + target.getName() + " to party " + party.getPartyId());
        return true;
    }

    /**
     * Accept a party invitation.
     */
    public boolean acceptInvite(Player player, String leaderName) {
        // Find the party by leader name
        Party targetParty = null;
        for (Party party : parties.values()) {
            if (party.getLeader().getName().equalsIgnoreCase(leaderName)) {
                Set<UUID> playerInvites = pendingInvites.get(player.getUniqueId());
                if (playerInvites != null && playerInvites.contains(party.getPartyId())) {
                    targetParty = party;
                    break;
                }
            }
        }

        if (targetParty == null) {
            return false;
        }

        // Check if player is already in a party
        if (isInParty(player)) {
            return false;
        }

        // Check if party is full
        if (targetParty.isFull()) {
            return false;
        }

        // Add player to party
        if (targetParty.addMember(player)) {
            playerParties.put(player.getUniqueId(), targetParty);
            
            // Remove pending invite
            Set<UUID> playerInvites = pendingInvites.get(player.getUniqueId());
            if (playerInvites != null) {
                playerInvites.remove(targetParty.getPartyId());
                if (playerInvites.isEmpty()) {
                    pendingInvites.remove(player.getUniqueId());
                }
            }
            
            // Notify all party members
            for (Player member : targetParty.getMembers()) {
                member.sendMessage(ChatColor.GREEN + "🎉 " + player.getName() + " joined the party!");
            }
            
            plugin.getLogger().info("Player " + player.getName() + " joined party " + targetParty.getPartyId());
            return true;
        }

        return false;
    }

    /**
     * Decline a party invitation.
     */
    public boolean declineInvite(Player player, String leaderName) {
        // Find the party by leader name
        Party targetParty = null;
        for (Party party : parties.values()) {
            if (party.getLeader().getName().equalsIgnoreCase(leaderName)) {
                Set<UUID> playerInvites = pendingInvites.get(player.getUniqueId());
                if (playerInvites != null && playerInvites.contains(party.getPartyId())) {
                    targetParty = party;
                    break;
                }
            }
        }

        if (targetParty == null) {
            return false;
        }

        // Remove pending invite
        Set<UUID> playerInvites = pendingInvites.get(player.getUniqueId());
        if (playerInvites != null) {
            playerInvites.remove(targetParty.getPartyId());
            if (playerInvites.isEmpty()) {
                pendingInvites.remove(player.getUniqueId());
            }
        }

        // Notify party leader
        targetParty.getLeader().sendMessage(ChatColor.YELLOW + player.getName() + " declined the party invitation.");
        player.sendMessage(ChatColor.YELLOW + "You declined the party invitation from " + targetParty.getLeader().getName());
        
        return true;
    }

    /**
     * Leave a party.
     */
    public boolean leaveParty(Player player) {
        Party party = getPlayerParty(player);
        if (party == null) {
            return false;
        }

        if (party.isLeader(player)) {
            // Disband party if leader leaves
            disbandParty(party);
        } else {
            // Remove member
            party.removeMember(player);
            playerParties.remove(player.getUniqueId());
            
            // Notify remaining members
            for (Player member : party.getMembers()) {
                member.sendMessage(ChatColor.YELLOW + player.getName() + " left the party.");
            }
            
            player.sendMessage(ChatColor.YELLOW + "You left the party.");
        }
        
        return true;
    }

    /**
     * Disband a party.
     */
    public void disbandParty(Party party) {
        // Notify all members
        for (Player member : party.getMembers()) {
            member.sendMessage(ChatColor.RED + "Party has been disbanded.");
            playerParties.remove(member.getUniqueId());
        }
        
        // Remove party
        parties.remove(party.getPartyId());
        
        // Remove any pending invites for this party
        pendingInvites.values().forEach(invites -> invites.remove(party.getPartyId()));
        
        plugin.getLogger().info("Disbanded party " + party.getPartyId());
    }

    /**
     * Kick a player from party.
     */
    public boolean kickPlayer(Player kicker, Player target) {
        Party party = getPlayerParty(kicker);
        if (party == null || !party.isLeader(kicker)) {
            return false;
        }

        if (!party.hasMember(target) || party.isLeader(target)) {
            return false;
        }

        party.removeMember(target);
        playerParties.remove(target.getUniqueId());
        
        // Notify members
        for (Player member : party.getMembers()) {
            member.sendMessage(ChatColor.YELLOW + target.getName() + " was kicked from the party.");
        }
        
        target.sendMessage(ChatColor.RED + "You were kicked from the party.");
        
        return true;
    }

    /**
     * Get player's current party.
     */
    public Party getPlayerParty(Player player) {
        return playerParties.get(player.getUniqueId());
    }

    /**
     * Check if player is in a party.
     */
    public boolean isInParty(Player player) {
        return playerParties.containsKey(player.getUniqueId());
    }

    /**
     * Get all parties.
     */
    public Collection<Party> getAllParties() {
        return parties.values();
    }

    /**
     * Get pending invites for a player.
     */
    public Set<UUID> getPendingInvites(Player player) {
        return pendingInvites.getOrDefault(player.getUniqueId(), new HashSet<>());
    }

    /**
     * Clean up expired invites and empty parties.
     */
    public void cleanup() {
        // Remove empty parties (shouldn't happen normally, but safety check)
        parties.entrySet().removeIf(entry -> {
            Party party = entry.getValue();
            if (party.isEmpty()) {
                plugin.getLogger().info("Removing empty party: " + party.getPartyId());
                return true;
            }
            return false;
        });
        
        // Clean up pending invites for offline players
        pendingInvites.entrySet().removeIf(entry -> {
            UUID playerId = entry.getKey();
            Player player = plugin.getServer().getPlayer(playerId);
            return player == null || !player.isOnline();
        });
    }

    /**
     * Shutdown party manager.
     */
    public void shutdown() {
        // Disband all parties
        for (Party party : new ArrayList<>(parties.values())) {
            disbandParty(party);
        }
        
        parties.clear();
        playerParties.clear();
        pendingInvites.clear();
    }
}
