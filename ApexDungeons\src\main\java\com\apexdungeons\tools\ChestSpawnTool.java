package com.apexdungeons.tools;

import com.apexdungeons.ApexDungeons;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;

import java.util.ArrayList;
import java.util.List;

/**
 * Tool for placing chest spawn points in dungeons.
 * Players can right-click blocks to set chest spawn triggers.
 */
public class ChestSpawnTool {
    private final ApexDungeons plugin;
    private final NamespacedKey toolKey;

    public ChestSpawnTool(ApexDungeons plugin) {
        this.plugin = plugin;
        this.toolKey = new NamespacedKey(plugin, "chest_spawn_tool");
    }

    /**
     * Create a chest spawn tool item.
     */
    public ItemStack createChestSpawnTool() {
        ItemStack tool = new ItemStack(Material.CHEST);
        ItemMeta meta = tool.getItemMeta();
        
        meta.setDisplayName(ChatColor.GOLD + "Chest Spawn Tool");
        
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "Right-click blocks to set chest spawn points");
        lore.add(ChatColor.GRAY + "Use /dgn chestspawn set <loot_table> to configure");
        lore.add("");
        lore.add(ChatColor.YELLOW + "📍 How to use:");
        lore.add(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Right-click a block to mark chest spawn");
        lore.add(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Use /dgn chestspawn set <loot_table>");
        lore.add(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Configure loot with weighted RNG");
        lore.add(ChatColor.AQUA + "4. " + ChatColor.WHITE + "Players find chests when exploring");
        lore.add("");
        lore.add(ChatColor.GREEN + "💰 Features:");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Weighted item spawning");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Custom loot tables");
        lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Configurable spawn rates");
        lore.add("");
        lore.add(ChatColor.GREEN + "💡 Example loot tables:");
        lore.add(ChatColor.GRAY + "common, rare, epic, boss_rewards");
        
        meta.setLore(lore);
        
        // Mark as chest spawn tool
        meta.getPersistentDataContainer().set(toolKey, PersistentDataType.BYTE, (byte) 1);
        
        tool.setItemMeta(meta);
        return tool;
    }

    /**
     * Check if an item is a chest spawn tool.
     */
    public boolean isChestSpawnTool(ItemStack item) {
        if (item == null || !item.hasItemMeta()) return false;
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().has(toolKey, PersistentDataType.BYTE);
    }

    /**
     * Give a chest spawn tool to a player.
     */
    public void giveChestSpawnTool(Player player) {
        player.getInventory().addItem(createChestSpawnTool());
    }
}
