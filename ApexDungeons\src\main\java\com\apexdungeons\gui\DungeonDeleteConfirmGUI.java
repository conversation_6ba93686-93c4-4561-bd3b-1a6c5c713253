package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * Confirmation GUI for dungeon deletion with safety checks and warnings.
 */
public class DungeonDeleteConfirmGUI {
    private static final String GUI_NAME = ChatColor.DARK_RED + "⚠ Confirm Deletion";

    public static void open(Player player, ApexDungeons plugin, DungeonInstance dungeon) {
        Inventory inv = Bukkit.createInventory(player, 27, GUI_NAME);
        
        // Fill background with warning colors
        fillBackground(inv);
        
        // Create warning display
        createWarningDisplay(inv, dungeon);
        
        // Create confirmation buttons
        createConfirmationButtons(inv, plugin, dungeon);
        
        player.openInventory(inv);
        
        // Register event listener
        registerEventListener(plugin, dungeon);
    }

    private static void fillBackground(Inventory inv) {
        ItemStack warning = new ItemStack(Material.RED_STAINED_GLASS_PANE);
        ItemMeta warningMeta = warning.getItemMeta();
        warningMeta.setDisplayName(" ");
        warning.setItemMeta(warningMeta);
        
        ItemStack danger = new ItemStack(Material.ORANGE_STAINED_GLASS_PANE);
        ItemMeta dangerMeta = danger.getItemMeta();
        dangerMeta.setDisplayName(" ");
        danger.setItemMeta(dangerMeta);
        
        // Fill with alternating warning colors
        for (int i = 0; i < 27; i++) {
            if (i % 2 == 0) {
                inv.setItem(i, warning);
            } else {
                inv.setItem(i, danger);
            }
        }
    }

    private static void createWarningDisplay(Inventory inv, DungeonInstance dungeon) {
        // Main warning item
        ItemStack warning = new ItemStack(Material.BARRIER);
        ItemMeta warningMeta = warning.getItemMeta();
        warningMeta.setDisplayName(ChatColor.RED + "⚠ DELETION WARNING ⚠");
        List<String> warningLore = new ArrayList<>();
        warningLore.add(ChatColor.GRAY + "━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        warningLore.add(ChatColor.RED + "You are about to PERMANENTLY DELETE:");
        warningLore.add("");
        warningLore.add(ChatColor.YELLOW + "Dungeon: " + ChatColor.WHITE + dungeon.getDisplayName());
        warningLore.add(ChatColor.YELLOW + "Creator: " + ChatColor.WHITE + dungeon.getCreator());
        warningLore.add(ChatColor.YELLOW + "World: " + ChatColor.WHITE + dungeon.getWorld().getName());
        warningLore.add("");
        warningLore.add(ChatColor.RED + "THIS ACTION WILL:");
        warningLore.add(ChatColor.DARK_RED + "• Delete the entire dungeon world");
        warningLore.add(ChatColor.DARK_RED + "• Remove all portals");
        warningLore.add(ChatColor.DARK_RED + "• Kick all current players");
        warningLore.add(ChatColor.DARK_RED + "• Permanently destroy all data");
        warningLore.add("");
        warningLore.add(ChatColor.RED + "⚠ THIS CANNOT BE UNDONE! ⚠");
        warningLore.add(ChatColor.GRAY + "━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        warningMeta.setLore(warningLore);
        warning.setItemMeta(warningMeta);
        inv.setItem(13, warning);
        
        // Player count warning
        if (!dungeon.getPlayers().isEmpty()) {
            ItemStack playerWarning = new ItemStack(Material.PLAYER_HEAD);
            ItemMeta playerMeta = playerWarning.getItemMeta();
            playerMeta.setDisplayName(ChatColor.GOLD + "⚠ Active Players Warning");
            List<String> playerLore = new ArrayList<>();
            playerLore.add(ChatColor.GRAY + "There are currently players in this dungeon!");
            playerLore.add("");
            playerLore.add(ChatColor.YELLOW + "Active Players: " + ChatColor.WHITE + dungeon.getPlayers().size());
            playerLore.add("");
            playerLore.add(ChatColor.RED + "Deleting will immediately kick all players");
            playerLore.add(ChatColor.RED + "and teleport them to the main world spawn.");
            playerMeta.setLore(playerLore);
            playerWarning.setItemMeta(playerMeta);
            inv.setItem(4, playerWarning);
        }
    }

    private static void createConfirmationButtons(Inventory inv, ApexDungeons plugin, DungeonInstance dungeon) {
        // Cancel button (safe option)
        ItemStack cancel = new ItemStack(Material.LIME_DYE);
        ItemMeta cancelMeta = cancel.getItemMeta();
        cancelMeta.setDisplayName(ChatColor.GREEN + "✓ CANCEL - Keep Dungeon");
        List<String> cancelLore = new ArrayList<>();
        cancelLore.add(ChatColor.GRAY + "Return to dungeon options");
        cancelLore.add(ChatColor.GRAY + "without deleting anything");
        cancelLore.add("");
        cancelLore.add(ChatColor.GREEN + "▶ Click to cancel deletion");
        cancelMeta.setLore(cancelLore);
        cancel.setItemMeta(cancelMeta);
        inv.setItem(10, cancel);
        
        // Confirm delete button (dangerous option)
        ItemStack confirm = new ItemStack(Material.TNT);
        ItemMeta confirmMeta = confirm.getItemMeta();
        confirmMeta.setDisplayName(ChatColor.DARK_RED + "💥 CONFIRM DELETE");
        List<String> confirmLore = new ArrayList<>();
        confirmLore.add(ChatColor.RED + "PERMANENTLY DELETE this dungeon");
        confirmLore.add("");
        confirmLore.add(ChatColor.DARK_RED + "⚠ FINAL WARNING ⚠");
        confirmLore.add(ChatColor.RED + "This action is IRREVERSIBLE!");
        confirmLore.add(ChatColor.RED + "All data will be LOST FOREVER!");
        confirmLore.add("");
        confirmLore.add(ChatColor.YELLOW + "Only click if you are absolutely sure!");
        confirmLore.add("");
        confirmLore.add(ChatColor.DARK_RED + "▶ Click to DELETE PERMANENTLY");
        confirmMeta.setLore(confirmLore);
        confirm.setItemMeta(confirmMeta);
        inv.setItem(16, confirm);
        
        // Information item
        ItemStack info = new ItemStack(Material.BOOK);
        ItemMeta infoMeta = info.getItemMeta();
        infoMeta.setDisplayName(ChatColor.YELLOW + "ℹ Deletion Information");
        List<String> infoLore = new ArrayList<>();
        infoLore.add(ChatColor.GRAY + "What happens when you delete:");
        infoLore.add("");
        infoLore.add(ChatColor.AQUA + "1. " + ChatColor.WHITE + "All players are safely teleported out");
        infoLore.add(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Portal connections are removed");
        infoLore.add(ChatColor.AQUA + "3. " + ChatColor.WHITE + "World files are deleted from disk");
        infoLore.add(ChatColor.AQUA + "4. " + ChatColor.WHITE + "Dungeon is removed from memory");
        infoLore.add("");
        infoLore.add(ChatColor.GRAY + "The deletion process is safe and will");
        infoLore.add(ChatColor.GRAY + "not affect other dungeons or the");
        infoLore.add(ChatColor.GRAY + "main server world.");
        infoMeta.setLore(infoLore);
        info.setItemMeta(infoMeta);
        inv.setItem(22, info);
    }

    private static void registerEventListener(ApexDungeons plugin, DungeonInstance dungeon) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player) e.getWhoClicked();
                    int slot = e.getRawSlot();
                    
                    switch (slot) {
                        case 10: // Cancel
                            clicker.closeInventory();
                            DungeonOptionsGUI.open(clicker, plugin, dungeon);
                            clicker.sendMessage(ChatColor.GREEN + "Deletion cancelled. Dungeon is safe!");
                            break;
                            
                        case 16: // Confirm Delete
                            clicker.closeInventory();
                            
                            // Perform the deletion
                            String dungeonName = dungeon.getName();
                            String displayName = dungeon.getDisplayName();
                            
                            // Show deletion in progress message
                            clicker.sendMessage(ChatColor.YELLOW + "Deleting dungeon '" + displayName + "'...");
                            
                            // Remove the dungeon (this handles player teleportation and cleanup)
                            plugin.getDungeonManager().removeDungeon(dungeonName, clicker);
                            
                            // Show success message
                            clicker.sendMessage(ChatColor.GREEN + "Dungeon '" + displayName + "' has been permanently deleted.");
                            clicker.sendMessage(ChatColor.GRAY + "All players have been safely teleported out.");
                            
                            // Return to main dungeon management
                            DungeonManagementGUI.open(clicker, plugin);
                            break;
                            
                        default:
                            // Clicking anywhere else does nothing (safety measure)
                            break;
                    }
                }
            }
        }, plugin);
    }
}
