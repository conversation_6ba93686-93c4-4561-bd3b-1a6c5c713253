package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * GUI for naming dungeons before creation. Allows players to input custom names
 * and select presets for their dungeons.
 */
public class NamingGUI {
    private static final String GUI_NAME = ChatColor.DARK_GRAY + "Name Your Dungeon";
    private static final Map<UUID, String> awaitingInput = new HashMap<>();
    private static final Map<UUID, String> selectedPresets = new HashMap<>();

    public static void open(Player player, ApexDungeons plugin, String presetName) {
        selectedPresets.put(player.getUniqueId(), presetName);
        openGUI(player, plugin);
    }

    public static void open(Player player, ApexDungeons plugin) {
        selectedPresets.put(player.getUniqueId(), "default"); // Use default preset
        openGUI(player, plugin);
    }

    private static void openGUI(Player player, ApexDungeons plugin) {
        
        Inventory inv = Bukkit.createInventory(player, 27, GUI_NAME);
        
        // Name input button
        ItemStack nameInput = new ItemStack(Material.NAME_TAG);
        ItemMeta nameMeta = nameInput.getItemMeta();
        nameMeta.setDisplayName(ChatColor.GREEN + "Enter Custom Name");
        List<String> nameLore = new ArrayList<>();
        nameLore.add(ChatColor.GRAY + "Click to enter a custom name");
        nameLore.add(ChatColor.GRAY + "in chat for your dungeon");
        nameLore.add(ChatColor.YELLOW + "Names must be 3-32 characters");
        nameLore.add(ChatColor.YELLOW + "Letters, numbers, spaces, - and _ only");
        nameMeta.setLore(nameLore);
        nameInput.setItemMeta(nameMeta);
        inv.setItem(11, nameInput);
        
        // Auto-generate name button
        ItemStack autoName = new ItemStack(Material.PAPER);
        ItemMeta autoMeta = autoName.getItemMeta();
        autoMeta.setDisplayName(ChatColor.AQUA + "Auto-Generate Name");
        List<String> autoLore = new ArrayList<>();
        autoLore.add(ChatColor.GRAY + "Let the system create a");
        autoLore.add(ChatColor.GRAY + "unique name for your dungeon");
        autoMeta.setLore(autoLore);
        autoName.setItemMeta(autoMeta);
        inv.setItem(13, autoName);
        
        // Preset info
        String currentPreset = selectedPresets.get(player.getUniqueId());
        ItemStack presetInfo = new ItemStack(Material.BOOK);
        ItemMeta presetMeta = presetInfo.getItemMeta();
        presetMeta.setDisplayName(ChatColor.GOLD + "Dungeon Type: " + (currentPreset != null ? currentPreset.toUpperCase() : "DEFAULT"));
        List<String> presetLore = new ArrayList<>();
        presetLore.add(ChatColor.GRAY + "Creating a flat dungeon world");
        presetLore.add(ChatColor.GRAY + "perfect for custom building");
        presetMeta.setLore(presetLore);
        presetInfo.setItemMeta(presetMeta);
        inv.setItem(15, presetInfo);
        
        // Back button
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(ChatColor.RED + "Back");
        back.setItemMeta(backMeta);
        inv.setItem(22, back);
        
        player.openInventory(inv);
        
        // Register event listener
        JavaPlugin pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player) e.getWhoClicked();
                    int slot = e.getRawSlot();
                    
                    switch (slot) {
                        case 11: // Custom name input
                            clicker.closeInventory();
                            awaitingInput.put(clicker.getUniqueId(), selectedPresets.get(clicker.getUniqueId()));
                            clicker.sendMessage(ChatColor.GREEN + "Enter your dungeon name in chat:");
                            clicker.sendMessage(ChatColor.YELLOW + "Type 'cancel' to cancel naming");
                            break;
                        case 13: // Auto-generate name
                            clicker.closeInventory();
                            String preset = selectedPresets.get(clicker.getUniqueId());
                            plugin.getDungeonManager().createDungeonWithName(null, preset, clicker);
                            selectedPresets.remove(clicker.getUniqueId());
                            break;
                        case 15: // Change preset
                            clicker.closeInventory();
                            PresetGUI.open(clicker, plugin);
                            selectedPresets.remove(clicker.getUniqueId());
                            break;
                        case 22: // Back
                            clicker.closeInventory();
                            MainGUI.open(clicker, plugin);
                            selectedPresets.remove(clicker.getUniqueId());
                            break;
                    }
                }
            }
            
            @EventHandler
            public void onChat(AsyncPlayerChatEvent e) {
                Player player = e.getPlayer();
                if (awaitingInput.containsKey(player.getUniqueId())) {
                    e.setCancelled(true);
                    String message = e.getMessage().trim();
                    String preset = awaitingInput.remove(player.getUniqueId());
                    
                    if (message.equalsIgnoreCase("cancel")) {
                        player.sendMessage(ChatColor.YELLOW + "Dungeon naming cancelled.");
                        Bukkit.getScheduler().runTask(plugin, () -> MainGUI.open(player, plugin));
                        return;
                    }
                    
                    // Validate and create dungeon
                    Bukkit.getScheduler().runTask(plugin, () -> {
                        if (plugin.getDungeonManager().isValidDungeonName(message)) {
                            plugin.getDungeonManager().createDungeonWithName(message, preset, player);
                        } else {
                            player.sendMessage(ChatColor.RED + "Invalid name! Please try again or use /dgn to return to the menu.");
                            player.sendMessage(ChatColor.YELLOW + "Names must be 3-32 characters, alphanumeric with spaces, hyphens, and underscores only.");
                        }
                    });
                }
            }
        }, plugin);
    }
    
    /**
     * Check if a player is currently in naming mode
     */
    public static boolean isAwaitingInput(Player player) {
        return awaitingInput.containsKey(player.getUniqueId());
    }
    
    /**
     * Cancel naming input for a player
     */
    public static void cancelInput(Player player) {
        awaitingInput.remove(player.getUniqueId());
        selectedPresets.remove(player.getUniqueId());
    }
}
