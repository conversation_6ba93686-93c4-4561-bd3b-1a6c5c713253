package com.apexdungeons.integration;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

/**
 * Default mob adapter that spawns vanilla Bukkit entities.  Bosses defined in
 * bosses.yml are simply spawned as their underlying entity type with no
 * modifications.
 */
public class VanillaAdapter implements MobAdapter {
    @Override
    public LivingEntity spawnMob(String mobName, Location location) {
        if (mobName == null || location == null) {
            System.out.println("[ApexDungeons] VanillaAdapter: Null mobName or location");
            return null;
        }

        try {
            // Handle both direct entity names and prefixed names
            String entityName = mobName;
            if (mobName.contains(":")) {
                String[] parts = mobName.split(":");
                if (parts.length == 2) {
                    if (parts[0].equalsIgnoreCase("VANILLA") || parts[0].equalsIgnoreCase("minecraft")) {
                        entityName = parts[1];
                    } else {
                        // Try the full name first, then just the second part
                        entityName = parts[1];
                    }
                }
            }

            // Try common mob name mappings first
            entityName = mapCommonMobNames(entityName);

            System.out.println("[ApexDungeons] VanillaAdapter: Attempting to spawn " + entityName + " at " + location);

            EntityType entityType = EntityType.valueOf(entityName.toUpperCase());
            if (entityType.isAlive()) {
                LivingEntity entity = (LivingEntity) location.getWorld().spawnEntity(location, entityType);
                System.out.println("[ApexDungeons] VanillaAdapter: Successfully spawned " + entityName);
                return entity;
            } else {
                System.out.println("[ApexDungeons] VanillaAdapter: EntityType " + entityName + " is not alive");
            }
        } catch (IllegalArgumentException e) {
            System.out.println("[ApexDungeons] VanillaAdapter: Invalid entity type: " + mobName + " -> " + e.getMessage());
            // Try fallback spawning
            return tryFallbackSpawning(mobName, location);
        } catch (Exception e) {
            System.out.println("[ApexDungeons] VanillaAdapter: Error spawning mob: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * Map common mob names to proper EntityType names.
     */
    private String mapCommonMobNames(String mobName) {
        String name = mobName.toLowerCase();
        switch (name) {
            case "zombie": return "ZOMBIE";
            case "skeleton": return "SKELETON";
            case "spider": return "SPIDER";
            case "creeper": return "CREEPER";
            case "enderman": return "ENDERMAN";
            case "witch": return "WITCH";
            case "slime": return "SLIME";
            case "magma_cube": case "magmacube": return "MAGMA_CUBE";
            case "blaze": return "BLAZE";
            case "ghast": return "GHAST";
            case "piglin": return "PIGLIN";
            case "zombified_piglin": case "zombie_pigman": return "ZOMBIFIED_PIGLIN";
            case "wither_skeleton": return "WITHER_SKELETON";
            case "stray": return "STRAY";
            case "husk": return "HUSK";
            case "drowned": return "DROWNED";
            case "phantom": return "PHANTOM";
            case "vindicator": return "VINDICATOR";
            case "evoker": return "EVOKER";
            case "pillager": return "PILLAGER";
            case "ravager": return "RAVAGER";
            case "vex": return "VEX";
            default: return mobName.toUpperCase();
        }
    }

    /**
     * Try fallback spawning with common mob types.
     */
    private LivingEntity tryFallbackSpawning(String mobName, Location location) {
        System.out.println("[ApexDungeons] VanillaAdapter: Trying fallback spawning for " + mobName);

        // Try spawning a zombie as fallback
        try {
            LivingEntity entity = (LivingEntity) location.getWorld().spawnEntity(location, EntityType.ZOMBIE);
            System.out.println("[ApexDungeons] VanillaAdapter: Fallback spawned ZOMBIE for " + mobName);
            return entity;
        } catch (Exception e) {
            System.out.println("[ApexDungeons] VanillaAdapter: Even fallback spawning failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public LivingEntity spawnBoss(String bossName, Location location) {
        if (bossName == null || location == null) {
            System.out.println("[ApexDungeons] VanillaAdapter: Null bossName or location for boss spawn");
            return null;
        }

        System.out.println("[ApexDungeons] VanillaAdapter: Attempting to spawn boss " + bossName);

        // For vanilla adapter, treat bosses as enhanced regular mobs
        LivingEntity boss = null;

        // Try common boss mobs first
        switch (bossName.toLowerCase()) {
            case "wither":
            case "wither_boss":
                boss = spawnMob("WITHER", location);
                break;
            case "ender_dragon":
            case "dragon":
                boss = spawnMob("ENDER_DRAGON", location);
                break;
            case "elder_guardian":
                boss = spawnMob("ELDER_GUARDIAN", location);
                break;
            case "warden":
                boss = spawnMob("WARDEN", location);
                break;
            case "wither_skeleton":
                boss = spawnMob("WITHER_SKELETON", location);
                break;
            case "giant":
                boss = spawnMob("GIANT", location);
                break;
            default:
                // Try to spawn as regular mob first
                boss = spawnMob(bossName, location);
                // If that fails, spawn a strong mob as fallback
                if (boss == null) {
                    System.out.println("[ApexDungeons] VanillaAdapter: Boss spawn failed, trying fallback boss");
                    boss = spawnMob("WITHER_SKELETON", location);
                }
                break;
        }

        // Enhance the boss if spawning was successful
        if (boss != null) {
            enhanceBoss(boss, bossName);
            System.out.println("[ApexDungeons] VanillaAdapter: Successfully spawned and enhanced boss " + bossName);
        } else {
            System.out.println("[ApexDungeons] VanillaAdapter: Failed to spawn boss " + bossName);
        }

        return boss;
    }

    /**
     * Enhance a boss with better stats and equipment.
     */
    private void enhanceBoss(LivingEntity boss, String bossName) {
        // Set custom name
        boss.setCustomName("§c§l" + bossName.replace("_", " "));
        boss.setCustomNameVisible(true);

        // Increase health
        boss.setMaxHealth(boss.getMaxHealth() * 2);
        boss.setHealth(boss.getMaxHealth());

        // Add basic equipment if possible
        try {
            if (boss.getEquipment() != null) {
                boss.getEquipment().setHelmet(new ItemStack(Material.DIAMOND_HELMET));
                boss.getEquipment().setChestplate(new ItemStack(Material.DIAMOND_CHESTPLATE));
                boss.getEquipment().setItemInMainHand(new ItemStack(Material.DIAMOND_SWORD));
            }
        } catch (Exception e) {
            // Some entities don't support equipment
        }
    }

    @Override
    public boolean isAvailable() {
        return true; // Vanilla adapter is always available
    }

    @Override
    public String getAdapterName() {
        return "Vanilla";
    }

    @Override
    public String[] getAvailableMobs() {
        return new String[]{
            "ZOMBIE", "SKELETON", "SPIDER", "CREEPER", "ENDERMAN",
            "WITCH", "BLAZE", "GHAST", "PIGLIN", "HOGLIN",
            "VINDICATOR", "EVOKER", "PILLAGER", "RAVAGER"
        };
    }

    @Override
    public String[] getAvailableBosses() {
        return new String[]{
            "WITHER", "ENDER_DRAGON", "ELDER_GUARDIAN", "WARDEN"
        };
    }
}