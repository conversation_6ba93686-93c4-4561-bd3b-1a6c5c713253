# ApexDungeons

ApexDungeons is a procedurally generated dungeon plugin for **PaperMC 1.21+** servers.  It provides a fully GUI driven
experience allowing both players and administrators to create, explore and manage custom dungeons on the fly.  The
plugin ships with a suite of default rooms, loot tables and mobs so that you can start adventuring immediately after
installing.

## Features

- **Randomly generated dungeons** built from modular room blueprints with support for branches, towers and verticality.
- **Zero‑command gameplay** – all interactions are via easy to use inventory GUIs.  After installation simply run
  `/dgn` and start exploring.
- **Dungeon Architect Wand** tool which lets you preview and place individual rooms anywhere in your world.  Rotate,
  reposition and select rooms using intuitive mouse controls.
- **Custom loot and mobs** via YAML definitions.  The plugin includes basic, rare and epic loot tables by default.
- **Boss fights** at the end of dungeons.  Bosses are configurable and integrate with MythicMobs when available.
- **MythicMobs & ModelEngine support** through a pluggable adapter system.  When those plugins are detected your
  dungeon mobs and bosses can use custom models and behaviours.
- **Performance focused** – generation occurs in small batches per tick and chunks are preloaded to minimise lag.

## Quick Start

1. Drop `ApexDungeons-<version>.jar` into your server's `plugins` folder.
2. Start or restart your server.  The plugin will generate its data folder (`plugins/ApexDungeons/`) with default
   configurations, rooms, loot tables and presets.
3. Join your server and run `/dgn`.  This opens the main GUI where you can create a new dungeon using one of the
   pre‑configured presets (Small, Medium or Large).
4. After selecting a size and theme the dungeon will begin generating.  Once finished, click the entry in the list
   of active dungeons to teleport there.

Administrators can access additional controls by running `/dgn admin`.  From here you can pause/resume generation,
regenerate rooms, refill chests, spawn the dungeon boss, export or import rooms and adjust performance settings.

### Commands and Permissions

| Command | Description | Permission |
| --- | --- | --- |
| `/dgn` | Opens the main player GUI. | `apexdungeons.use` |
| `/dgn admin` | Opens the admin control panel. | `apexdungeons.admin` |
| `/dgn givewand <player>` | Gives the Dungeon Architect Wand to a player. | `apexdungeons.admin` |
| `/dgn create <name>` | Manually create a dungeon instance with a unique name. | `apexdungeons.admin` |
| `/dgn remove <name>` | Remove a dungeon instance by name. | `apexdungeons.admin` |
| `/dgn tp <name>` | Teleport to an existing dungeon instance. | `apexdungeons.admin` |

Players with the `apexdungeons.bypass` permission will ignore world or region restrictions when generating or
interacting with dungeons.

## Configuration

All configuration files are located in `plugins/ApexDungeons/` and are generated automatically on first run.  The
primary files include:

- **config.yml** – global toggles and performance budgets.
- **loot.yml** – weighted loot tables used when filling chests.
- **mobs.yml** – spawn pools for each room theme.
- **bosses.yml** – definitions of bosses including health and damage multipliers.
- **rooms/** – folder containing YAML blueprints for every room the generator can use.
- **presets/** – size presets that populate the main GUI.
- **messages.yml** – all user facing strings for easy localisation.

For more detailed information on file formats and how to create your own content, see the documentation in the
`docs/` folder.

## Building From Source

This project uses **Maven** and **Java 21**.  To build your own copy of the plugin run the following from the project
root:

```sh
mvn -q -DskipTests package
```

The shaded plugin JAR will be located at `target/ApexDungeons-<version>-shaded.jar`.  Rename this file to
`ApexDungeons-<version>.jar` and place it in your server's `plugins` folder.  A zipped archive of the full project
can be created with any zip utility.

## FAQ

### Does the plugin work without MythicMobs or ModelEngine?

Yes.  All third‑party integrations are marked as *soft dependencies* and will be automatically detected at runtime.
When those plugins are not present the plugin falls back to the vanilla mob adapter.

### Where are the dungeons generated?

By default dungeons generate in the same world as the player who initiates creation.  You can restrict generation
to certain worlds or create a dedicated `dungeons` world via the `worlds.allowed` list in `config.yml`.

### How do I create my own rooms?

See [docs/rooms.md](docs/rooms.md) for an explanation of the room blueprint format and tips for creating new rooms.

## License

This project is licensed under the MIT License.  See the [LICENSE](LICENSE) file for more information.