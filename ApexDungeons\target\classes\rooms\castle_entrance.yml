# Castle-themed entrance room with medieval architecture
name: "castle_entrance"
description: "A grand castle entrance with stone walls and torches"
width: 15
height: 8
depth: 15

# Material palette for castle theme
palette:
  '#': STONE_BRICKS
  'W': COBBLESTONE_WALL
  'T': TORCH
  'D': OAK_DOOR
  'S': STONE_BRICK_STAIRS
  'F': STONE_BRICK_SLAB
  'I': IRON_BARS
  'L': LANTERN
  'C': CHISELED_STONE_BRICKS
  'M': MOSSY_STONE_BRICKS
  'G': GRAVEL
  'A': AIR
  'B': STONE_BUTTON
  'P': STONE_PRESSURE_PLATE

# Layout for each Y level (bottom to top)
layout:
  0: # Ground level
    - "###############"
    - "#GGGGGGGGGGGGG#"
    - "#G###########G#"
    - "#G#AAAAAAAAA#G#"
    - "#G#AAAAAAAAA#G#"
    - "#G#AAAAAAAAA#G#"
    - "#G#AAAAAAAAA#G#"
    - "#G#AAAAAAAAA#G#"
    - "#G#AAAAAAAAA#G#"
    - "#G#AAAAAAAAA#G#"
    - "#G#AAAAAAAAA#G#"
    - "#G#AAAAAAAAA#G#"
    - "#G###########G#"
    - "#GGGGGGGGGGGGG#"
    - "###############"
  1: # First floor
    - "###############"
    - "#AAAAAAAAAAAAA#"
    - "#A###########A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A###########A#"
    - "#AAAAAAAAAAAAA#"
    - "###############"
  2: # Second floor
    - "###############"
    - "#AAAAAAAAAAAAA#"
    - "#A###########A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A###########A#"
    - "#AAAAAAAAAAAAA#"
    - "###############"
  3: # Third floor with decorations
    - "###############"
    - "#AAAAAAAAAAAAA#"
    - "#A###########A#"
    - "#A#TAAAAAAAT#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#TAAAAAAAT#A#"
    - "#A###########A#"
    - "#AAAAAAAAAAAAA#"
    - "###############"
  4: # Fourth floor with windows
    - "###############"
    - "#AAAAAAAAAAAAA#"
    - "#A###########A#"
    - "#A#IAAAAAAI#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#IAAAAAAI#A#"
    - "#A###########A#"
    - "#AAAAAAAAAAAAA#"
    - "###############"
  5: # Fifth floor
    - "###############"
    - "#AAAAAAAAAAAAA#"
    - "#A###########A#"
    - "#A#LAAAAAAAL#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#LAAAAAAAL#A#"
    - "#A###########A#"
    - "#AAAAAAAAAAAAA#"
    - "###############"
  6: # Sixth floor with chiseled details
    - "###############"
    - "#AAAAAAAAAAAAA#"
    - "#A###########A#"
    - "#A#CAAAAAAC#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#AAAAAAAAA#A#"
    - "#A#CAAAAAAC#A#"
    - "#A###########A#"
    - "#AAAAAAAAAAAAA#"
    - "###############"
  7: # Roof level
    - "###############"
    - "###############"
    - "###############"
    - "###############"
    - "###############"
    - "###############"
    - "###############"
    - "###############"
    - "###############"
    - "###############"
    - "###############"
    - "###############"
    - "###############"
    - "###############"
    - "###############"

# Special features and spawn points
features:
  spawn_point:
    x: 7
    y: 1
    z: 7
  
  boss_spawn:
    x: 7
    y: 1
    z: 3
  
  treasure_chests:
    - x: 3
      y: 1
      z: 3
    - x: 11
      y: 1
      z: 3
    - x: 7
      y: 1
      z: 11

  mob_spawns:
    - x: 5
      y: 1
      z: 5
    - x: 9
      y: 1
      z: 5
    - x: 5
      y: 1
      z: 9
    - x: 9
      y: 1
      z: 9

# Theme-specific settings
theme: "castle"
difficulty: "medium"
recommended_level: "10-20"
