package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * GUI for displaying player dungeon statistics and achievements.
 */
public class StatisticsGUI {
    private static final String GUI_NAME = ChatColor.GOLD + "📊 Your Statistics";

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory(player, 54, GUI_NAME);
        
        // Fill background
        fillBackground(inv);
        
        // Create statistics displays
        createStatisticsDisplay(inv, player, plugin);
        
        // Create navigation buttons
        createNavigationButtons(inv);
        
        player.openInventory(inv);
        
        // Register event listener
        registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.YELLOW_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        
        // Fill border
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
    }

    private static void createStatisticsDisplay(Inventory inv, Player player, ApexDungeons plugin) {
        // General Statistics
        ItemStack general = new ItemStack(Material.PLAYER_HEAD);
        ItemMeta generalMeta = general.getItemMeta();
        generalMeta.setDisplayName(ChatColor.AQUA + "👤 General Statistics");
        List<String> generalLore = new ArrayList<>();
        generalLore.add(ChatColor.GRAY + "Your overall dungeon activity");
        generalLore.add("");
        generalLore.add(ChatColor.YELLOW + "Player: " + ChatColor.WHITE + player.getName());
        generalLore.add(ChatColor.YELLOW + "Dungeons Created: " + ChatColor.WHITE + "0"); // TODO: Implement player stats tracking
        generalLore.add(ChatColor.YELLOW + "Dungeons Completed: " + ChatColor.WHITE + "0");
        generalLore.add(ChatColor.YELLOW + "Total Playtime: " + ChatColor.WHITE + "0h 0m");
        generalLore.add(ChatColor.YELLOW + "First Dungeon: " + ChatColor.WHITE + "Never");
        generalLore.add(ChatColor.YELLOW + "Last Activity: " + ChatColor.WHITE + "Now");
        generalMeta.setLore(generalLore);
        general.setItemMeta(generalMeta);
        inv.setItem(11, general);
        
        // Theme Statistics
        ItemStack themes = new ItemStack(Material.PAINTING);
        ItemMeta themesMeta = themes.getItemMeta();
        themesMeta.setDisplayName(ChatColor.LIGHT_PURPLE + "🎨 Theme Preferences");
        List<String> themesLore = new ArrayList<>();
        themesLore.add(ChatColor.GRAY + "Your favorite dungeon themes");
        themesLore.add("");
        themesLore.add(ChatColor.YELLOW + "Castle Dungeons: " + ChatColor.WHITE + "0 created");
        themesLore.add(ChatColor.YELLOW + "Cave Systems: " + ChatColor.WHITE + "0 created");
        themesLore.add(ChatColor.YELLOW + "Ancient Temples: " + ChatColor.WHITE + "0 created");
        themesLore.add("");
        themesLore.add(ChatColor.YELLOW + "Favorite Theme: " + ChatColor.WHITE + "None yet");
        themesLore.add(ChatColor.YELLOW + "Most Challenging: " + ChatColor.WHITE + "None yet");
        themesMeta.setLore(themesLore);
        themes.setItemMeta(themesMeta);
        inv.setItem(13, themes);
        
        // Achievement Progress
        ItemStack achievements = new ItemStack(Material.GOLDEN_APPLE);
        ItemMeta achievementsMeta = achievements.getItemMeta();
        achievementsMeta.setDisplayName(ChatColor.GOLD + "🏆 Achievements");
        List<String> achievementsLore = new ArrayList<>();
        achievementsLore.add(ChatColor.GRAY + "Your dungeon achievements");
        achievementsLore.add("");
        achievementsLore.add(ChatColor.GREEN + "✓ First Steps" + ChatColor.GRAY + " - Create your first dungeon");
        achievementsLore.add(ChatColor.GRAY + "✗ Master Builder" + ChatColor.GRAY + " - Create 10 dungeons");
        achievementsLore.add(ChatColor.GRAY + "✗ Theme Explorer" + ChatColor.GRAY + " - Try all themes");
        achievementsLore.add(ChatColor.GRAY + "✗ Speed Runner" + ChatColor.GRAY + " - Complete in under 10 minutes");
        achievementsLore.add(ChatColor.GRAY + "✗ Architect" + ChatColor.GRAY + " - Create a massive dungeon");
        achievementsLore.add("");
        achievementsLore.add(ChatColor.YELLOW + "Progress: " + ChatColor.WHITE + "0/15 achievements");
        achievementsMeta.setLore(achievementsLore);
        achievements.setItemMeta(achievementsMeta);
        inv.setItem(15, achievements);
        
        // Recent Activity
        ItemStack recent = new ItemStack(Material.CLOCK);
        ItemMeta recentMeta = recent.getItemMeta();
        recentMeta.setDisplayName(ChatColor.YELLOW + "⏰ Recent Activity");
        List<String> recentLore = new ArrayList<>();
        recentLore.add(ChatColor.GRAY + "Your latest dungeon activities");
        recentLore.add("");
        recentLore.add(ChatColor.GRAY + "No recent activity");
        recentLore.add(ChatColor.GRAY + "Create your first dungeon to");
        recentLore.add(ChatColor.GRAY + "start tracking your progress!");
        recentMeta.setLore(recentLore);
        recent.setItemMeta(recentMeta);
        inv.setItem(29, recent);
        
        // Server Statistics
        ItemStack server = new ItemStack(Material.BEACON);
        ItemMeta serverMeta = server.getItemMeta();
        serverMeta.setDisplayName(ChatColor.BLUE + "🌐 Server Statistics");
        List<String> serverLore = new ArrayList<>();
        serverLore.add(ChatColor.GRAY + "Global server dungeon stats");
        serverLore.add("");
        int totalDungeons = plugin.getDungeonManager().getDungeons().size();
        serverLore.add(ChatColor.YELLOW + "Active Dungeons: " + ChatColor.WHITE + totalDungeons);
        serverLore.add(ChatColor.YELLOW + "Total Created: " + ChatColor.WHITE + "0"); // TODO: Implement global stats
        serverLore.add(ChatColor.YELLOW + "Most Popular Theme: " + ChatColor.WHITE + "Castle");
        serverLore.add(ChatColor.YELLOW + "Average Completion: " + ChatColor.WHITE + "25 minutes");
        serverLore.add("");
        serverLore.add(ChatColor.GRAY + "Be part of the community!");
        serverMeta.setLore(serverLore);
        server.setItemMeta(serverMeta);
        inv.setItem(31, server);
        
        // Leaderboards
        ItemStack leaderboard = new ItemStack(Material.DIAMOND);
        ItemMeta leaderboardMeta = leaderboard.getItemMeta();
        leaderboardMeta.setDisplayName(ChatColor.AQUA + "🥇 Leaderboards");
        List<String> leaderboardLore = new ArrayList<>();
        leaderboardLore.add(ChatColor.GRAY + "Top dungeon creators");
        leaderboardLore.add("");
        leaderboardLore.add(ChatColor.GOLD + "1. " + ChatColor.WHITE + "Player1" + ChatColor.GRAY + " - 25 dungeons");
        leaderboardLore.add(ChatColor.GRAY + "2. " + ChatColor.WHITE + "Player2" + ChatColor.GRAY + " - 18 dungeons");
        leaderboardLore.add(ChatColor.GOLD + "3. " + ChatColor.WHITE + "Player3" + ChatColor.GRAY + " - 15 dungeons");
        leaderboardLore.add("");
        leaderboardLore.add(ChatColor.YELLOW + "Your Rank: " + ChatColor.WHITE + "Unranked");
        leaderboardLore.add(ChatColor.GRAY + "Create dungeons to climb the ranks!");
        leaderboardMeta.setLore(leaderboardLore);
        leaderboard.setItemMeta(leaderboardMeta);
        inv.setItem(33, leaderboard);
    }

    private static void createNavigationButtons(Inventory inv) {
        // Back button
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(ChatColor.RED + "← Back to Main Menu");
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        
        // Refresh button
        ItemStack refresh = new ItemStack(Material.LIME_DYE);
        ItemMeta refreshMeta = refresh.getItemMeta();
        refreshMeta.setDisplayName(ChatColor.GREEN + "🔄 Refresh Statistics");
        List<String> refreshLore = new ArrayList<>();
        refreshLore.add(ChatColor.GRAY + "Update your statistics");
        refreshLore.add(ChatColor.GRAY + "with the latest data");
        refreshMeta.setLore(refreshLore);
        refresh.setItemMeta(refreshMeta);
        inv.setItem(53, refresh);
    }

    private static void registerEventListener(ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player) e.getWhoClicked();
                    int slot = e.getRawSlot();
                    
                    switch (slot) {
                        case 45: // Back
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            break;
                        case 53: // Refresh
                            clicker.closeInventory();
                            StatisticsGUI.open(clicker, plugin);
                            clicker.sendMessage(ChatColor.GREEN + "Statistics refreshed!");
                            break;
                    }
                }
            }
        }, plugin);
    }
}
