package com.apexdungeons.tools;

import com.apexdungeons.ApexDungeons;
import org.bukkit.*;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;

/**
 * Tool for connecting rooms together with doorways, passages, and seamless transitions.
 * Similar to the existing wand tool but specialized for room connections.
 */
public class RoomConnector implements Listener {
    private final ApexDungeons plugin;
    private final NamespacedKey connectorToolKey;
    
    // Track active connection sessions
    private final Map<UUID, ConnectionSession> activeSessions = new HashMap<>();
    private final Map<UUID, BukkitTask> previewTasks = new HashMap<>();
    
    public RoomConnector(ApexDungeons plugin) {
        this.plugin = plugin;
        this.connectorToolKey = new NamespacedKey(plugin, "room_connector_tool");
        
        // Register event listener
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Create a room connector tool.
     */
    public ItemStack createConnectorTool() {
        ItemStack tool = new ItemStack(Material.BLAZE_ROD);
        ItemMeta meta = tool.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(ChatColor.GOLD + "Room Connector Tool");
            meta.setLore(Arrays.asList(
                ChatColor.GRAY + "Connect rooms with doorways and passages",
                "",
                ChatColor.GREEN + "Left-click: " + ChatColor.WHITE + "Select first connection point",
                ChatColor.GREEN + "Right-click: " + ChatColor.WHITE + "Select second connection point",
                ChatColor.GREEN + "Shift+Right-click: " + ChatColor.WHITE + "Cancel connection",
                "",
                ChatColor.YELLOW + "Connection Types:",
                ChatColor.GRAY + "• Simple doorway (2x3 opening)",
                ChatColor.GRAY + "• Wide passage (3x3 opening)",
                ChatColor.GRAY + "• Corridor (full tunnel between points)"
            ));
            meta.getPersistentDataContainer().set(connectorToolKey, PersistentDataType.BYTE, (byte) 1);
            tool.setItemMeta(meta);
        }
        return tool;
    }

    /**
     * Handle player interactions with the connector tool.
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        
        if (!isConnectorTool(item)) {
            return;
        }
        
        event.setCancelled(true);
        
        Block targetBlock = player.getTargetBlockExact(10);
        if (targetBlock == null || targetBlock.getType() == Material.AIR) {
            player.sendMessage(ChatColor.YELLOW + "Look at a block to select connection point!");
            return;
        }
        
        UUID playerId = player.getUniqueId();
        
        if (event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            if (player.isSneaking()) {
                // Cancel connection
                cancelConnection(player);
            } else {
                // Select second point
                handleSecondPointSelection(player, targetBlock.getLocation());
            }
        } else if (event.getAction() == Action.LEFT_CLICK_AIR || event.getAction() == Action.LEFT_CLICK_BLOCK) {
            // Select first point
            handleFirstPointSelection(player, targetBlock.getLocation());
        }
    }

    /**
     * Handle selection of the first connection point.
     */
    private void handleFirstPointSelection(Player player, Location location) {
        UUID playerId = player.getUniqueId();
        
        // Cancel any existing session
        cancelConnection(player);
        
        // Create new connection session
        ConnectionSession session = new ConnectionSession(location);
        activeSessions.put(playerId, session);
        
        // Start preview task for first point
        startPreviewTask(player, session);
        
        player.sendMessage(ChatColor.GREEN + "First connection point selected!");
        player.sendMessage(ChatColor.GRAY + "Right-click to select the second point.");
        player.playSound(location, Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.2f);
    }

    /**
     * Handle selection of the second connection point.
     */
    private void handleSecondPointSelection(Player player, Location location) {
        UUID playerId = player.getUniqueId();
        ConnectionSession session = activeSessions.get(playerId);
        
        if (session == null) {
            player.sendMessage(ChatColor.YELLOW + "Select the first connection point first (left-click)!");
            return;
        }
        
        if (session.getSecondPoint() != null) {
            player.sendMessage(ChatColor.YELLOW + "Connection already has two points! Left-click to start a new connection.");
            return;
        }
        
        // Set second point
        session.setSecondPoint(location);
        
        // Show connection options
        showConnectionOptions(player, session);
        
        player.sendMessage(ChatColor.GREEN + "Second connection point selected!");
        player.sendMessage(ChatColor.GRAY + "Choose connection type in chat:");
        player.playSound(location, Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.4f);
    }

    /**
     * Show connection type options to the player.
     */
    private void showConnectionOptions(Player player, ConnectionSession session) {
        player.sendMessage(ChatColor.GOLD + "=== Connection Options ===");
        player.sendMessage(ChatColor.YELLOW + "1. " + ChatColor.WHITE + "Simple Doorway (2x3 opening)");
        player.sendMessage(ChatColor.YELLOW + "2. " + ChatColor.WHITE + "Wide Passage (3x3 opening)");
        player.sendMessage(ChatColor.YELLOW + "3. " + ChatColor.WHITE + "Full Corridor (tunnel between points)");
        player.sendMessage(ChatColor.GRAY + "Type the number (1, 2, or 3) in chat to create the connection.");
        
        // Set session to waiting for input
        session.setWaitingForInput(true);
    }

    /**
     * Create a connection between two points.
     */
    public void createConnection(Player player, ConnectionType type) {
        UUID playerId = player.getUniqueId();
        ConnectionSession session = activeSessions.get(playerId);
        
        if (session == null || session.getSecondPoint() == null) {
            player.sendMessage(ChatColor.RED + "No valid connection points selected!");
            return;
        }
        
        Location point1 = session.getFirstPoint();
        Location point2 = session.getSecondPoint();
        
        player.sendMessage(ChatColor.YELLOW + "Creating " + type.getDisplayName() + "...");
        
        switch (type) {
            case SIMPLE_DOORWAY:
                createSimpleDoorway(point1, point2);
                break;
            case WIDE_PASSAGE:
                createWidePassage(point1, point2);
                break;
            case FULL_CORRIDOR:
                createFullCorridor(point1, point2);
                break;
        }
        
        // Clean up session
        cancelConnection(player);
        
        player.sendMessage(ChatColor.GREEN + "Connection created successfully!");
        player.playSound(point1, Sound.BLOCK_ANVIL_USE, 1.0f, 1.0f);
        player.playSound(point2, Sound.BLOCK_ANVIL_USE, 1.0f, 1.0f);
    }

    /**
     * Create a simple 2x3 doorway between two points.
     */
    private void createSimpleDoorway(Location point1, Location point2) {
        // Create openings at both points
        createOpening(point1, 2, 3);
        createOpening(point2, 2, 3);
        
        // Add some decorative elements
        addDoorwayDecoration(point1);
        addDoorwayDecoration(point2);
    }

    /**
     * Create a wide 3x3 passage between two points.
     */
    private void createWidePassage(Location point1, Location point2) {
        // Create larger openings at both points
        createOpening(point1, 3, 3);
        createOpening(point2, 3, 3);
        
        // Add passage decoration
        addPassageDecoration(point1);
        addPassageDecoration(point2);
    }

    /**
     * Create a full corridor tunnel between two points.
     */
    private void createFullCorridor(Location point1, Location point2) {
        // Create openings at both points
        createOpening(point1, 3, 3);
        createOpening(point2, 3, 3);
        
        // Create the tunnel between points
        createTunnel(point1, point2);
    }

    /**
     * Create an opening at a specific location.
     */
    private void createOpening(Location center, int width, int height) {
        World world = center.getWorld();
        if (world == null) return;
        
        int halfWidth = width / 2;
        int halfHeight = height / 2;
        
        // Clear blocks for the opening
        for (int x = -halfWidth; x <= halfWidth; x++) {
            for (int y = -halfHeight; y <= halfHeight; y++) {
                Location blockLoc = center.clone().add(x, y, 0);
                Block block = world.getBlockAt(blockLoc);
                block.setType(Material.AIR);
            }
        }
    }

    /**
     * Add decorative elements around a doorway.
     */
    private void addDoorwayDecoration(Location center) {
        World world = center.getWorld();
        if (world == null) return;
        
        // Add stone brick frame around the doorway
        Location[] framePositions = {
            center.clone().add(-1, -2, 0), // Bottom left
            center.clone().add(1, -2, 0),  // Bottom right
            center.clone().add(-1, 2, 0),  // Top left
            center.clone().add(1, 2, 0),   // Top right
            center.clone().add(0, 2, 0)    // Top center
        };
        
        for (Location pos : framePositions) {
            Block block = world.getBlockAt(pos);
            if (block.getType() == Material.AIR || block.getType().name().contains("STONE")) {
                block.setType(Material.STONE_BRICKS);
            }
        }
    }

    /**
     * Add decorative elements around a passage.
     */
    private void addPassageDecoration(Location center) {
        World world = center.getWorld();
        if (world == null) return;
        
        // Add pillars on the sides
        for (int y = -1; y <= 2; y++) {
            Block leftPillar = world.getBlockAt(center.clone().add(-2, y, 0));
            Block rightPillar = world.getBlockAt(center.clone().add(2, y, 0));
            
            if (leftPillar.getType() == Material.AIR || leftPillar.getType().name().contains("STONE")) {
                leftPillar.setType(Material.STONE_BRICK_STAIRS);
            }
            if (rightPillar.getType() == Material.AIR || rightPillar.getType().name().contains("STONE")) {
                rightPillar.setType(Material.STONE_BRICK_STAIRS);
            }
        }
    }

    /**
     * Create a tunnel between two points.
     */
    private void createTunnel(Location start, Location end) {
        World world = start.getWorld();
        if (world == null || !world.equals(end.getWorld())) return;
        
        // Simple straight-line tunnel for now
        // In a more advanced implementation, you could add pathfinding
        
        double distance = start.distance(end);
        int steps = (int) Math.ceil(distance);
        
        for (int i = 0; i <= steps; i++) {
            double ratio = (double) i / steps;
            Location tunnelPoint = start.clone().add(
                (end.getX() - start.getX()) * ratio,
                (end.getY() - start.getY()) * ratio,
                (end.getZ() - start.getZ()) * ratio
            );
            
            // Create 3x3 tunnel cross-section
            for (int x = -1; x <= 1; x++) {
                for (int y = -1; y <= 1; y++) {
                    for (int z = -1; z <= 1; z++) {
                        Location blockLoc = tunnelPoint.clone().add(x, y, z);
                        Block block = world.getBlockAt(blockLoc);
                        
                        // Clear interior, add walls/ceiling/floor as needed
                        if (x == 0 && z == 0 && y >= -1 && y <= 1) {
                            block.setType(Material.AIR); // Clear center
                        } else if (y == -2) {
                            block.setType(Material.STONE_BRICKS); // Floor
                        } else if (y == 2) {
                            block.setType(Material.STONE_BRICKS); // Ceiling
                        } else if (Math.abs(x) == 1 || Math.abs(z) == 1) {
                            if (block.getType() == Material.AIR) {
                                block.setType(Material.STONE_BRICKS); // Walls
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Start preview task for showing connection points.
     */
    private void startPreviewTask(Player player, ConnectionSession session) {
        UUID playerId = player.getUniqueId();
        
        // Cancel existing task
        BukkitTask existingTask = previewTasks.remove(playerId);
        if (existingTask != null) {
            existingTask.cancel();
        }
        
        // Start new preview task
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (!activeSessions.containsKey(playerId)) {
                    cancel();
                    return;
                }
                
                showConnectionPreview(player, session);
            }
        }.runTaskTimer(plugin, 0L, 20L); // Update every second
        
        previewTasks.put(playerId, task);
    }

    /**
     * Show preview particles for connection points.
     */
    private void showConnectionPreview(Player player, ConnectionSession session) {
        World world = session.getFirstPoint().getWorld();
        if (world == null) return;
        
        // Show first point
        Location point1 = session.getFirstPoint().clone().add(0.5, 0.5, 0.5);
        world.spawnParticle(Particle.END_ROD, point1, 5, 0.2, 0.2, 0.2, 0);
        
        // Show second point if selected
        if (session.getSecondPoint() != null) {
            Location point2 = session.getSecondPoint().clone().add(0.5, 0.5, 0.5);
            world.spawnParticle(Particle.ENCHANT, point2, 5, 0.2, 0.2, 0.2, 0);
            
            // Show line between points
            showConnectionLine(point1, point2);
        }
    }

    /**
     * Show a particle line between two points.
     */
    private void showConnectionLine(Location start, Location end) {
        World world = start.getWorld();
        if (world == null) return;
        
        double distance = start.distance(end);
        int particles = Math.min(20, (int) distance * 2); // Limit particles to avoid lag
        
        for (int i = 0; i <= particles; i++) {
            double ratio = (double) i / particles;
            Location linePoint = start.clone().add(
                (end.getX() - start.getX()) * ratio,
                (end.getY() - start.getY()) * ratio,
                (end.getZ() - start.getZ()) * ratio
            );
            world.spawnParticle(Particle.HAPPY_VILLAGER, linePoint, 1, 0, 0, 0, 0);
        }
    }

    /**
     * Cancel the connection for a player.
     */
    private void cancelConnection(Player player) {
        UUID playerId = player.getUniqueId();
        
        // Remove session
        activeSessions.remove(playerId);
        
        // Cancel preview task
        BukkitTask task = previewTasks.remove(playerId);
        if (task != null) {
            task.cancel();
        }
        
        player.sendMessage(ChatColor.GRAY + "Connection cancelled.");
    }

    /**
     * Handle chat input for connection type selection.
     */
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        ConnectionSession session = activeSessions.get(playerId);

        if (session != null && session.isWaitingForInput()) {
            String message = event.getMessage().trim();

            try {
                int choice = Integer.parseInt(message);
                ConnectionType type = ConnectionType.fromNumber(choice);

                if (type != null) {
                    event.setCancelled(true);
                    session.setWaitingForInput(false);

                    // Run on main thread
                    plugin.getServer().getScheduler().runTask(plugin, () -> {
                        createConnection(player, type);
                    });
                } else {
                    player.sendMessage(ChatColor.RED + "Invalid choice! Please type 1, 2, or 3.");
                }
            } catch (NumberFormatException e) {
                // Not a number, ignore
            }
        }
    }

    /**
     * Handle player quit to clean up sessions.
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        cancelConnection(event.getPlayer());
    }

    /**
     * Check if an item is a connector tool.
     */
    private boolean isConnectorTool(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().has(connectorToolKey, PersistentDataType.BYTE);
    }

    /**
     * Get active connection session for a player.
     */
    public ConnectionSession getConnectionSession(Player player) {
        return activeSessions.get(player.getUniqueId());
    }

    /**
     * Shutdown and cleanup.
     */
    public void shutdown() {
        // Cancel all preview tasks
        for (BukkitTask task : previewTasks.values()) {
            task.cancel();
        }
        previewTasks.clear();
        activeSessions.clear();
        
        plugin.getLogger().info("RoomConnector shutdown complete.");
    }

    /**
     * Represents an active connection session.
     */
    public static class ConnectionSession {
        private final Location firstPoint;
        private Location secondPoint;
        private boolean waitingForInput = false;
        
        public ConnectionSession(Location firstPoint) {
            this.firstPoint = firstPoint.clone();
        }
        
        public Location getFirstPoint() {
            return firstPoint.clone();
        }
        
        public Location getSecondPoint() {
            return secondPoint != null ? secondPoint.clone() : null;
        }
        
        public void setSecondPoint(Location secondPoint) {
            this.secondPoint = secondPoint.clone();
        }
        
        public boolean isWaitingForInput() {
            return waitingForInput;
        }
        
        public void setWaitingForInput(boolean waitingForInput) {
            this.waitingForInput = waitingForInput;
        }
    }

    /**
     * Types of connections that can be created.
     */
    public enum ConnectionType {
        SIMPLE_DOORWAY("Simple Doorway"),
        WIDE_PASSAGE("Wide Passage"),
        FULL_CORRIDOR("Full Corridor");
        
        private final String displayName;
        
        ConnectionType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public static ConnectionType fromNumber(int number) {
            switch (number) {
                case 1: return SIMPLE_DOORWAY;
                case 2: return WIDE_PASSAGE;
                case 3: return FULL_CORRIDOR;
                default: return null;
            }
        }
    }
}
