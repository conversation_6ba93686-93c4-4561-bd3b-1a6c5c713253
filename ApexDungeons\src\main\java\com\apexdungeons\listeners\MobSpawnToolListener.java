package com.apexdungeons.listeners;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.mobs.MobSpawnPoint;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;

/**
 * Handles mob spawn tool interactions.
 */
public class MobSpawnToolListener implements Listener {
    private final ApexDungeons plugin;
    
    public MobSpawnToolListener(ApexDungeons plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        if (item == null || event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        
        boolean isMobTool = plugin.getMobSpawnTool().isMobSpawnTool(item);
        boolean isBossTool = plugin.getMobSpawnTool().isBossSpawnTool(item);
        
        if (!isMobTool && !isBossTool) {
            return;
        }
        
        event.setCancelled(true);
        
        Location clickedLocation = event.getClickedBlock().getLocation().add(0, 1, 0);
        
        // Check if there's already a spawn point here
        MobSpawnPoint existing = plugin.getMobSpawnManager().getSpawnPoint(clickedLocation);
        if (existing != null) {
            player.sendMessage(ChatColor.YELLOW + "⚠ There's already a " + 
                (existing.isBoss() ? "boss" : "mob") + " spawn point here!");
            player.sendMessage(ChatColor.GRAY + "Mob: " + existing.getMobName() + 
                ", Radius: " + existing.getRadius());
            
            // Show particles to indicate existing spawn point
            showSpawnPointParticles(clickedLocation, existing.isBoss());
            return;
        }
        
        // Check permissions
        if (!player.hasPermission("apexdungeons.admin") && !player.hasPermission("apexdungeons.mobspawn")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to place spawn points!");
            return;
        }
        
        if (isMobTool) {
            handleMobSpawnTool(player, clickedLocation);
        } else {
            handleBossSpawnTool(player, clickedLocation);
        }
    }
    
    private void handleMobSpawnTool(Player player, Location location) {
        // Check if player has configured a mob type
        String mobName = plugin.getMobSpawnData().getPlayerMobSelection(player.getUniqueId());
        if (mobName == null) {
            player.sendMessage(ChatColor.RED + "❌ No mob configured!");
            player.sendMessage(ChatColor.YELLOW + "Use " + ChatColor.AQUA + "/dgn mobspawn set <mob_name>" + 
                ChatColor.YELLOW + " first");
            return;
        }
        
        double radius = plugin.getMobSpawnData().getPlayerRadius(player.getUniqueId(), 5.0);
        
        // Add the spawn point
        plugin.getMobSpawnManager().addMobSpawnPoint(location, mobName, radius);
        
        // Feedback to player
        player.sendMessage(ChatColor.GREEN + "✓ Mob spawn point created!");
        player.sendMessage(ChatColor.GRAY + "Mob: " + ChatColor.WHITE + mobName);
        player.sendMessage(ChatColor.GRAY + "Radius: " + ChatColor.WHITE + radius + " blocks");
        player.sendMessage(ChatColor.GRAY + "Location: " + ChatColor.WHITE + 
            location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ());
        
        // Visual and audio feedback
        showSpawnPointParticles(location, false);
        player.playSound(location, Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
    }
    
    private void handleBossSpawnTool(Player player, Location location) {
        // Check if player has configured a boss type
        String bossName = plugin.getMobSpawnData().getPlayerBossSelection(player.getUniqueId());
        if (bossName == null) {
            player.sendMessage(ChatColor.RED + "❌ No boss configured!");
            player.sendMessage(ChatColor.YELLOW + "Use " + ChatColor.AQUA + "/dgn bossspawn set <boss_name>" + 
                ChatColor.YELLOW + " first");
            return;
        }
        
        double radius = plugin.getMobSpawnData().getPlayerRadius(player.getUniqueId(), 7.0);
        
        // Add the boss spawn point
        plugin.getMobSpawnManager().addBossSpawnPoint(location, bossName, radius);
        
        // Feedback to player
        player.sendMessage(ChatColor.GREEN + "✓ Boss spawn point created!");
        player.sendMessage(ChatColor.GRAY + "Boss: " + ChatColor.WHITE + bossName);
        player.sendMessage(ChatColor.GRAY + "Radius: " + ChatColor.WHITE + radius + " blocks");
        player.sendMessage(ChatColor.GRAY + "Location: " + ChatColor.WHITE + 
            location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ());
        
        // Visual and audio feedback
        showSpawnPointParticles(location, true);
        player.playSound(location, Sound.ENTITY_WITHER_SPAWN, 0.5f, 1.0f);
    }
    
    private void showSpawnPointParticles(Location location, boolean isBoss) {
        if (isBoss) {
            // Dark particles for boss spawns
            location.getWorld().spawnParticle(Particle.LARGE_SMOKE,
                location.add(0.5, 0.5, 0.5), 20, 0.5, 0.5, 0.5, 0.02);
            location.getWorld().spawnParticle(Particle.FLAME,
                location, 10, 0.3, 0.3, 0.3, 0.01);
        } else {
            // Green particles for regular mob spawns
            location.getWorld().spawnParticle(Particle.HAPPY_VILLAGER,
                location.add(0.5, 0.5, 0.5), 15, 0.5, 0.5, 0.5, 0);
            location.getWorld().spawnParticle(Particle.ENCHANT,
                location, 10, 0.3, 0.3, 0.3, 1);
        }
    }
}
