# Ancient temple with mystical architecture and traps
name: "ancient_temple"
description: "An ancient temple with golden decorations and mysterious chambers"
width: 25
height: 12
depth: 25

# Material palette for temple theme
palette:
  '#': SANDSTONE
  'S': SMOOTH_SANDSTONE
  'C': CHISELED_SANDSTONE
  'R': RED_SANDSTONE
  'G': GOLD_BLOCK
  'L': LAPIS_LAZULI_BLOCK
  'E': EMERALD_BLOCK
  'D': DIAMOND_BLOCK
  'T': TORCH
  'F': FIRE
  'W': WATER
  'A': AIR
  'P': SANDSTONE_PILLAR
  'B': SANDSTONE_STAIRS
  'M': SANDSTONE_SLAB
  'O': OBSERVER
  'H': HOPPER
  'N': NOTE_BLOCK
  'I': IRON_DOOR
  'J': JUNGLE_STAIRS
  'V': VINE
  'X': TRIPWIRE_HOOK

# Layout for each Y level (bottom to top)
layout:
  0: # Foundation level
    - "#########################"
    - "#SSSSSSSSSSSSSSSSSSSSSSS#"
    - "#S###################S#"
    - "#S#SSSSSSSSSSSSSSSSS#S#"
    - "#S#S###############S#S#"
    - "#S#S#SSSSSSSSSSSSS#S#S#"
    - "#S#S#S###########S#S#S#"
    - "#S#S#S#SSSSSSSSS#S#S#S#"
    - "#S#S#S#S#######S#S#S#S#"
    - "#S#S#S#S#SSSSS#S#S#S#S#"
    - "#S#S#S#S#S###S#S#S#S#S#"
    - "#S#S#S#S#S#G#S#S#S#S#S#"
    - "#S#S#S#S#S###S#S#S#S#S#"
    - "#S#S#S#S#SSSSS#S#S#S#S#"
    - "#S#S#S#S#######S#S#S#S#"
    - "#S#S#S#SSSSSSSSS#S#S#S#"
    - "#S#S#S###########S#S#S#"
    - "#S#S#SSSSSSSSSSSSS#S#S#"
    - "#S#S###############S#S#"
    - "#S#SSSSSSSSSSSSSSSSS#S#"
    - "#S###################S#"
    - "#SSSSSSSSSSSSSSSSSSSSSSS#"
    - "#########################"
  1: # Ground level with pillars
    - "#########################"
    - "#AAAAAAAAAAAAAAAAAAAAAA#"
    - "#A###################A#"
    - "#A#AAAAAAAAAAAAAAAA#A#"
    - "#A#A###############A#A#"
    - "#A#A#PAAAAAAAAAAAAP#A#A#"
    - "#A#A#A###########A#A#A#"
    - "#A#A#A#AAAAAAAAA#A#A#A#"
    - "#A#A#A#A#######A#A#A#A#"
    - "#A#A#A#A#PAAAP#A#A#A#A#"
    - "#A#A#A#A#A###A#A#A#A#A#"
    - "#A#A#A#A#A#G#A#A#A#A#A#"
    - "#A#A#A#A#A###A#A#A#A#A#"
    - "#A#A#A#A#PAAAP#A#A#A#A#"
    - "#A#A#A#A#######A#A#A#A#"
    - "#A#A#A#AAAAAAAAA#A#A#A#"
    - "#A#A#A###########A#A#A#"
    - "#A#A#PAAAAAAAAAAAAP#A#A#"
    - "#A#A###############A#A#"
    - "#A#AAAAAAAAAAAAAAAA#A#"
    - "#A###################A#"
    - "#AAAAAAAAAAAAAAAAAAAAAA#"
    - "#########################"
  2: # Second level
    - "#########################"
    - "#AAAAAAAAAAAAAAAAAAAAAA#"
    - "#A###################A#"
    - "#A#AAAAAAAAAAAAAAAA#A#"
    - "#A#A###############A#A#"
    - "#A#A#PAAAAAAAAAAAAP#A#A#"
    - "#A#A#A###########A#A#A#"
    - "#A#A#A#AAAAAAAAA#A#A#A#"
    - "#A#A#A#A#######A#A#A#A#"
    - "#A#A#A#A#PAAAP#A#A#A#A#"
    - "#A#A#A#A#A###A#A#A#A#A#"
    - "#A#A#A#A#A#L#A#A#A#A#A#"
    - "#A#A#A#A#A###A#A#A#A#A#"
    - "#A#A#A#A#PAAAP#A#A#A#A#"
    - "#A#A#A#A#######A#A#A#A#"
    - "#A#A#A#AAAAAAAAA#A#A#A#"
    - "#A#A#A###########A#A#A#"
    - "#A#A#PAAAAAAAAAAAAP#A#A#"
    - "#A#A###############A#A#"
    - "#A#AAAAAAAAAAAAAAAA#A#"
    - "#A###################A#"
    - "#AAAAAAAAAAAAAAAAAAAAAA#"
    - "#########################"
  3: # Third level with torches
    - "#########################"
    - "#AAAAAAAAAAAAAAAAAAAAAA#"
    - "#A###################A#"
    - "#A#AAAAAAAAAAAAAAAA#A#"
    - "#A#A###############A#A#"
    - "#A#A#TAAAAAAAAAAAAAT#A#A#"
    - "#A#A#A###########A#A#A#"
    - "#A#A#A#AAAAAAAAA#A#A#A#"
    - "#A#A#A#A#######A#A#A#A#"
    - "#A#A#A#A#TAAAAT#A#A#A#A#"
    - "#A#A#A#A#A###A#A#A#A#A#"
    - "#A#A#A#A#A#E#A#A#A#A#A#"
    - "#A#A#A#A#A###A#A#A#A#A#"
    - "#A#A#A#A#TAAAAT#A#A#A#A#"
    - "#A#A#A#A#######A#A#A#A#"
    - "#A#A#A#AAAAAAAAA#A#A#A#"
    - "#A#A#A###########A#A#A#"
    - "#A#A#TAAAAAAAAAAAAAT#A#A#"
    - "#A#A###############A#A#"
    - "#A#AAAAAAAAAAAAAAAA#A#"
    - "#A###################A#"
    - "#AAAAAAAAAAAAAAAAAAAAAA#"
    - "#########################"
  4: # Fourth level
    - "#########################"
    - "#AAAAAAAAAAAAAAAAAAAAAA#"
    - "#A###################A#"
    - "#A#AAAAAAAAAAAAAAAA#A#"
    - "#A#A###############A#A#"
    - "#A#A#CAAAAAAAAAAAAC#A#A#"
    - "#A#A#A###########A#A#A#"
    - "#A#A#A#AAAAAAAAA#A#A#A#"
    - "#A#A#A#A#######A#A#A#A#"
    - "#A#A#A#A#CAAAC#A#A#A#A#"
    - "#A#A#A#A#A###A#A#A#A#A#"
    - "#A#A#A#A#A#D#A#A#A#A#A#"
    - "#A#A#A#A#A###A#A#A#A#A#"
    - "#A#A#A#A#CAAAC#A#A#A#A#"
    - "#A#A#A#A#######A#A#A#A#"
    - "#A#A#A#AAAAAAAAA#A#A#A#"
    - "#A#A#A###########A#A#A#"
    - "#A#A#CAAAAAAAAAAAAC#A#A#"
    - "#A#A###############A#A#"
    - "#A#AAAAAAAAAAAAAAAA#A#"
    - "#A###################A#"
    - "#AAAAAAAAAAAAAAAAAAAAAA#"
    - "#########################"
  5: # Fifth level with decorations
    - "#########################"
    - "#AAAAAAAAAAAAAAAAAAAAAA#"
    - "#A###################A#"
    - "#A#AAAAAAAAAAAAAAAA#A#"
    - "#A#A###############A#A#"
    - "#A#A#GAAAAAAAAAAAAG#A#A#"
    - "#A#A#A###########A#A#A#"
    - "#A#A#A#AAAAAAAAA#A#A#A#"
    - "#A#A#A#A#######A#A#A#A#"
    - "#A#A#A#A#GAAAG#A#A#A#A#"
    - "#A#A#A#A#A###A#A#A#A#A#"
    - "#A#A#A#A#A#G#A#A#A#A#A#"
    - "#A#A#A#A#A###A#A#A#A#A#"
    - "#A#A#A#A#GAAAG#A#A#A#A#"
    - "#A#A#A#A#######A#A#A#A#"
    - "#A#A#A#AAAAAAAAA#A#A#A#"
    - "#A#A#A###########A#A#A#"
    - "#A#A#GAAAAAAAAAAAAG#A#A#"
    - "#A#A###############A#A#"
    - "#A#AAAAAAAAAAAAAAAA#A#"
    - "#A###################A#"
    - "#AAAAAAAAAAAAAAAAAAAAAA#"
    - "#########################"
  6: # Sixth level
    - "#########################"
    - "#####################"
    - "#####################"
    - "#####################"
    - "###############"
    - "###############"
    - "###########"
    - "###########"
    - "#######"
    - "#######"
    - "###"
    - "###"
    - "###"
    - "#######"
    - "#######"
    - "###########"
    - "###########"
    - "###############"
    - "###############"
    - "#####################"
    - "#####################"
    - "#####################"
    - "#########################"

# Special features and spawn points
features:
  spawn_point:
    x: 12
    y: 1
    z: 12
  
  boss_spawn:
    x: 12
    y: 1
    z: 6
  
  treasure_chests:
    - x: 6
      y: 1
      z: 6
    - x: 18
      y: 1
      z: 6
    - x: 6
      y: 1
      z: 18
    - x: 18
      y: 1
      z: 18
    - x: 12
      y: 1
      z: 3

  mob_spawns:
    - x: 9
      y: 1
      z: 9
    - x: 15
      y: 1
      z: 9
    - x: 9
      y: 1
      z: 15
    - x: 15
      y: 1
      z: 15
    - x: 12
      y: 1
      z: 21

  traps:
    - x: 12
      y: 1
      z: 9
      type: "pressure_plate"
    - x: 9
      y: 1
      z: 12
      type: "tripwire"
    - x: 15
      y: 1
      z: 12
      type: "tripwire"

# Theme-specific settings
theme: "temple"
difficulty: "expert"
recommended_level: "20-30"
ambient_sounds:
  - "BLOCK_FIRE_AMBIENT"
  - "BLOCK_NOTE_BLOCK_CHIME"
lighting: "mystical"
special_effects:
  - "golden_particles"
  - "enchantment_glint"
