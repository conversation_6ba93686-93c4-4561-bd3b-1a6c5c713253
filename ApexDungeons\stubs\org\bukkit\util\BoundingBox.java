package org.bukkit.util;

/**
 * Minimal stub for BoundingBox.  Stores min and max coordinates but does
 * not provide any real intersection or transformation logic.  Only
 * constructors and getters are implemented.
 */
public class BoundingBox {
    private final double minX, minY, minZ;
    private final double maxX, maxY, maxZ;

    public BoundingBox(double minX, double minY, double minZ, double maxX, double maxY, double maxZ) {
        this.minX = minX;
        this.minY = minY;
        this.minZ = minZ;
        this.maxX = maxX;
        this.maxY = maxY;
        this.maxZ = maxZ;
    }

    public double getMinX() { return minX; }
    public double getMinY() { return minY; }
    public double getMinZ() { return minZ; }
    public double getMaxX() { return maxX; }
    public double getMaxY() { return maxY; }
    public double getMaxZ() { return maxZ; }
}