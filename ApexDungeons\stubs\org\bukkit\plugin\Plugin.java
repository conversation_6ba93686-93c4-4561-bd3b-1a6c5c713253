package org.bukkit.plugin;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.command.PluginCommand;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.plugin.PluginLoader;
import org.bukkit.plugin.PluginDescriptionFile;
import org.bukkit.server.Server;
import java.io.File;
import java.util.logging.Logger;

/**
 * Stub for Plugin interface.
 */
public interface Plugin {
    File getDataFolder();
    PluginLoader getPluginLoader();
    PluginDescriptionFile getDescription();
    void onEnable();
    void onDisable();
    Logger getLogger();
    String getName();
    Server getServer();
    boolean isEnabled();
    void onLoad();
    FileConfiguration getConfig();
    void saveConfig();
    void saveDefaultConfig();
    void reloadConfig();
}