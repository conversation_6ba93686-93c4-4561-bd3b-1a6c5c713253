package com.apexdungeons.templates;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * Manages saving and loading of dungeon templates.
 */
public class DungeonTemplateManager {
    private final ApexDungeons plugin;
    private final File templatesDir;
    private final Map<String, DungeonTemplate> loadedTemplates = new HashMap<>();

    public DungeonTemplateManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.templatesDir = new File(plugin.getDataFolder(), "templates");
        if (!templatesDir.exists()) {
            templatesDir.mkdirs();
        }
        loadAllTemplates();
    }

    /**
     * Save a dungeon as a template.
     */
    public boolean saveDungeonAsTemplate(String dungeonName, String templateName, Player creator) {
        DungeonInstance dungeon = plugin.getDungeonManager().getDungeon(dungeonName);
        if (dungeon == null) {
            plugin.getLogger().warning("Cannot save template: Dungeon '" + dungeonName + "' not found");
            return false;
        }

        World dungeonWorld = dungeon.getOrigin().getWorld();
        if (dungeonWorld == null) {
            plugin.getLogger().warning("Cannot save template: Dungeon world is null");
            return false;
        }

        DungeonTemplate template = new DungeonTemplate(templateName);
        template.setCreator(creator.getName());
        template.setWorldName(dungeonWorld.getName());
        template.setDescription("Template created from dungeon: " + dungeonName);

        // Scan the world for dungeon blocks and portals
        scanDungeonWorld(dungeonWorld, template);

        // Get custom spawn and exit locations from config
        Location customSpawn = plugin.getDungeonConfig().getCustomSpawnLocation(dungeonName);
        if (customSpawn != null) {
            template.setSpawnLocation(customSpawn);
        }

        Location customExit = plugin.getDungeonConfig().getCustomExitLocation(dungeonName);
        if (customExit != null) {
            template.setExitLocation(customExit);
        }

        // Save template settings
        FileConfiguration dungeonConfig = plugin.getDungeonConfig().getDungeonConfig(dungeonName);
        Map<String, Object> settings = new HashMap<>();
        settings.put("max_players", dungeonConfig.getInt("settings.max_players", 4));
        settings.put("time_limit", dungeonConfig.getInt("settings.time_limit", 0));
        settings.put("difficulty", dungeonConfig.getString("settings.difficulty", "normal"));
        template.setSettings(settings);

        // Save to file
        return saveTemplate(template);
    }

    /**
     * Scan dungeon world for important blocks and structures.
     */
    private void scanDungeonWorld(World world, DungeonTemplate template) {
        List<Location> startBlocks = new ArrayList<>();
        List<Location> endBlocks = new ArrayList<>();

        // Get world bounds (scan a reasonable area around spawn)
        Location worldSpawn = world.getSpawnLocation();
        int centerX = worldSpawn.getBlockX();
        int centerZ = worldSpawn.getBlockZ();
        int radius = 500; // Scan 500 blocks in each direction

        plugin.getLogger().info("Scanning world " + world.getName() + " for dungeon blocks...");

        for (int x = centerX - radius; x <= centerX + radius; x += 16) { // Check every 16 blocks for performance
            for (int z = centerZ - radius; z <= centerZ + radius; z += 16) {
                for (int y = 0; y <= world.getMaxHeight(); y += 8) { // Check every 8 blocks vertically
                    Block block = world.getBlockAt(x, y, z);
                    
                    if (block.getType() == Material.EMERALD_BLOCK) {
                        startBlocks.add(block.getLocation());
                        plugin.getLogger().info("Found start block at: " + x + "," + y + "," + z);
                    } else if (block.getType() == Material.DIAMOND_BLOCK) {
                        endBlocks.add(block.getLocation());
                        plugin.getLogger().info("Found end block at: " + x + "," + y + "," + z);
                    }
                }
            }
        }

        template.setStartBlocks(startBlocks);
        template.setEndBlocks(endBlocks);
        
        plugin.getLogger().info("Template scan complete: " + startBlocks.size() + " start blocks, " + endBlocks.size() + " end blocks");
    }

    /**
     * Save template to file.
     */
    public boolean saveTemplate(DungeonTemplate template) {
        try {
            File templateFile = new File(templatesDir, template.getName() + ".yml");
            FileConfiguration config = new YamlConfiguration();
            template.saveToConfig(config);
            config.save(templateFile);
            
            loadedTemplates.put(template.getName(), template);
            plugin.getLogger().info("Saved dungeon template: " + template.getName());
            return true;
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save template " + template.getName() + ": " + e.getMessage());
            return false;
        }
    }

    /**
     * Load template from file.
     */
    public DungeonTemplate loadTemplate(String templateName) {
        if (loadedTemplates.containsKey(templateName)) {
            return loadedTemplates.get(templateName);
        }

        File templateFile = new File(templatesDir, templateName + ".yml");
        if (!templateFile.exists()) {
            return null;
        }

        try {
            FileConfiguration config = YamlConfiguration.loadConfiguration(templateFile);
            DungeonTemplate template = DungeonTemplate.fromConfig(config);
            loadedTemplates.put(templateName, template);
            return template;
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to load template " + templateName + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Load all templates from the templates directory.
     */
    public void loadAllTemplates() {
        loadedTemplates.clear();
        File[] files = templatesDir.listFiles((dir, name) -> name.endsWith(".yml"));
        
        if (files != null) {
            for (File file : files) {
                String templateName = file.getName().replace(".yml", "");
                loadTemplate(templateName);
            }
        }
        
        plugin.getLogger().info("Loaded " + loadedTemplates.size() + " dungeon templates");
    }

    /**
     * Apply template to create a new dungeon.
     */
    public boolean applyTemplate(String templateName, String newDungeonName, World targetWorld) {
        DungeonTemplate template = loadTemplate(templateName);
        if (template == null) {
            plugin.getLogger().warning("Template not found: " + templateName);
            return false;
        }

        try {
            // Create dungeon instance
            Location origin = new Location(targetWorld, 0, 64, 0);
            DungeonInstance newDungeon = new DungeonInstance(plugin, newDungeonName, targetWorld, origin, 1);
            
            // Apply template settings to dungeon config
            FileConfiguration dungeonConfig = plugin.getDungeonConfig().getDungeonConfig(newDungeonName);
            for (Map.Entry<String, Object> setting : template.getSettings().entrySet()) {
                dungeonConfig.set("settings." + setting.getKey(), setting.getValue());
            }
            plugin.getDungeonConfig().saveDungeonConfig(newDungeonName, dungeonConfig);

            // Set custom spawn location if template has one
            if (template.getSpawnLocation() != null) {
                Location spawnLoc = template.getSpawnLocation().clone();
                spawnLoc.setWorld(targetWorld);
                plugin.getDungeonConfig().setCustomSpawnLocation(newDungeonName, spawnLoc);
            }

            // Set custom exit location if template has one
            if (template.getExitLocation() != null) {
                Location exitLoc = template.getExitLocation().clone();
                exitLoc.setWorld(targetWorld);
                plugin.getDungeonConfig().setCustomExitLocation(newDungeonName, exitLoc);
            }

            // Place start and end blocks
            for (Location startBlockLoc : template.getStartBlocks()) {
                Location worldLoc = startBlockLoc.clone();
                worldLoc.setWorld(targetWorld);
                worldLoc.getBlock().setType(Material.EMERALD_BLOCK);
            }

            for (Location endBlockLoc : template.getEndBlocks()) {
                Location worldLoc = endBlockLoc.clone();
                worldLoc.setWorld(targetWorld);
                worldLoc.getBlock().setType(Material.DIAMOND_BLOCK);
            }

            // Register the dungeon
            plugin.getDungeonManager().addDungeon(newDungeon);
            plugin.getWorldManager().registerDungeonWorld(newDungeonName, targetWorld);

            plugin.getLogger().info("Applied template '" + templateName + "' to create dungeon '" + newDungeonName + "'");
            return true;
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to apply template " + templateName + ": " + e.getMessage());
            return false;
        }
    }

    /**
     * Get list of available template names.
     */
    public List<String> getTemplateNames() {
        return new ArrayList<>(loadedTemplates.keySet());
    }

    /**
     * Get template by name.
     */
    public DungeonTemplate getTemplate(String name) {
        return loadedTemplates.get(name);
    }

    /**
     * Delete template.
     */
    public boolean deleteTemplate(String templateName) {
        File templateFile = new File(templatesDir, templateName + ".yml");
        if (templateFile.exists()) {
            boolean deleted = templateFile.delete();
            if (deleted) {
                loadedTemplates.remove(templateName);
                plugin.getLogger().info("Deleted template: " + templateName);
            }
            return deleted;
        }
        return false;
    }

    /**
     * Check if template exists.
     */
    public boolean templateExists(String templateName) {
        return loadedTemplates.containsKey(templateName) || 
               new File(templatesDir, templateName + ".yml").exists();
    }
}
