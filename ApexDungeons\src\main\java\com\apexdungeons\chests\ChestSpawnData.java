package com.apexdungeons.chests;

import com.apexdungeons.ApexDungeons;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages player chest spawn selections and settings.
 */
public class ChestSpawnData {
    private final ApexDungeons plugin;
    private final Map<UUID, String> playerLootTableSelections = new HashMap<>();
    private final Map<UUID, Double> playerRadiusSettings = new HashMap<>();
    
    public ChestSpawnData(ApexDungeons plugin) {
        this.plugin = plugin;
        loadData();
    }
    
    /**
     * Set a player's loot table selection.
     */
    public void setPlayerLootTableSelection(UUID playerId, String lootTable) {
        playerLootTableSelections.put(playerId, lootTable);
        saveData();
    }
    
    /**
     * Get a player's loot table selection.
     */
    public String getPlayerLootTableSelection(UUID playerId) {
        return playerLootTableSelections.get(playerId);
    }
    
    /**
     * Set a player's chest spawn radius setting.
     */
    public void setPlayerRadius(UUID playerId, double radius) {
        playerRadiusSettings.put(playerId, radius);
        saveData();
    }
    
    /**
     * Get a player's chest spawn radius setting.
     */
    public double getPlayerRadius(UUID playerId, double defaultRadius) {
        return playerRadiusSettings.getOrDefault(playerId, defaultRadius);
    }
    
    /**
     * Clear a player's loot table selection.
     */
    public void clearPlayerLootTableSelection(UUID playerId) {
        playerLootTableSelections.remove(playerId);
        saveData();
    }
    
    /**
     * Load data from file.
     */
    private void loadData() {
        File file = new File(plugin.getDataFolder(), "chest_spawn_data.yml");
        if (!file.exists()) return;
        
        FileConfiguration config = YamlConfiguration.loadConfiguration(file);
        
        // Load loot table selections
        if (config.contains("loot_table_selections")) {
            for (String key : config.getConfigurationSection("loot_table_selections").getKeys(false)) {
                try {
                    UUID playerId = UUID.fromString(key);
                    String lootTable = config.getString("loot_table_selections." + key);
                    playerLootTableSelections.put(playerId, lootTable);
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to load loot table selection for " + key + ": " + e.getMessage());
                }
            }
        }
        
        // Load radius settings
        if (config.contains("radius_settings")) {
            for (String key : config.getConfigurationSection("radius_settings").getKeys(false)) {
                try {
                    UUID playerId = UUID.fromString(key);
                    double radius = config.getDouble("radius_settings." + key);
                    playerRadiusSettings.put(playerId, radius);
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to load radius setting for " + key + ": " + e.getMessage());
                }
            }
        }
        
        plugin.getLogger().info("Loaded chest spawn data for " + 
            playerLootTableSelections.size() + " loot table selections, " +
            playerRadiusSettings.size() + " radius settings");
    }
    
    /**
     * Save data to file.
     */
    private void saveData() {
        File file = new File(plugin.getDataFolder(), "chest_spawn_data.yml");
        FileConfiguration config = new YamlConfiguration();
        
        // Save loot table selections
        for (Map.Entry<UUID, String> entry : playerLootTableSelections.entrySet()) {
            config.set("loot_table_selections." + entry.getKey().toString(), entry.getValue());
        }
        
        // Save radius settings
        for (Map.Entry<UUID, Double> entry : playerRadiusSettings.entrySet()) {
            config.set("radius_settings." + entry.getKey().toString(), entry.getValue());
        }
        
        try {
            config.save(file);
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save chest spawn data: " + e.getMessage());
        }
    }
    
    /**
     * Shutdown and save data.
     */
    public void shutdown() {
        saveData();
        playerLootTableSelections.clear();
        playerRadiusSettings.clear();
    }
}
