package com.apexdungeons.chests;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.Chest;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages chest spawn points and handles chest generation with loot.
 */
public class ChestSpawnManager implements Listener {
    private final ApexDungeons plugin;
    private final Map<String, ChestSpawnPoint> chestSpawnPoints = new ConcurrentHashMap<>();
    private final Map<String, Long> lastSpawnTimes = new ConcurrentHashMap<>();
    private final Set<String> activeChests = ConcurrentHashMap.newKeySet();
    
    // Configuration
    private double defaultRadius = 3.0;
    private long spawnCooldown = 60000; // 1 minute
    
    public ChestSpawnManager(ApexDungeons plugin) {
        this.plugin = plugin;
        loadChestSpawnPoints();
        
        // Register event listener
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * Add a chest spawn point.
     */
    public void addChestSpawnPoint(Location location, String lootTable, double radius) {
        String key = locationToKey(location);
        ChestSpawnPoint spawnPoint = new ChestSpawnPoint(location, lootTable, radius);
        chestSpawnPoints.put(key, spawnPoint);
        saveChestSpawnPoints();
        plugin.getLogger().info("Added chest spawn point: " + lootTable + " at " + locationToString(location));
    }
    
    /**
     * Remove a chest spawn point at the given location.
     */
    public boolean removeChestSpawnPoint(Location location) {
        String key = locationToKey(location);
        ChestSpawnPoint removed = chestSpawnPoints.remove(key);
        if (removed != null) {
            saveChestSpawnPoints();
            plugin.getLogger().info("Removed chest spawn point at " + locationToString(location));
            return true;
        }
        return false;
    }
    
    /**
     * Get chest spawn point at location.
     */
    public ChestSpawnPoint getChestSpawnPoint(Location location) {
        String key = locationToKey(location);
        return chestSpawnPoints.get(key);
    }
    
    /**
     * Get all chest spawn points.
     */
    public Collection<ChestSpawnPoint> getAllChestSpawnPoints() {
        return new ArrayList<>(chestSpawnPoints.values());
    }
    
    /**
     * Handle player movement to check for chest spawn triggers.
     */
    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        Location to = event.getTo();
        
        if (to == null) return;
        
        // Check if player moved to a different block
        Location from = event.getFrom();
        if (from.getBlockX() == to.getBlockX() && 
            from.getBlockY() == to.getBlockY() && 
            from.getBlockZ() == to.getBlockZ()) {
            return; // Same block, no need to check
        }
        
        // Check all chest spawn points for proximity
        for (ChestSpawnPoint spawnPoint : chestSpawnPoints.values()) {
            if (!spawnPoint.getLocation().getWorld().equals(to.getWorld())) continue;
            
            double distance = spawnPoint.getLocation().distance(to);
            if (distance <= spawnPoint.getRadius()) {
                triggerChestSpawn(spawnPoint, player);
            }
        }
    }
    
    /**
     * Trigger chest spawning at a spawn point.
     */
    private void triggerChestSpawn(ChestSpawnPoint spawnPoint, Player triggeringPlayer) {
        String key = locationToKey(spawnPoint.getLocation());
        
        // Check cooldown
        Long lastSpawn = lastSpawnTimes.get(key);
        if (lastSpawn != null && System.currentTimeMillis() - lastSpawn < spawnCooldown) {
            return; // Still on cooldown
        }
        
        // Check if already spawning
        if (activeChests.contains(key)) {
            return; // Already spawning
        }
        
        // Check if there's already a chest at this location
        Location chestLoc = spawnPoint.getLocation();
        if (chestLoc.getBlock().getType() == Material.CHEST) {
            return; // Chest already exists
        }
        
        activeChests.add(key);
        lastSpawnTimes.put(key, System.currentTimeMillis());
        
        // Spawn chest
        spawnChest(spawnPoint, triggeringPlayer);
        activeChests.remove(key);
    }
    
    /**
     * Actually spawn the chest with loot.
     */
    private void spawnChest(ChestSpawnPoint spawnPoint, Player triggeringPlayer) {
        Location chestLoc = spawnPoint.getLocation().clone();
        String lootTable = spawnPoint.getLootTable();
        
        // Find safe spawn location (on solid ground)
        chestLoc = findSafeChestLocation(chestLoc);
        
        // Place chest
        Block chestBlock = chestLoc.getBlock();
        chestBlock.setType(Material.CHEST);
        
        // Fill chest with loot
        if (chestBlock.getState() instanceof Chest) {
            Chest chest = (Chest) chestBlock.getState();
            fillChestWithLoot(chest.getInventory(), lootTable);
            
            plugin.getLogger().info("Spawned chest with loot table '" + lootTable + "' at " + 
                locationToString(chestLoc) + " (triggered by " + triggeringPlayer.getName() + ")");
        }
    }
    
    /**
     * Find a safe location to place a chest.
     */
    private Location findSafeChestLocation(Location location) {
        Location safe = location.clone();
        
        // Check if current location is safe (air block with solid block below)
        if (safe.getBlock().getType().isAir() && 
            !safe.clone().add(0, -1, 0).getBlock().getType().isAir()) {
            return safe;
        }
        
        // Try to find safe location nearby
        for (int y = -2; y <= 3; y++) {
            Location test = safe.clone().add(0, y, 0);
            if (test.getBlock().getType().isAir() && 
                !test.clone().add(0, -1, 0).getBlock().getType().isAir()) {
                return test;
            }
        }
        
        return safe; // Return original if no safe location found
    }
    
    /**
     * Fill chest with loot based on loot table.
     */
    private void fillChestWithLoot(Inventory chestInventory, String lootTable) {
        ChestLootTable lootTableData = plugin.getChestLootManager().getLootTable(lootTable);
        if (lootTableData == null) {
            // Use default loot if table not found
            lootTableData = plugin.getChestLootManager().getDefaultLootTable();
        }
        
        if (lootTableData != null) {
            List<ItemStack> loot = lootTableData.generateLoot();
            
            // Randomly place items in chest
            Random random = new Random();
            for (ItemStack item : loot) {
                int slot = random.nextInt(chestInventory.getSize());
                // Try to find an empty slot if the random one is occupied
                for (int i = 0; i < chestInventory.getSize(); i++) {
                    int checkSlot = (slot + i) % chestInventory.getSize();
                    if (chestInventory.getItem(checkSlot) == null) {
                        chestInventory.setItem(checkSlot, item);
                        break;
                    }
                }
            }
        }
    }
    
    /**
     * Convert location to string key.
     */
    private String locationToKey(Location location) {
        return location.getWorld().getName() + ":" + 
               location.getBlockX() + ":" + 
               location.getBlockY() + ":" + 
               location.getBlockZ();
    }
    
    /**
     * Convert location to readable string.
     */
    private String locationToString(Location location) {
        return String.format("%s(%d,%d,%d)", 
            location.getWorld().getName(),
            location.getBlockX(),
            location.getBlockY(),
            location.getBlockZ());
    }
    
    /**
     * Load chest spawn points from file.
     */
    private void loadChestSpawnPoints() {
        File file = new File(plugin.getDataFolder(), "chest_spawns.yml");
        if (!file.exists()) return;
        
        FileConfiguration config = YamlConfiguration.loadConfiguration(file);
        
        for (String key : config.getKeys(false)) {
            try {
                String worldName = config.getString(key + ".world");
                int x = config.getInt(key + ".x");
                int y = config.getInt(key + ".y");
                int z = config.getInt(key + ".z");
                String lootTable = config.getString(key + ".loot_table");
                double radius = config.getDouble(key + ".radius", defaultRadius);
                
                Location location = new Location(Bukkit.getWorld(worldName), x, y, z);
                ChestSpawnPoint spawnPoint = new ChestSpawnPoint(location, lootTable, radius);
                chestSpawnPoints.put(key, spawnPoint);
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to load chest spawn point " + key + ": " + e.getMessage());
            }
        }
        
        plugin.getLogger().info("Loaded " + chestSpawnPoints.size() + " chest spawn points");
    }
    
    /**
     * Save chest spawn points to file.
     */
    private void saveChestSpawnPoints() {
        File file = new File(plugin.getDataFolder(), "chest_spawns.yml");
        FileConfiguration config = new YamlConfiguration();
        
        for (Map.Entry<String, ChestSpawnPoint> entry : chestSpawnPoints.entrySet()) {
            String key = entry.getKey();
            ChestSpawnPoint point = entry.getValue();
            Location loc = point.getLocation();
            
            config.set(key + ".world", loc.getWorld().getName());
            config.set(key + ".x", loc.getBlockX());
            config.set(key + ".y", loc.getBlockY());
            config.set(key + ".z", loc.getBlockZ());
            config.set(key + ".loot_table", point.getLootTable());
            config.set(key + ".radius", point.getRadius());
        }
        
        try {
            config.save(file);
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save chest spawn points: " + e.getMessage());
        }
    }
    
    /**
     * Shutdown the manager.
     */
    public void shutdown() {
        saveChestSpawnPoints();
        chestSpawnPoints.clear();
        lastSpawnTimes.clear();
        activeChests.clear();
    }
}
