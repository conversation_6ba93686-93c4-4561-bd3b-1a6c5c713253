package org.bukkit.scheduler;

import org.bukkit.plugin.Plugin;

/**
 * Stub for BukkitRunnable.  Provides runTaskTimer method which executes the
 * runnable synchronously in this stub implementation.
 */
public abstract class BukkitRunnable implements Runnable {
    public void runTask(Plugin plugin) { run(); }
    public void runTaskTimer(Plugin plugin, long delay, long period) { run(); }
    public void cancel() {}
}