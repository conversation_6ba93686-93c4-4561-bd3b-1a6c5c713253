package com.apexdungeons.config;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Manages dungeon-specific configurations including spawn points and exit destinations.
 */
public class DungeonConfig {
    private final ApexDungeons plugin;
    private final Map<String, FileConfiguration> dungeonConfigs = new HashMap<>();
    private final File configDir;

    public DungeonConfig(ApexDungeons plugin) {
        this.plugin = plugin;
        this.configDir = new File(plugin.getDataFolder(), "dungeons");
        if (!configDir.exists()) {
            configDir.mkdirs();
        }
    }

    /**
     * Get or create configuration for a specific dungeon.
     */
    public FileConfiguration getDungeonConfig(String dungeonName) {
        if (!dungeonConfigs.containsKey(dungeonName)) {
            File configFile = new File(configDir, dungeonName + ".yml");
            FileConfiguration config = YamlConfiguration.loadConfiguration(configFile);
            
            // Set default values if file doesn't exist
            if (!configFile.exists()) {
                setDefaults(config, dungeonName);
                saveDungeonConfig(dungeonName, config);
            }
            
            dungeonConfigs.put(dungeonName, config);
        }
        return dungeonConfigs.get(dungeonName);
    }

    /**
     * Save dungeon configuration to file.
     */
    public void saveDungeonConfig(String dungeonName, FileConfiguration config) {
        try {
            File configFile = new File(configDir, dungeonName + ".yml");
            config.save(configFile);
            dungeonConfigs.put(dungeonName, config);
            plugin.getLogger().info("Saved configuration for dungeon: " + dungeonName);
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save configuration for dungeon " + dungeonName + ": " + e.getMessage());
        }
    }

    /**
     * Set default configuration values for a new dungeon.
     */
    private void setDefaults(FileConfiguration config, String dungeonName) {
        config.set("dungeon.name", dungeonName);
        config.set("dungeon.created", System.currentTimeMillis());
        config.set("spawn.use_custom", false);
        config.set("spawn.world", "");
        config.set("spawn.x", 0.0);
        config.set("spawn.y", 64.0);
        config.set("spawn.z", 0.0);
        config.set("spawn.yaw", 0.0f);
        config.set("spawn.pitch", 0.0f);
        config.set("exit.use_custom", false);
        config.set("exit.world", "");
        config.set("exit.x", 0.0);
        config.set("exit.y", 64.0);
        config.set("exit.z", 0.0);
        config.set("exit.yaw", 0.0f);
        config.set("exit.pitch", 0.0f);
        config.set("settings.max_players", 4);
        config.set("settings.time_limit", 0); // 0 = no limit
        config.set("settings.difficulty", "normal");
    }

    /**
     * Set custom spawn location for a dungeon.
     */
    public void setCustomSpawnLocation(String dungeonName, Location location) {
        FileConfiguration config = getDungeonConfig(dungeonName);
        config.set("spawn.use_custom", true);
        config.set("spawn.world", location.getWorld().getName());
        config.set("spawn.x", location.getX());
        config.set("spawn.y", location.getY());
        config.set("spawn.z", location.getZ());
        config.set("spawn.yaw", location.getYaw());
        config.set("spawn.pitch", location.getPitch());
        saveDungeonConfig(dungeonName, config);
    }

    /**
     * Set custom exit destination for a dungeon.
     */
    public void setCustomExitLocation(String dungeonName, Location location) {
        FileConfiguration config = getDungeonConfig(dungeonName);
        config.set("exit.use_custom", true);
        config.set("exit.world", location.getWorld().getName());
        config.set("exit.x", location.getX());
        config.set("exit.y", location.getY());
        config.set("exit.z", location.getZ());
        config.set("exit.yaw", location.getYaw());
        config.set("exit.pitch", location.getPitch());
        saveDungeonConfig(dungeonName, config);
    }

    /**
     * Get custom spawn location for a dungeon.
     */
    public Location getCustomSpawnLocation(String dungeonName) {
        FileConfiguration config = getDungeonConfig(dungeonName);
        if (!config.getBoolean("spawn.use_custom", false)) {
            return null;
        }

        String worldName = config.getString("spawn.world");
        World world = Bukkit.getWorld(worldName);
        if (world == null) {
            plugin.getLogger().warning("World not found for custom spawn: " + worldName);
            return null;
        }

        double x = config.getDouble("spawn.x");
        double y = config.getDouble("spawn.y");
        double z = config.getDouble("spawn.z");
        float yaw = (float) config.getDouble("spawn.yaw");
        float pitch = (float) config.getDouble("spawn.pitch");

        return new Location(world, x, y, z, yaw, pitch);
    }

    /**
     * Get custom exit location for a dungeon.
     */
    public Location getCustomExitLocation(String dungeonName) {
        FileConfiguration config = getDungeonConfig(dungeonName);
        if (!config.getBoolean("exit.use_custom", false)) {
            return null;
        }

        String worldName = config.getString("exit.world");
        World world = Bukkit.getWorld(worldName);
        if (world == null) {
            plugin.getLogger().warning("World not found for custom exit: " + worldName);
            return null;
        }

        double x = config.getDouble("exit.x");
        double y = config.getDouble("exit.y");
        double z = config.getDouble("exit.z");
        float yaw = (float) config.getDouble("exit.yaw");
        float pitch = (float) config.getDouble("exit.pitch");

        return new Location(world, x, y, z, yaw, pitch);
    }

    /**
     * Remove custom spawn location for a dungeon.
     */
    public void removeCustomSpawnLocation(String dungeonName) {
        FileConfiguration config = getDungeonConfig(dungeonName);
        config.set("spawn.use_custom", false);
        saveDungeonConfig(dungeonName, config);
    }

    /**
     * Remove custom exit location for a dungeon.
     */
    public void removeCustomExitLocation(String dungeonName) {
        FileConfiguration config = getDungeonConfig(dungeonName);
        config.set("exit.use_custom", false);
        saveDungeonConfig(dungeonName, config);
    }

    /**
     * Check if dungeon has custom spawn location.
     */
    public boolean hasCustomSpawnLocation(String dungeonName) {
        FileConfiguration config = getDungeonConfig(dungeonName);
        return config.getBoolean("spawn.use_custom", false);
    }

    /**
     * Check if dungeon has custom exit location.
     */
    public boolean hasCustomExitLocation(String dungeonName) {
        FileConfiguration config = getDungeonConfig(dungeonName);
        return config.getBoolean("exit.use_custom", false);
    }

    /**
     * Get all dungeon configuration files.
     */
    public Map<String, FileConfiguration> getAllDungeonConfigs() {
        Map<String, FileConfiguration> configs = new HashMap<>();
        File[] files = configDir.listFiles((dir, name) -> name.endsWith(".yml"));
        
        if (files != null) {
            for (File file : files) {
                String dungeonName = file.getName().replace(".yml", "");
                configs.put(dungeonName, getDungeonConfig(dungeonName));
            }
        }
        
        return configs;
    }

    /**
     * Delete dungeon configuration.
     */
    public void deleteDungeonConfig(String dungeonName) {
        File configFile = new File(configDir, dungeonName + ".yml");
        if (configFile.exists()) {
            configFile.delete();
        }
        dungeonConfigs.remove(dungeonName);
    }
}
