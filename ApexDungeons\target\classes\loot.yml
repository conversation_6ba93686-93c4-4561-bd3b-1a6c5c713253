# ------------------------------------------------------------------------------
# Loot configuration for ApexDungeons
# Loot tables define the weighted items which can appear in dungeon chests. You
# can create additional tables and reference them from your room blueprints.
# Each table defines entries with an item and weight. The weight determines
# how likely that item is to appear relative to the other entries.
# ------------------------------------------------------------------------------

tables:
  basic:
    - item: minecraft:bread
      amount: 3
      weight: 30
    - item: minecraft:iron_ingot
      amount: 2
      weight: 20
    - item: minecraft:torch
      amount: 8
      weight: 50
  rare:
    - item: minecraft:golden_apple
      amount: 1
      weight: 10
    - item: minecraft:ender_pearl
      amount: 2
      weight: 20
    - item: minecraft:diamond
      amount: 1
      weight: 5
  epic:
    - item: minecraft:netherite_ingot
      amount: 1
      weight: 1
    - item: minecraft:elytra
      amount: 1
      weight: 1