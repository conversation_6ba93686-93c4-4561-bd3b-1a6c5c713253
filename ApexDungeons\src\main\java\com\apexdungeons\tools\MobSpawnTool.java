package com.apexdungeons.tools;

import com.apexdungeons.ApexDungeons;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;

import java.util.ArrayList;
import java.util.List;

/**
 * Tool for placing mob spawn points in dungeons.
 * Players can right-click blocks to set invisible spawn triggers.
 */
public class MobSpawnTool {
    private final ApexDungeons plugin;
    private final NamespacedKey toolKey;

    public MobSpawnTool(ApexDungeons plugin) {
        this.plugin = plugin;
        this.toolKey = new NamespacedKey(plugin, "mob_spawn_tool");
    }

    /**
     * Create a mob spawn tool item.
     */
    public ItemStack createMobSpawnTool() {
        ItemStack tool = new ItemStack(Material.ZOMBIE_HEAD);
        ItemMeta meta = tool.getItemMeta();
        
        meta.setDisplayName(ChatColor.RED + "Mob Spawn Tool");
        
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "Right-click blocks to set mob spawn points");
        lore.add(ChatColor.GRAY + "Use /dgn mobspawn set <mob_name> to configure");
        lore.add("");
        lore.add(ChatColor.YELLOW + "📍 How to use:");
        lore.add(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Right-click a block to mark spawn point");
        lore.add(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Use /dgn mobspawn set <mob_name>");
        lore.add(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Players trigger spawns by proximity");
        lore.add("");
        lore.add(ChatColor.GREEN + "💡 Supports MythicMobs if installed!");
        
        meta.setLore(lore);
        
        // Mark as mob spawn tool
        meta.getPersistentDataContainer().set(toolKey, PersistentDataType.BYTE, (byte) 1);
        
        tool.setItemMeta(meta);
        return tool;
    }

    /**
     * Create a boss spawn tool item.
     */
    public ItemStack createBossSpawnTool() {
        ItemStack tool = new ItemStack(Material.WITHER_SKELETON_SKULL);
        ItemMeta meta = tool.getItemMeta();
        
        meta.setDisplayName(ChatColor.DARK_RED + "Boss Spawn Tool");
        
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "Right-click blocks to set boss spawn points");
        lore.add(ChatColor.GRAY + "Use /dgn bossspawn set <boss_name> to configure");
        lore.add("");
        lore.add(ChatColor.YELLOW + "📍 How to use:");
        lore.add(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Right-click a block to mark boss spawn");
        lore.add(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Use /dgn bossspawn set <boss_name>");
        lore.add(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Players trigger spawns by proximity");
        lore.add("");
        lore.add(ChatColor.GREEN + "💡 Supports MythicMobs bosses!");
        
        meta.setLore(lore);
        
        // Mark as boss spawn tool
        meta.getPersistentDataContainer().set(new NamespacedKey(plugin, "boss_spawn_tool"), 
            PersistentDataType.BYTE, (byte) 1);
        
        tool.setItemMeta(meta);
        return tool;
    }

    /**
     * Check if an item is a mob spawn tool.
     */
    public boolean isMobSpawnTool(ItemStack item) {
        if (item == null || !item.hasItemMeta()) return false;
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().has(toolKey, PersistentDataType.BYTE);
    }

    /**
     * Check if an item is a boss spawn tool.
     */
    public boolean isBossSpawnTool(ItemStack item) {
        if (item == null || !item.hasItemMeta()) return false;
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().has(new NamespacedKey(plugin, "boss_spawn_tool"), 
            PersistentDataType.BYTE);
    }

    /**
     * Give a mob spawn tool to a player.
     */
    public void giveMobSpawnTool(Player player) {
        player.getInventory().addItem(createMobSpawnTool());
    }

    /**
     * Give a boss spawn tool to a player.
     */
    public void giveBossSpawnTool(Player player) {
        player.getInventory().addItem(createBossSpawnTool());
    }
}
