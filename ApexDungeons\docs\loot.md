# Loot Tables

Loot tables determine what treasure players will find inside dungeon chests.  ApexDungeons uses simple weighted
tables defined in `loot.yml`.  Each table contains a list of entries with an `item`, an `amount` and a `weight`.

```yaml
tables:
  basic:
    - item: minecraft:bread
      amount: 3
      weight: 30
    - item: minecraft:iron_ingot
      amount: 2
      weight: 20
    - item: minecraft:torch
      amount: 8
      weight: 50
  rare:
    - item: minecraft:golden_apple
      amount: 1
      weight: 10
    - item: minecraft:ender_pearl
      amount: 2
      weight: 20
    - item: minecraft:diamond
      amount: 1
      weight: 5
```

### How Loot is Selected

When a chest is filled the plugin picks one or more tables from the `loot.tables.default` list in `config.yml` and
draws items from those tables based on their weights.  The higher the weight, the more likely the item appears.

Rooms can override which tables they use by defining a `loot` section with explicit table names.  For example:

```yaml
loot:
  tables:
    - epic
    - rare
  spots:
    - [3, 1, 4]  # x, y, z coordinate for a chest inside the room
    - [5, 1, 2]
```

### Adding Your Own Loot

You can create new tables by adding more entries under `tables:` in `loot.yml`.  Use any valid Bukkit material
identifier (e.g. `minecraft:emerald`, `minecraft:netherite_sword`).  Adjust the `amount` and `weight` values to
tune how much and how often the item appears.

After editing `loot.yml` simply reload the plugin (`/reload confirm` or restart the server) for changes to take
effect.  If you add new tables make sure to reference them in your room blueprints or in the `loot.tables.default`
list so that they are used when filling chests.