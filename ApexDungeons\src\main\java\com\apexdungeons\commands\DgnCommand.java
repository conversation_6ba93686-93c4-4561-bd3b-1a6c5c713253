package com.apexdungeons.commands;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.AdminGUI;
import com.apexdungeons.gui.BuildingToolsGUI;
import com.apexdungeons.gui.DungeonManagementGUI;
import com.apexdungeons.gui.EnhancedMainGUI;
import com.apexdungeons.gui.MainGUI;
import com.apexdungeons.gui.StatisticsGUI;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Command handler for /dgn.  This class parses user input and dispatches
 * appropriate actions such as opening GUIs or managing dungeons.
 */
public class DgnCommand implements CommandExecutor, TabCompleter {
    private final ApexDungeons plugin;

    public DgnCommand(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, @NotNull String label, @NotNull String[] args) {
        if (args.length == 0) {
            // /dgn
            if (!(sender instanceof Player player)) {
                sender.sendMessage(ChatColor.RED + "You must be a player to use this command.");
                return true;
            }
            if (!player.hasPermission("apexdungeons.use")) {
                player.sendMessage(ChatColor.RED + "You don't have permission to use Soaps Dungeons.");
                return true;
            }
            EnhancedMainGUI.open(player, plugin);
            return true;
        }
        String sub = args[0].toLowerCase();
        switch (sub) {
            case "admin":
                return handleAdmin(sender);
            case "manage":
                return handleManage(sender);
            case "create":
                return handleCreate(sender, args);
            case "delete":
            case "remove":
                return handleDelete(sender, args);
            case "list":
                return handleList(sender, args);
            case "tp":
            case "teleport":
                return handleTeleport(sender, args);
            case "info":
                return handleInfo(sender, args);
            case "portal":
                return handlePortal(sender, args);
            case "stats":
                return handleStats(sender);
            case "reload":
                return handleReload(sender);
            case "reloadschematics":
                return handleReloadSchematics(sender);
            case "testworld":
                return handleTestWorld(sender);
            case "tools":
                return handleTools(sender);
            case "version":
                return handleVersion(sender);
            case "help":
                return handleHelp(sender, args);
            case "setspawn":
                return handleSetSpawn(sender, args);
            case "setexit":
                return handleSetExit(sender, args);
            case "save":
                return handleSave(sender, args);
            case "load":
                return handleLoad(sender, args);
            case "party":
                return handleParty(sender, args);
            case "mobspawn":
                return handleMobSpawn(sender, args);
            case "bossspawn":
                return handleBossSpawn(sender, args);
            case "chestspawn":
                return handleChestSpawn(sender, args);
            case "givewand":
                if (!sender.hasPermission("apexdungeons.admin")) {
                    sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
                    return true;
                }
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.YELLOW + "Usage: /dgn givewand <player>");
                    return true;
                }
                Player target = Bukkit.getPlayerExact(args[1]);
                if (target == null) {
                    sender.sendMessage(ChatColor.RED + "Player not found.");
                    return true;
                }
                plugin.getWandManager().giveWand(target);
                sender.sendMessage(ChatColor.GREEN + "Gave a wand to " + target.getName());
                return true;
            case "leave":
                return handleLeave(sender);
            case "start":
                return handleStart(sender, args);
            default:
                sender.sendMessage(ChatColor.RED + "Unknown sub command.");
                return true;
        }
    }

    // Command handler methods
    private boolean handleAdmin(CommandSender sender) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "You must be a player to use this command.");
            return true;
        }
        if (!player.hasPermission("apexdungeons.admin")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to use the admin GUI.");
            return true;
        }
        AdminGUI.open(player, plugin);
        return true;
    }

    private boolean handleManage(CommandSender sender) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "You must be a player to use this command.");
            return true;
        }
        if (!player.hasPermission("apexdungeons.use")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to manage dungeons.");
            return true;
        }
        DungeonManagementGUI.open(player, plugin);
        return true;
    }

    private boolean handleCreate(CommandSender sender, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "You must be a player to create dungeons.");
            return true;
        }
        if (!player.hasPermission("apexdungeons.create")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to create dungeons.");
            return true;
        }

        if (args.length < 2) {
            player.sendMessage(ChatColor.YELLOW + "Usage: /dgn create <name>");
            player.sendMessage(ChatColor.GRAY + "Creates a new dungeon with the specified name");
            return true;
        }

        String dungeonName = args[1];

        // Validate dungeon name
        if (dungeonName.length() < 3 || dungeonName.length() > 32) {
            player.sendMessage(ChatColor.RED + "Dungeon name must be 3-32 characters long!");
            return true;
        }

        if (!dungeonName.matches("[a-zA-Z0-9_-]+")) {
            player.sendMessage(ChatColor.RED + "Dungeon name can only contain letters, numbers, hyphens, and underscores!");
            return true;
        }

        // Create the dungeon directly (no preset selection)
        plugin.getDungeonManager().createDungeon(dungeonName, player);
        return true;
    }

    private boolean handleDelete(CommandSender sender, String[] args) {
        if (!sender.hasPermission("apexdungeons.delete")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to delete dungeons.");
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.YELLOW + "Usage: /dgn delete <dungeon_name>");
            return true;
        }

        String dungeonName = args[1];
        if (!plugin.getDungeonManager().getDungeons().containsKey(dungeonName)) {
            sender.sendMessage(ChatColor.RED + "Dungeon '" + dungeonName + "' not found.");
            return true;
        }

        plugin.getDungeonManager().removeDungeon(dungeonName, sender);
        return true;
    }

    private boolean handleList(CommandSender sender, String[] args) {
        if (!sender.hasPermission("apexdungeons.list")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to list dungeons.");
            return true;
        }

        var dungeons = plugin.getDungeonManager().getDungeons();
        if (dungeons.isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + "No active dungeons found.");
            return true;
        }

        sender.sendMessage(ChatColor.GOLD + "=== Active Dungeons ===");
        for (var entry : dungeons.entrySet()) {
            var dungeon = entry.getValue();
            String status = dungeon.isGenerating() ? ChatColor.YELLOW + "Generating" : ChatColor.GREEN + "Ready";
            sender.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + dungeon.getDisplayName() +
                ChatColor.GRAY + " (" + dungeon.getCreator() + ") - " + status);
        }
        return true;
    }

    private boolean handleTeleport(CommandSender sender, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "You must be a player to teleport.");
            return true;
        }
        if (!player.hasPermission("apexdungeons.teleport")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to teleport to dungeons.");
            return true;
        }

        if (args.length < 2) {
            player.sendMessage(ChatColor.YELLOW + "Usage: /dgn tp <dungeon_name>");
            return true;
        }

        String dungeonName = args[1];
        var dungeon = plugin.getDungeonManager().getDungeon(dungeonName);
        if (dungeon == null) {
            player.sendMessage(ChatColor.RED + "Dungeon '" + dungeonName + "' not found.");
            return true;
        }

        if (dungeon.isGenerating()) {
            player.sendMessage(ChatColor.YELLOW + "Dungeon is still generating. Please wait...");
            return true;
        }

        var spawnLoc = plugin.getWorldManager().getDungeonSpawnLocation(dungeonName);
        if (spawnLoc != null) {
            plugin.getEffectsManager().playDungeonEntryEffects(player, dungeon);
            player.teleport(spawnLoc);
            dungeon.addPlayer(player);
            player.sendMessage(ChatColor.GREEN + "Teleported to dungeon '" + dungeon.getDisplayName() + "'!");
        } else {
            player.sendMessage(ChatColor.RED + "Failed to find dungeon spawn location!");
        }
        return true;
    }

    private boolean handleInfo(CommandSender sender, String[] args) {
        if (!sender.hasPermission("apexdungeons.info")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to view dungeon info.");
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.YELLOW + "Usage: /dgn info <dungeon_name>");
            return true;
        }

        String dungeonName = args[1];
        var dungeon = plugin.getDungeonManager().getDungeon(dungeonName);
        if (dungeon == null) {
            sender.sendMessage(ChatColor.RED + "Dungeon '" + dungeonName + "' not found.");
            return true;
        }

        sender.sendMessage(ChatColor.GOLD + "=== Dungeon Information ===");
        sender.sendMessage(ChatColor.YELLOW + "Name: " + ChatColor.WHITE + dungeon.getDisplayName());
        sender.sendMessage(ChatColor.YELLOW + "Creator: " + ChatColor.WHITE + dungeon.getCreator());
        sender.sendMessage(ChatColor.YELLOW + "World: " + ChatColor.WHITE + dungeon.getWorld().getName());
        sender.sendMessage(ChatColor.YELLOW + "Rooms: " + ChatColor.WHITE + dungeon.getRoomCount());
        sender.sendMessage(ChatColor.YELLOW + "Status: " + (dungeon.isGenerating() ?
            ChatColor.GOLD + "Generating" : ChatColor.GREEN + "Ready"));
        sender.sendMessage(ChatColor.YELLOW + "Players: " + ChatColor.WHITE + dungeon.getPlayers().size());
        return true;
    }

    private boolean handlePortal(CommandSender sender, String[] args) {
        sender.sendMessage(ChatColor.YELLOW + "Portal management coming soon!");
        return true;
    }

    private boolean handleStats(CommandSender sender) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "You must be a player to view statistics.");
            return true;
        }
        StatisticsGUI.open(player, plugin);
        return true;
    }

    private boolean handleReload(CommandSender sender) {
        if (!sender.hasPermission("apexdungeons.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to reload the plugin.");
            return true;
        }
        sender.sendMessage(ChatColor.YELLOW + "Reloading Soaps Dungeons...");
        // TODO: Implement reload functionality
        sender.sendMessage(ChatColor.GREEN + "Soaps Dungeons reloaded successfully!");
        return true;
    }

    private boolean handleReloadSchematics(CommandSender sender) {
        if (!sender.hasPermission("apexdungeons.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to reload schematics.");
            return true;
        }

        sender.sendMessage(ChatColor.YELLOW + "Reloading schematics from folder...");
        plugin.getSchematicManager().reloadSchematics();

        int count = plugin.getSchematicManager().getLoadedSchematicNames().size();
        sender.sendMessage(ChatColor.GREEN + "Schematics reloaded! " + count + " schematics available.");
        sender.sendMessage(ChatColor.GRAY + "Available: " + String.join(", ", plugin.getSchematicManager().getLoadedSchematicNames()));

        return true;
    }

    private boolean handleTestWorld(CommandSender sender) {
        if (!sender.hasPermission("apexdungeons.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to test world generation.");
            return true;
        }

        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "You must be a player to test world generation.");
            return true;
        }

        sender.sendMessage(ChatColor.YELLOW + "Creating test superflat world...");

        plugin.getWorldManager().createDungeonWorld("test").thenAccept(world -> {
            if (world != null) {
                Location spawnLoc = new Location(world, 0, 64, 0);
                player.teleport(spawnLoc);
                player.sendMessage(ChatColor.GREEN + "Test world created! You've been teleported to: " + world.getName());
                player.sendMessage(ChatColor.GRAY + "Check if the world is properly superflat. Use /dgn leave to return.");

                // Store location for leave command
                plugin.getPlayerLocationManager().storePlayerLocation(player, "test");
            } else {
                player.sendMessage(ChatColor.RED + "Failed to create test world!");
            }
        });

        return true;
    }

    private boolean handleTools(CommandSender sender) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "You must be a player to access building tools.");
            return true;
        }

        player.closeInventory();
        BuildingToolsGUI.open(player, plugin);
        return true;
    }

    private boolean handleVersion(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "Soaps Dungeons " + ChatColor.WHITE + "v1.0.0 " + ChatColor.GRAY + "- Made by Vexy");
        sender.sendMessage(ChatColor.GRAY + "Enhanced dungeon creation and management plugin");
        return true;
    }

    private boolean handleLeave(CommandSender sender) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "You must be a player to use this command.");
            return true;
        }

        // Check if player is in a dungeon world
        if (!plugin.getWorldManager().isDungeonWorld(player.getWorld())) {
            player.sendMessage(ChatColor.RED + "You are not currently in a dungeon!");
            return true;
        }

        // Get stored return location
        Location returnLocation = plugin.getPlayerLocationManager().getPlayerReturnLocation(player);
        if (returnLocation == null) {
            // Fallback to main world spawn if no stored location
            returnLocation = plugin.getServer().getWorlds().get(0).getSpawnLocation();
            player.sendMessage(ChatColor.YELLOW + "No return location found, teleporting to main world spawn.");
        }

        // Remove player from current dungeon
        String dungeonName = plugin.getWorldManager().getDungeonNameFromWorld(player.getWorld());
        if (dungeonName != null) {
            var dungeon = plugin.getDungeonManager().getDungeon(dungeonName);
            if (dungeon != null) {
                dungeon.removePlayer(player);
            }
        }

        // Clear stored location data
        plugin.getPlayerLocationManager().clearPlayerLocation(player);

        // Teleport with effects
        plugin.getEffectsManager().playDungeonExitEffects(player, dungeonName != null ? dungeonName : "Unknown");
        player.teleport(returnLocation);
        player.sendMessage(ChatColor.GREEN + "You have left the dungeon and returned to your previous location!");

        return true;
    }

    private boolean handleStart(CommandSender sender, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "You must be a player to use this command.");
            return true;
        }

        if (args.length < 2) {
            player.sendMessage(ChatColor.YELLOW + "Usage: /dgn start <dungeon_name>");
            return true;
        }

        String dungeonName = args[1];
        var dungeon = plugin.getDungeonManager().getDungeon(dungeonName);
        if (dungeon == null) {
            player.sendMessage(ChatColor.RED + "Dungeon '" + dungeonName + "' not found!");
            return true;
        }

        if (dungeon.isGenerating()) {
            player.sendMessage(ChatColor.YELLOW + "Dungeon is still generating. Please wait...");
            return true;
        }

        // Store player's current location before entering dungeon
        plugin.getPlayerLocationManager().storePlayerLocation(player, dungeonName);

        // Teleport to dungeon
        Location spawnLocation = plugin.getWorldManager().getDungeonSpawnLocation(dungeonName);
        if (spawnLocation == null) {
            player.sendMessage(ChatColor.RED + "Failed to find dungeon spawn location!");
            plugin.getPlayerLocationManager().clearPlayerLocation(player); // Clean up stored location
            return true;
        }

        // Add player to dungeon and teleport
        dungeon.addPlayer(player);
        plugin.getEffectsManager().playDungeonEntryEffects(player, dungeon);
        player.teleport(spawnLocation);
        player.sendMessage(ChatColor.GREEN + "Started dungeon '" + dungeon.getDisplayName() + "'! Use /dgn leave to exit.");

        return true;
    }

    private boolean handleHelp(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Help guides are only available to players.");
            return true;
        }

        Player player = (Player) sender;

        // Check for specific help topics
        if (args.length >= 2) {
            String topic = args[1].toLowerCase();
            switch (topic) {
                case "rooms":
                    showRoomGuide(player);
                    return true;
                case "connections":
                    showConnectionGuide(player);
                    return true;
                case "blocks":
                    showBlocksGuide(player);
                    return true;
                case "schematics":
                    showSchematicsGuide(player);
                    return true;
                case "guide":
                case "system":
                    showMainGuide(player);
                    return true;
                default:
                    player.sendMessage(ChatColor.RED + "Unknown help topic: " + topic);
                    player.sendMessage(ChatColor.YELLOW + "Available topics: rooms, connections, blocks, schematics, guide");
                    return true;
            }
        }

        // Show command help and room system overview
        sender.sendMessage(ChatColor.GOLD + "=== Soaps Dungeons Commands - Made by Vexy ===");
        sender.sendMessage(ChatColor.YELLOW + "/dgn" + ChatColor.WHITE + " - Open main GUI");
        sender.sendMessage(ChatColor.YELLOW + "/dgn manage" + ChatColor.WHITE + " - Open dungeon management");
        sender.sendMessage(ChatColor.YELLOW + "/dgn create <name>" + ChatColor.WHITE + " - Create a flat dungeon world");
        sender.sendMessage(ChatColor.YELLOW + "/dgn delete <name>" + ChatColor.WHITE + " - Delete a dungeon");
        sender.sendMessage(ChatColor.YELLOW + "/dgn list" + ChatColor.WHITE + " - List active dungeons");
        sender.sendMessage(ChatColor.YELLOW + "/dgn tp <name>" + ChatColor.WHITE + " - Teleport to dungeon");
        sender.sendMessage(ChatColor.YELLOW + "/dgn start <name>" + ChatColor.WHITE + " - Start/enter a dungeon");
        sender.sendMessage(ChatColor.YELLOW + "/dgn leave" + ChatColor.WHITE + " - Leave current dungeon");
        sender.sendMessage(ChatColor.YELLOW + "/dgn tools" + ChatColor.WHITE + " - Open building tools GUI");
        sender.sendMessage(ChatColor.YELLOW + "/dgn party" + ChatColor.WHITE + " - Party management system");
        sender.sendMessage(ChatColor.YELLOW + "/dgn info <name>" + ChatColor.WHITE + " - View dungeon info");
        sender.sendMessage(ChatColor.YELLOW + "/dgn stats" + ChatColor.WHITE + " - View your statistics");
        sender.sendMessage(ChatColor.YELLOW + "/dgn version" + ChatColor.WHITE + " - Show plugin version");
        if (sender.hasPermission("apexdungeons.admin")) {
            sender.sendMessage(ChatColor.RED + "/dgn admin" + ChatColor.WHITE + " - Admin tools");
            sender.sendMessage(ChatColor.RED + "/dgn reload" + ChatColor.WHITE + " - Reload plugin");
            sender.sendMessage(ChatColor.RED + "/dgn reloadschematics" + ChatColor.WHITE + " - Reload schematic files");
            sender.sendMessage(ChatColor.RED + "/dgn testworld" + ChatColor.WHITE + " - Create test superflat world");
            sender.sendMessage(ChatColor.RED + "/dgn setspawn <dungeon>" + ChatColor.WHITE + " - Set custom spawn location");
            sender.sendMessage(ChatColor.RED + "/dgn setexit <dungeon>" + ChatColor.WHITE + " - Set custom exit location");
            sender.sendMessage(ChatColor.RED + "/dgn save <dungeon> <template>" + ChatColor.WHITE + " - Save dungeon as template");
            sender.sendMessage(ChatColor.RED + "/dgn load <template> <dungeon>" + ChatColor.WHITE + " - Load template as new dungeon");
        }
        sender.sendMessage("");
        sender.sendMessage(ChatColor.GREEN + "🏰 ROOM SYSTEM GUIDES:");
        sender.sendMessage(ChatColor.AQUA + "/dgn help guide" + ChatColor.WHITE + " - Complete room system overview");
        sender.sendMessage(ChatColor.AQUA + "/dgn help rooms" + ChatColor.WHITE + " - Room types and design");
        sender.sendMessage(ChatColor.AQUA + "/dgn help connections" + ChatColor.WHITE + " - Connecting rooms");
        sender.sendMessage(ChatColor.AQUA + "/dgn help blocks" + ChatColor.WHITE + " - Start/End blocks");
        sender.sendMessage(ChatColor.AQUA + "/dgn help schematics" + ChatColor.WHITE + " - Using schematics");
        return true;
    }

    private boolean handleSetSpawn(CommandSender sender, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return true;
        }

        if (!player.hasPermission("apexdungeons.admin")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }

        if (args.length < 2) {
            player.sendMessage(ChatColor.YELLOW + "Usage: /dgn setspawn <dungeon_name>");
            return true;
        }

        String dungeonName = args[1];

        // Check if dungeon exists
        if (plugin.getDungeonManager().getDungeon(dungeonName) == null) {
            player.sendMessage(ChatColor.RED + "Dungeon '" + dungeonName + "' not found!");
            return true;
        }

        // Set custom spawn location to player's current location
        Location currentLocation = player.getLocation();
        plugin.getDungeonConfig().setCustomSpawnLocation(dungeonName, currentLocation);

        player.sendMessage(ChatColor.GREEN + "Custom spawn location set for dungeon '" + dungeonName + "'!");
        player.sendMessage(ChatColor.GRAY + "Location: " + currentLocation.getWorld().getName() +
            " " + currentLocation.getBlockX() + "," + currentLocation.getBlockY() + "," + currentLocation.getBlockZ());

        return true;
    }

    private boolean handleSetExit(CommandSender sender, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return true;
        }

        if (!player.hasPermission("apexdungeons.admin")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }

        if (args.length < 2) {
            player.sendMessage(ChatColor.YELLOW + "Usage: /dgn setexit <dungeon_name>");
            return true;
        }

        String dungeonName = args[1];

        // Check if dungeon exists
        if (plugin.getDungeonManager().getDungeon(dungeonName) == null) {
            player.sendMessage(ChatColor.RED + "Dungeon '" + dungeonName + "' not found!");
            return true;
        }

        // Set custom exit location to player's current location
        Location currentLocation = player.getLocation();
        plugin.getDungeonConfig().setCustomExitLocation(dungeonName, currentLocation);

        player.sendMessage(ChatColor.GREEN + "Custom exit location set for dungeon '" + dungeonName + "'!");
        player.sendMessage(ChatColor.GRAY + "Location: " + currentLocation.getWorld().getName() +
            " " + currentLocation.getBlockX() + "," + currentLocation.getBlockY() + "," + currentLocation.getBlockZ());

        return true;
    }

    private boolean handleSave(CommandSender sender, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return true;
        }

        if (!player.hasPermission("apexdungeons.admin")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }

        if (args.length < 2) {
            player.sendMessage(ChatColor.YELLOW + "Usage: /dgn save <saved_name> [dungeon_name]");
            player.sendMessage(ChatColor.GRAY + "If dungeon_name is not specified, uses your current dungeon");
            return true;
        }

        String savedName = args[1];
        final String dungeonName;

        if (args.length >= 3) {
            dungeonName = args[2];
        } else {
            // Try to detect current dungeon from player's world
            String worldName = player.getWorld().getName();
            if (worldName.startsWith("dungeon_")) {
                dungeonName = worldName.substring(8); // Remove "dungeon_" prefix
            } else {
                dungeonName = null;
            }
        }

        if (dungeonName == null) {
            player.sendMessage(ChatColor.RED + "Could not determine dungeon name. Please specify: /dgn save <saved_name> <dungeon_name>");
            return true;
        }

        // Check if dungeon exists
        if (plugin.getDungeonManager().getDungeon(dungeonName) == null) {
            player.sendMessage(ChatColor.RED + "Dungeon '" + dungeonName + "' not found!");
            return true;
        }

        // Check if saved dungeon already exists
        if (plugin.getSavedDungeonManager().exists(savedName)) {
            player.sendMessage(ChatColor.RED + "Saved dungeon '" + savedName + "' already exists!");
            return true;
        }

        player.sendMessage(ChatColor.YELLOW + "Saving dungeon '" + dungeonName + "' as '" + savedName + "'...");

        // Save dungeon asynchronously to avoid blocking
        plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
            boolean success = plugin.getSavedDungeonManager().saveDungeon(dungeonName, savedName, player);

            // Send result message on main thread
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                if (success) {
                    player.sendMessage(ChatColor.GREEN + "Successfully saved dungeon as '" + savedName + "'!");
                    player.sendMessage(ChatColor.GRAY + "You can now load it from the Saved Dungeons menu or use /dgn load " + savedName + " <new_name>");
                } else {
                    player.sendMessage(ChatColor.RED + "Failed to save dungeon. Check console for details.");
                }
            });
        });

        return true;
    }

    private boolean handleLoad(CommandSender sender, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return true;
        }

        if (!player.hasPermission("apexdungeons.admin")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }

        if (args.length < 3) {
            player.sendMessage(ChatColor.YELLOW + "Usage: /dgn load <saved_name> <new_dungeon_name>");
            return true;
        }

        String savedName = args[1];
        String newDungeonName = args[2];

        // Check if saved dungeon exists
        if (!plugin.getSavedDungeonManager().exists(savedName)) {
            player.sendMessage(ChatColor.RED + "Saved dungeon '" + savedName + "' not found!");
            return true;
        }

        // Check if dungeon already exists
        if (plugin.getDungeonManager().getDungeon(newDungeonName) != null) {
            player.sendMessage(ChatColor.RED + "Dungeon '" + newDungeonName + "' already exists!");
            return true;
        }

        player.sendMessage(ChatColor.YELLOW + "Loading saved dungeon '" + savedName + "' as '" + newDungeonName + "'...");

        // Load saved dungeon
        boolean success = plugin.getSavedDungeonManager().loadSavedDungeon(savedName, newDungeonName, player);
        if (!success) {
            player.sendMessage(ChatColor.RED + "Failed to load saved dungeon. It may not exist or there was an error.");
        }

        return true;
    }

    private boolean handleParty(CommandSender sender, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return true;
        }

        if (args.length < 2) {
            // Open party GUI if player is in a party, otherwise show help
            if (plugin.getPartyManager().isInParty(player)) {
                plugin.getPartyGUI().openPartyGUI(player);
            } else {
                showPartyHelp(player);
            }
            return true;
        }

        String subCommand = args[1].toLowerCase();

        switch (subCommand) {
            case "create":
                return handlePartyCreate(player);
            case "invite":
                return handlePartyInvite(player, args);
            case "accept":
                return handlePartyAccept(player, args);
            case "decline":
                return handlePartyDecline(player, args);
            case "leave":
                return handlePartyLeave(player);
            case "kick":
                return handlePartyKick(player, args);
            case "info":
                return handlePartyInfo(player);
            case "help":
                showPartyHelp(player);
                return true;
            default:
                showPartyHelp(player);
                return true;
        }
    }

    private boolean handlePartyCreate(Player player) {
        if (plugin.getPartyManager().isInParty(player)) {
            player.sendMessage(ChatColor.RED + "You are already in a party! Use /dgn party leave to leave your current party.");
            return true;
        }

        var party = plugin.getPartyManager().createParty(player);
        if (party != null) {
            player.sendMessage(ChatColor.GREEN + "🎉 Party created! You are now the party leader.");
            player.sendMessage(ChatColor.YELLOW + "Use " + ChatColor.WHITE + "/dgn party invite <player>" +
                              ChatColor.YELLOW + " to invite players to your party.");
            player.sendMessage(ChatColor.YELLOW + "Use " + ChatColor.WHITE + "/dgn party" +
                              ChatColor.YELLOW + " to open the party management GUI.");
        } else {
            player.sendMessage(ChatColor.RED + "Failed to create party!");
        }
        return true;
    }

    private boolean handlePartyInvite(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(ChatColor.YELLOW + "Usage: /dgn party invite <player>");
            return true;
        }

        var party = plugin.getPartyManager().getPlayerParty(player);
        if (party == null) {
            player.sendMessage(ChatColor.RED + "You are not in a party! Use /dgn party create to create one.");
            return true;
        }

        if (!party.isLeader(player)) {
            player.sendMessage(ChatColor.RED + "Only the party leader can invite players!");
            return true;
        }

        String targetName = args[2];
        Player target = plugin.getServer().getPlayer(targetName);
        if (target == null) {
            player.sendMessage(ChatColor.RED + "Player '" + targetName + "' not found or not online!");
            return true;
        }

        if (target.equals(player)) {
            player.sendMessage(ChatColor.RED + "You cannot invite yourself!");
            return true;
        }

        if (plugin.getPartyManager().invitePlayer(party, player, target)) {
            player.sendMessage(ChatColor.GREEN + "Invitation sent to " + target.getName() + "!");
        } else {
            player.sendMessage(ChatColor.RED + "Failed to invite player. They may already be in a party or have a pending invite.");
        }
        return true;
    }

    private boolean handlePartyAccept(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(ChatColor.YELLOW + "Usage: /dgn party accept <leader_name>");
            return true;
        }

        String leaderName = args[2];
        if (plugin.getPartyManager().acceptInvite(player, leaderName)) {
            player.sendMessage(ChatColor.GREEN + "🎉 You joined " + leaderName + "'s party!");
        } else {
            player.sendMessage(ChatColor.RED + "No pending invitation from " + leaderName + " or failed to join party!");
        }
        return true;
    }

    private boolean handlePartyDecline(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(ChatColor.YELLOW + "Usage: /dgn party decline <leader_name>");
            return true;
        }

        String leaderName = args[2];
        if (plugin.getPartyManager().declineInvite(player, leaderName)) {
            player.sendMessage(ChatColor.YELLOW + "You declined the party invitation from " + leaderName + ".");
        } else {
            player.sendMessage(ChatColor.RED + "No pending invitation from " + leaderName + "!");
        }
        return true;
    }

    private boolean handlePartyLeave(Player player) {
        if (plugin.getPartyManager().leaveParty(player)) {
            // Success message is handled in PartyManager
        } else {
            player.sendMessage(ChatColor.RED + "You are not in a party!");
        }
        return true;
    }

    private boolean handlePartyKick(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(ChatColor.YELLOW + "Usage: /dgn party kick <player>");
            return true;
        }

        String targetName = args[2];
        Player target = plugin.getServer().getPlayer(targetName);
        if (target == null) {
            player.sendMessage(ChatColor.RED + "Player '" + targetName + "' not found or not online!");
            return true;
        }

        if (plugin.getPartyManager().kickPlayer(player, target)) {
            // Success message is handled in PartyManager
        } else {
            player.sendMessage(ChatColor.RED + "Failed to kick player. You may not be the party leader or the player is not in your party.");
        }
        return true;
    }

    private boolean handlePartyInfo(Player player) {
        var party = plugin.getPartyManager().getPlayerParty(player);
        if (party == null) {
            player.sendMessage(ChatColor.RED + "You are not in a party!");
            return true;
        }

        player.sendMessage(ChatColor.GOLD + "=== Party Information ===");
        player.sendMessage(ChatColor.YELLOW + "Leader: " + ChatColor.WHITE + party.getLeader().getName());
        player.sendMessage(ChatColor.YELLOW + "Size: " + ChatColor.WHITE + party.getSize() + "/" + party.getMaxSize());
        player.sendMessage(ChatColor.YELLOW + "Members:");

        for (Player member : party.getMembers()) {
            String status = member.isOnline() ? ChatColor.GREEN + "Online" : ChatColor.RED + "Offline";
            String role = party.isLeader(member) ? ChatColor.GOLD + " (Leader)" : "";
            player.sendMessage(ChatColor.AQUA + "  • " + ChatColor.WHITE + member.getName() + role + " - " + status);
        }

        return true;
    }

    private void showPartyHelp(Player player) {
        player.sendMessage(ChatColor.GOLD + "=== Party System Help ===");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "Party Commands:");
        player.sendMessage(ChatColor.YELLOW + "/dgn party create" + ChatColor.WHITE + " - Create a new party");
        player.sendMessage(ChatColor.YELLOW + "/dgn party invite <player>" + ChatColor.WHITE + " - Invite a player to your party");
        player.sendMessage(ChatColor.YELLOW + "/dgn party accept <leader>" + ChatColor.WHITE + " - Accept a party invitation");
        player.sendMessage(ChatColor.YELLOW + "/dgn party decline <leader>" + ChatColor.WHITE + " - Decline a party invitation");
        player.sendMessage(ChatColor.YELLOW + "/dgn party leave" + ChatColor.WHITE + " - Leave your current party");
        player.sendMessage(ChatColor.YELLOW + "/dgn party kick <player>" + ChatColor.WHITE + " - Kick a player from your party (leader only)");
        player.sendMessage(ChatColor.YELLOW + "/dgn party info" + ChatColor.WHITE + " - Show party information");
        player.sendMessage(ChatColor.YELLOW + "/dgn party" + ChatColor.WHITE + " - Open party management GUI");
        player.sendMessage("");
        player.sendMessage(ChatColor.AQUA + "💡 Tips:");
        player.sendMessage(ChatColor.GRAY + "• Maximum 4 players per party");
        player.sendMessage(ChatColor.GRAY + "• Use the GUI for easy party management");
        player.sendMessage(ChatColor.GRAY + "• Parties work with dungeon instancing");
    }

    private void showMainGuide(Player player) {
        player.sendMessage(ChatColor.GOLD + "=== Soaps Dungeons Complete Guide - Made by Vexy ===");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🏰 Welcome to Soaps Dungeons!");
        player.sendMessage(ChatColor.GRAY + "This plugin provides advanced dungeon building and management tools.");
        player.sendMessage("");
        player.sendMessage(ChatColor.YELLOW + "📚 Available Help Topics:");
        player.sendMessage(ChatColor.AQUA + "/dgn help rooms" + ChatColor.WHITE + " - Room design and types");
        player.sendMessage(ChatColor.AQUA + "/dgn help connections" + ChatColor.WHITE + " - Connecting rooms");
        player.sendMessage(ChatColor.AQUA + "/dgn help blocks" + ChatColor.WHITE + " - Start and end blocks");
        player.sendMessage(ChatColor.AQUA + "/dgn help schematics" + ChatColor.WHITE + " - Using schematics");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🎯 Quick Start:");
        player.sendMessage(ChatColor.WHITE + "1. Use /dgn tools to get building tools");
        player.sendMessage(ChatColor.WHITE + "2. Create your dungeon with /dgn create <name>");
        player.sendMessage(ChatColor.WHITE + "3. Build rooms and connect them");
        player.sendMessage(ChatColor.WHITE + "4. Place start and end blocks");
        player.sendMessage(ChatColor.WHITE + "5. Test with /dgn start <name>");
    }

    private void showRoomGuide(Player player) {
        player.sendMessage(ChatColor.GOLD + "=== Room Design Guide ===");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🏗️ Room Types:");
        player.sendMessage(ChatColor.AQUA + "• Entrance Room" + ChatColor.WHITE + " - Contains start block");
        player.sendMessage(ChatColor.AQUA + "• Challenge Room" + ChatColor.WHITE + " - Puzzles, mobs, obstacles");
        player.sendMessage(ChatColor.AQUA + "• Boss Room" + ChatColor.WHITE + " - Final challenge with end block");
        player.sendMessage(ChatColor.AQUA + "• Treasure Room" + ChatColor.WHITE + " - Rewards and loot");
        player.sendMessage("");
        player.sendMessage(ChatColor.YELLOW + "💡 Design Tips:");
        player.sendMessage(ChatColor.WHITE + "• Leave 3-block high spaces for doorways");
        player.sendMessage(ChatColor.WHITE + "• Use varied room sizes for interest");
        player.sendMessage(ChatColor.WHITE + "• Consider lighting and atmosphere");
        player.sendMessage(ChatColor.WHITE + "• Plan your layout before building");
    }

    private void showConnectionGuide(Player player) {
        player.sendMessage(ChatColor.GOLD + "=== Room Connection Guide ===");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🔗 Connection Tool Usage:");
        player.sendMessage(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Get the Room Connector from /dgn tools");
        player.sendMessage(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Left-click on first room wall");
        player.sendMessage(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Right-click on second room wall");
        player.sendMessage(ChatColor.AQUA + "4. " + ChatColor.WHITE + "Type connection type in chat (1, 2, or 3)");
        player.sendMessage("");
        player.sendMessage(ChatColor.YELLOW + "🚪 Connection Types:");
        player.sendMessage(ChatColor.AQUA + "Type 1:" + ChatColor.WHITE + " Simple doorway (3x3 opening)");
        player.sendMessage(ChatColor.AQUA + "Type 2:" + ChatColor.WHITE + " Decorated doorway with frame");
        player.sendMessage(ChatColor.AQUA + "Type 3:" + ChatColor.WHITE + " Full corridor passage");
    }

    private void showBlocksGuide(Player player) {
        player.sendMessage(ChatColor.GOLD + "=== Start & End Blocks Guide ===");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🎯 Start Block (Emerald Block):");
        player.sendMessage(ChatColor.WHITE + "• Place in your entrance room");
        player.sendMessage(ChatColor.WHITE + "• Players right-click to begin challenge");
        player.sendMessage(ChatColor.WHITE + "• Only one per dungeon");
        player.sendMessage(ChatColor.WHITE + "• Starts timer and teleports player");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🏆 End Block (Diamond Block):");
        player.sendMessage(ChatColor.WHITE + "• Place in your final/boss room");
        player.sendMessage(ChatColor.WHITE + "• Players right-click to complete challenge");
        player.sendMessage(ChatColor.WHITE + "• Gives rewards and teleports back");
        player.sendMessage(ChatColor.WHITE + "• Must activate start block first");
        player.sendMessage("");
        player.sendMessage(ChatColor.YELLOW + "⚙️ Custom Locations:");
        player.sendMessage(ChatColor.AQUA + "/dgn setspawn <dungeon>" + ChatColor.WHITE + " - Set custom spawn");
        player.sendMessage(ChatColor.AQUA + "/dgn setexit <dungeon>" + ChatColor.WHITE + " - Set custom exit");
    }

    private void showSchematicsGuide(Player player) {
        player.sendMessage(ChatColor.GOLD + "=== Schematics Guide ===");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "📐 Schematic Tools:");
        player.sendMessage(ChatColor.WHITE + "• Get schematic tools from /dgn tools");
        player.sendMessage(ChatColor.WHITE + "• Right-click for 3D preview mode");
        player.sendMessage(ChatColor.WHITE + "• Left-click for immediate placement");
        player.sendMessage("");
        player.sendMessage(ChatColor.YELLOW + "🎮 Preview Controls:");
        player.sendMessage(ChatColor.AQUA + "W/A/S/D:" + ChatColor.WHITE + " Move preview position");
        player.sendMessage(ChatColor.AQUA + "R:" + ChatColor.WHITE + " Rotate 90 degrees");
        player.sendMessage(ChatColor.AQUA + "Enter:" + ChatColor.WHITE + " Confirm placement");
        player.sendMessage(ChatColor.AQUA + "Escape:" + ChatColor.WHITE + " Cancel preview");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "💡 Tips:");
        player.sendMessage(ChatColor.WHITE + "• Use preview to avoid placement mistakes");
        player.sendMessage(ChatColor.WHITE + "• Schematics include pre-built rooms");
        player.sendMessage(ChatColor.WHITE + "• Perfect for consistent room designs");
    }

    @Override
    public @Nullable List<String> onTabComplete(@NotNull CommandSender sender, @NotNull Command command, @NotNull String alias, @NotNull String[] args) {
        List<String> completions = new ArrayList<>();
        if (args.length == 1) {
            completions.add("admin");
            completions.add("givewand");
            completions.add("create");
            completions.add("remove");
            completions.add("tp");
            completions.add("start");
            completions.add("leave");
            completions.add("tools");
            completions.add("list");
            completions.add("info");
            completions.add("stats");
            completions.add("version");
            completions.add("help");
            if (sender.hasPermission("apexdungeons.admin")) {
                completions.add("reloadschematics");
                completions.add("testworld");
                completions.add("setspawn");
                completions.add("setexit");
                completions.add("save");
                completions.add("load");
                completions.add("party");
            }
            return completions;
        }
        if (args.length == 2) {
            String sub = args[0].toLowerCase();
            switch (sub) {
                case "givewand":
                    for (Player p : Bukkit.getOnlinePlayers()) {
                        completions.add(p.getName());
                    }
                    break;
                case "remove":
                case "tp":
                case "start":
                case "info":
                case "setspawn":
                case "setexit":
                case "save": // First argument is dungeon name
                    completions.addAll(plugin.getDungeonManager().listDungeonNames());
                    break;
                case "load": // First argument is template name
                    completions.addAll(plugin.getTemplateManager().getTemplateNames());
                    break;
                case "party": // Party subcommands
                    completions.add("create");
                    completions.add("invite");
                    completions.add("accept");
                    completions.add("decline");
                    completions.add("leave");
                    completions.add("kick");
                    completions.add("info");
                    completions.add("help");
                    break;
                case "help":
                    completions.add("guide");
                    completions.add("rooms");
                    completions.add("connections");
                    completions.add("blocks");
                    completions.add("schematics");
                    break;
                default:
                    break;
            }
        }
        return completions;
    }

    /**
     * Handle mobspawn command.
     */
    private boolean handleMobSpawn(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return true;
        }

        Player player = (Player) sender;

        if (!player.hasPermission("apexdungeons.admin") && !player.hasPermission("apexdungeons.mobspawn")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }

        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "Usage: /dgn mobspawn <set|clear|list|radius> [mob_name|radius]");
            return true;
        }

        String subCommand = args[1].toLowerCase();
        switch (subCommand) {
            case "set":
                return handleMobSpawnSet(player, args);
            case "clear":
                return handleMobSpawnClear(player);
            case "list":
                return handleMobSpawnList(player);
            case "radius":
                return handleMobSpawnRadius(player, args);
            default:
                player.sendMessage(ChatColor.RED + "Unknown subcommand. Use: set, clear, list, or radius");
                return true;
        }
    }

    /**
     * Handle chest spawn commands.
     */
    private boolean handleChestSpawn(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return true;
        }

        Player player = (Player) sender;

        if (!player.hasPermission("apexdungeons.admin") && !player.hasPermission("apexdungeons.chestspawn")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to use chest spawn commands.");
            return true;
        }

        if (args.length < 2) {
            player.sendMessage(ChatColor.YELLOW + "Chest Spawn Commands:");
            player.sendMessage(ChatColor.AQUA + "/dgn chestspawn set <loot_table> [radius] " + ChatColor.GRAY + "- Set loot table for chest spawning");
            player.sendMessage(ChatColor.AQUA + "/dgn chestspawn list " + ChatColor.GRAY + "- List available loot tables");
            player.sendMessage(ChatColor.AQUA + "/dgn chestspawn clear " + ChatColor.GRAY + "- Clear your loot table selection");
            player.sendMessage(ChatColor.AQUA + "/dgn chestspawn info " + ChatColor.GRAY + "- Show your current settings");
            player.sendMessage(ChatColor.GRAY + "Available loot tables: " +
                String.join(", ", plugin.getChestLootManager().getLootTableNames()));
            return true;
        }

        String subCommand = args[1].toLowerCase();

        switch (subCommand) {
            case "set":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "Usage: /dgn chestspawn set <loot_table> [radius]");
                    player.sendMessage(ChatColor.GRAY + "Available loot tables: " +
                        String.join(", ", plugin.getChestLootManager().getLootTableNames()));
                    return true;
                }

                String lootTable = args[2];
                if (!plugin.getChestLootManager().getLootTableNames().contains(lootTable)) {
                    player.sendMessage(ChatColor.RED + "Unknown loot table: " + lootTable);
                    player.sendMessage(ChatColor.GRAY + "Available loot tables: " +
                        String.join(", ", plugin.getChestLootManager().getLootTableNames()));
                    return true;
                }

                double radius = 3.0; // Default radius
                if (args.length >= 4) {
                    try {
                        radius = Double.parseDouble(args[3]);
                        if (radius <= 0 || radius > 10) {
                            player.sendMessage(ChatColor.RED + "Radius must be between 0.1 and 10.0");
                            return true;
                        }
                    } catch (NumberFormatException e) {
                        player.sendMessage(ChatColor.RED + "Invalid radius. Must be a number between 0.1 and 10.0");
                        return true;
                    }
                }

                plugin.getChestSpawnData().setPlayerLootTableSelection(player.getUniqueId(), lootTable);
                plugin.getChestSpawnData().setPlayerRadius(player.getUniqueId(), radius);

                player.sendMessage(ChatColor.GREEN + "✓ Chest spawn settings updated!");
                player.sendMessage(ChatColor.GRAY + "Loot Table: " + ChatColor.WHITE + lootTable);
                player.sendMessage(ChatColor.GRAY + "Radius: " + ChatColor.WHITE + radius + " blocks");
                player.sendMessage(ChatColor.YELLOW + "Now use the Chest Spawn Tool to place spawn points!");
                break;

            case "list":
                player.sendMessage(ChatColor.GREEN + "Available Loot Tables:");
                for (String tableName : plugin.getChestLootManager().getLootTableNames()) {
                    player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + tableName);
                }
                break;

            case "clear":
                plugin.getChestSpawnData().clearPlayerLootTableSelection(player.getUniqueId());
                player.sendMessage(ChatColor.GREEN + "✓ Chest spawn loot table selection cleared.");
                break;

            case "info":
                String currentTable = plugin.getChestSpawnData().getPlayerLootTableSelection(player.getUniqueId());
                double currentRadius = plugin.getChestSpawnData().getPlayerRadius(player.getUniqueId(), 3.0);

                player.sendMessage(ChatColor.GREEN + "Your Chest Spawn Settings:");
                if (currentTable != null) {
                    player.sendMessage(ChatColor.GRAY + "Loot Table: " + ChatColor.WHITE + currentTable);
                    player.sendMessage(ChatColor.GRAY + "Radius: " + ChatColor.WHITE + currentRadius + " blocks");
                } else {
                    player.sendMessage(ChatColor.YELLOW + "No loot table configured. Use /dgn chestspawn set <loot_table>");
                }
                break;

            default:
                player.sendMessage(ChatColor.RED + "Unknown chest spawn command: " + subCommand);
                player.sendMessage(ChatColor.YELLOW + "Use /dgn chestspawn for help");
                break;
        }

        return true;
    }

    /**
     * Handle bossspawn command.
     */
    private boolean handleBossSpawn(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return true;
        }

        Player player = (Player) sender;

        if (!player.hasPermission("apexdungeons.admin") && !player.hasPermission("apexdungeons.bossspawn")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }

        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "Usage: /dgn bossspawn <set|clear|list|radius> [boss_name|radius]");
            return true;
        }

        String subCommand = args[1].toLowerCase();
        switch (subCommand) {
            case "set":
                return handleBossSpawnSet(player, args);
            case "clear":
                return handleBossSpawnClear(player);
            case "list":
                return handleBossSpawnList(player);
            case "radius":
                return handleBossSpawnRadius(player, args);
            default:
                player.sendMessage(ChatColor.RED + "Unknown subcommand. Use: set, clear, list, or radius");
                return true;
        }
    }

    private boolean handleMobSpawnSet(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(ChatColor.RED + "Usage: /dgn mobspawn set <mob_name>");
            player.sendMessage(ChatColor.YELLOW + "Available mobs:");

            String[] availableMobs = plugin.getMobAdapter().getAvailableMobs();
            if (availableMobs.length > 0) {
                for (String mob : availableMobs) {
                    player.sendMessage(ChatColor.GRAY + "• " + ChatColor.WHITE + mob);
                }
            } else {
                player.sendMessage(ChatColor.GRAY + "No mobs available");
            }
            return true;
        }

        String mobName = args[2];
        plugin.getMobSpawnData().setPlayerMobSelection(player.getUniqueId(), mobName);

        player.sendMessage(ChatColor.GREEN + "✓ Mob spawn configured!");
        player.sendMessage(ChatColor.GRAY + "Mob: " + ChatColor.WHITE + mobName);
        player.sendMessage(ChatColor.GRAY + "Now use the Mob Spawn Tool to place spawn points");
        return true;
    }

    private boolean handleMobSpawnClear(Player player) {
        plugin.getMobSpawnData().clearPlayerMobSelection(player.getUniqueId());
        player.sendMessage(ChatColor.YELLOW + "Mob spawn selection cleared.");
        return true;
    }

    private boolean handleMobSpawnList(Player player) {
        String[] availableMobs = plugin.getMobAdapter().getAvailableMobs();
        player.sendMessage(ChatColor.GREEN + "=== Available Mobs ===");
        player.sendMessage(ChatColor.GRAY + "Adapter: " + ChatColor.WHITE + plugin.getMobAdapter().getAdapterName());
        player.sendMessage("");

        if (availableMobs.length > 0) {
            for (String mob : availableMobs) {
                player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + mob);
            }
        } else {
            player.sendMessage(ChatColor.GRAY + "No mobs available");
        }

        String currentSelection = plugin.getMobSpawnData().getPlayerMobSelection(player.getUniqueId());
        if (currentSelection != null) {
            player.sendMessage("");
            player.sendMessage(ChatColor.YELLOW + "Current selection: " + ChatColor.WHITE + currentSelection);
        }
        return true;
    }

    private boolean handleMobSpawnRadius(Player player, String[] args) {
        if (args.length < 3) {
            double currentRadius = plugin.getMobSpawnData().getPlayerRadius(player.getUniqueId(), 5.0);
            player.sendMessage(ChatColor.YELLOW + "Current spawn radius: " + ChatColor.WHITE + currentRadius + " blocks");
            player.sendMessage(ChatColor.GRAY + "Usage: /dgn mobspawn radius <number>");
            return true;
        }

        try {
            double radius = Double.parseDouble(args[2]);
            if (radius < 1.0 || radius > 20.0) {
                player.sendMessage(ChatColor.RED + "Radius must be between 1.0 and 20.0 blocks");
                return true;
            }

            plugin.getMobSpawnData().setPlayerRadius(player.getUniqueId(), radius);
            player.sendMessage(ChatColor.GREEN + "✓ Spawn radius set to " + radius + " blocks");
        } catch (NumberFormatException e) {
            player.sendMessage(ChatColor.RED + "Invalid number: " + args[2]);
        }
        return true;
    }

    private boolean handleBossSpawnSet(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(ChatColor.RED + "Usage: /dgn bossspawn set <boss_name>");
            player.sendMessage(ChatColor.YELLOW + "Available bosses:");

            String[] availableBosses = plugin.getMobAdapter().getAvailableBosses();
            if (availableBosses.length > 0) {
                for (String boss : availableBosses) {
                    player.sendMessage(ChatColor.GRAY + "• " + ChatColor.WHITE + boss);
                }
            } else {
                player.sendMessage(ChatColor.GRAY + "No bosses available");
            }
            return true;
        }

        String bossName = args[2];
        plugin.getMobSpawnData().setPlayerBossSelection(player.getUniqueId(), bossName);

        player.sendMessage(ChatColor.GREEN + "✓ Boss spawn configured!");
        player.sendMessage(ChatColor.GRAY + "Boss: " + ChatColor.WHITE + bossName);
        player.sendMessage(ChatColor.GRAY + "Now use the Boss Spawn Tool to place spawn points");
        return true;
    }

    private boolean handleBossSpawnClear(Player player) {
        plugin.getMobSpawnData().clearPlayerBossSelection(player.getUniqueId());
        player.sendMessage(ChatColor.YELLOW + "Boss spawn selection cleared.");
        return true;
    }

    private boolean handleBossSpawnList(Player player) {
        String[] availableBosses = plugin.getMobAdapter().getAvailableBosses();
        player.sendMessage(ChatColor.GREEN + "=== Available Bosses ===");
        player.sendMessage(ChatColor.GRAY + "Adapter: " + ChatColor.WHITE + plugin.getMobAdapter().getAdapterName());
        player.sendMessage("");

        if (availableBosses.length > 0) {
            for (String boss : availableBosses) {
                player.sendMessage(ChatColor.DARK_RED + "• " + ChatColor.WHITE + boss);
            }
        } else {
            player.sendMessage(ChatColor.GRAY + "No bosses available");
        }

        String currentSelection = plugin.getMobSpawnData().getPlayerBossSelection(player.getUniqueId());
        if (currentSelection != null) {
            player.sendMessage("");
            player.sendMessage(ChatColor.YELLOW + "Current selection: " + ChatColor.WHITE + currentSelection);
        }
        return true;
    }

    private boolean handleBossSpawnRadius(Player player, String[] args) {
        if (args.length < 3) {
            double currentRadius = plugin.getMobSpawnData().getPlayerRadius(player.getUniqueId(), 7.0);
            player.sendMessage(ChatColor.YELLOW + "Current boss spawn radius: " + ChatColor.WHITE + currentRadius + " blocks");
            player.sendMessage(ChatColor.GRAY + "Usage: /dgn bossspawn radius <number>");
            return true;
        }

        try {
            double radius = Double.parseDouble(args[2]);
            if (radius < 1.0 || radius > 20.0) {
                player.sendMessage(ChatColor.RED + "Radius must be between 1.0 and 20.0 blocks");
                return true;
            }

            plugin.getMobSpawnData().setPlayerRadius(player.getUniqueId(), radius);
            player.sendMessage(ChatColor.GREEN + "✓ Boss spawn radius set to " + radius + " blocks");
        } catch (NumberFormatException e) {
            player.sendMessage(ChatColor.RED + "Invalid number: " + args[2]);
        }
        return true;
    }
}