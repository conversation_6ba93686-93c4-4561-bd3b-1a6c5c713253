package com.apexdungeons.schematics;

import com.apexdungeons.ApexDungeons;
import org.bukkit.*;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;

/**
 * Advanced 3D preview system for schematics with rotation and positioning controls.
 * Provides full block outline preview, interactive rotation, and positioning.
 */
public class SchematicPreview {
    private final ApexDungeons plugin;
    private final Player player;
    private final SchematicData schematic;
    private Location baseLocation;
    private int rotation = 0; // 0, 90, 180, 270 degrees
    private BukkitTask previewTask;
    private boolean isActive = false;
    
    // Preview settings
    private static final Particle OUTLINE_PARTICLE = Particle.END_ROD;
    private static final Particle CORNER_PARTICLE = Particle.FLAME;
    private static final Particle CENTER_PARTICLE = Particle.ENCHANT;
    private static final Particle VALID_PARTICLE = Particle.HAPPY_VILLAGER;
    private static final Particle INVALID_PARTICLE = Particle.SMOKE;
    private static final int PREVIEW_DURATION = 300; // 15 seconds at 20 ticks per second
    
    // Movement controls
    private final Map<String, Location> movementOffsets = new HashMap<>();
    
    public SchematicPreview(ApexDungeons plugin, Player player, SchematicData schematic, Location location) {
        this.plugin = plugin;
        this.player = player;
        this.schematic = schematic;
        this.baseLocation = location.clone();
        
        initializeMovementOffsets();
    }
    
    /**
     * Initialize movement offset directions.
     */
    private void initializeMovementOffsets() {
        movementOffsets.put("north", new Location(null, 0, 0, -1));
        movementOffsets.put("south", new Location(null, 0, 0, 1));
        movementOffsets.put("east", new Location(null, 1, 0, 0));
        movementOffsets.put("west", new Location(null, -1, 0, 0));
        movementOffsets.put("up", new Location(null, 0, 1, 0));
        movementOffsets.put("down", new Location(null, 0, -1, 0));
    }
    
    /**
     * Start the 3D preview display.
     */
    public void startPreview() {
        if (isActive) {
            stopPreview();
        }
        
        isActive = true;
        showPreviewInstructions();
        
        previewTask = new BukkitRunnable() {
            private int ticks = 0;
            
            @Override
            public void run() {
                if (!isActive || !player.isOnline()) {
                    cancel();
                    return;
                }
                
                // Show preview every 4 ticks (5 times per second)
                if (ticks % 4 == 0) {
                    displayPreview();
                }
                
                ticks++;
                
                // Auto-cancel after duration
                if (ticks >= PREVIEW_DURATION) {
                    stopPreview();
                    player.sendMessage(ChatColor.YELLOW + "Preview timed out. Use the tool again to restart.");
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }
    
    /**
     * Stop the preview display.
     */
    public void stopPreview() {
        isActive = false;
        if (previewTask != null) {
            previewTask.cancel();
            previewTask = null;
        }
    }
    
    /**
     * Display the 3D preview with particles.
     */
    private void displayPreview() {
        World world = baseLocation.getWorld();
        if (world == null) return;
        
        // Get rotated dimensions
        int[] dimensions = getRotatedDimensions();
        int width = dimensions[0];
        int height = dimensions[1];
        int depth = dimensions[2];
        
        // Show corner markers
        showCornerMarkers(world, width, height, depth);
        
        // Show outline edges
        showOutlineEdges(world, width, height, depth);
        
        // Show center point
        showCenterPoint(world);
        
        // Show key blocks (non-air blocks from the schematic)
        showKeyBlocks(world);
        
        // Show info display
        showInfoDisplay();
    }
    
    /**
     * Show corner markers of the preview area.
     */
    private void showCornerMarkers(World world, int width, int height, int depth) {
        Location[] corners = {
            baseLocation.clone(),
            baseLocation.clone().add(width - 1, 0, 0),
            baseLocation.clone().add(0, 0, depth - 1),
            baseLocation.clone().add(width - 1, 0, depth - 1),
            baseLocation.clone().add(0, height - 1, 0),
            baseLocation.clone().add(width - 1, height - 1, 0),
            baseLocation.clone().add(0, height - 1, depth - 1),
            baseLocation.clone().add(width - 1, height - 1, depth - 1)
        };
        
        for (Location corner : corners) {
            world.spawnParticle(CORNER_PARTICLE, corner.add(0.5, 0.5, 0.5), 3, 0.1, 0.1, 0.1, 0);
        }
    }
    
    /**
     * Show outline edges of the preview area.
     */
    private void showOutlineEdges(World world, int width, int height, int depth) {
        // Bottom edges
        showEdgeLine(world, baseLocation.clone(), baseLocation.clone().add(width - 1, 0, 0));
        showEdgeLine(world, baseLocation.clone(), baseLocation.clone().add(0, 0, depth - 1));
        showEdgeLine(world, baseLocation.clone().add(width - 1, 0, 0), baseLocation.clone().add(width - 1, 0, depth - 1));
        showEdgeLine(world, baseLocation.clone().add(0, 0, depth - 1), baseLocation.clone().add(width - 1, 0, depth - 1));
        
        // Top edges
        showEdgeLine(world, baseLocation.clone().add(0, height - 1, 0), baseLocation.clone().add(width - 1, height - 1, 0));
        showEdgeLine(world, baseLocation.clone().add(0, height - 1, 0), baseLocation.clone().add(0, height - 1, depth - 1));
        showEdgeLine(world, baseLocation.clone().add(width - 1, height - 1, 0), baseLocation.clone().add(width - 1, height - 1, depth - 1));
        showEdgeLine(world, baseLocation.clone().add(0, height - 1, depth - 1), baseLocation.clone().add(width - 1, height - 1, depth - 1));
        
        // Vertical edges
        showEdgeLine(world, baseLocation.clone(), baseLocation.clone().add(0, height - 1, 0));
        showEdgeLine(world, baseLocation.clone().add(width - 1, 0, 0), baseLocation.clone().add(width - 1, height - 1, 0));
        showEdgeLine(world, baseLocation.clone().add(0, 0, depth - 1), baseLocation.clone().add(0, height - 1, depth - 1));
        showEdgeLine(world, baseLocation.clone().add(width - 1, 0, depth - 1), baseLocation.clone().add(width - 1, height - 1, depth - 1));
    }
    
    /**
     * Show a line of particles between two points.
     */
    private void showEdgeLine(World world, Location start, Location end) {
        double distance = start.distance(end);
        int particles = Math.max(2, (int) (distance * 2));
        
        for (int i = 0; i <= particles; i++) {
            double ratio = (double) i / particles;
            Location point = start.clone().add(
                (end.getX() - start.getX()) * ratio,
                (end.getY() - start.getY()) * ratio,
                (end.getZ() - start.getZ()) * ratio
            );
            world.spawnParticle(OUTLINE_PARTICLE, point.add(0.5, 0.5, 0.5), 1, 0, 0, 0, 0);
        }
    }
    
    /**
     * Show center point of the preview.
     */
    private void showCenterPoint(World world) {
        int[] dimensions = getRotatedDimensions();
        Location center = baseLocation.clone().add(
            dimensions[0] / 2.0,
            dimensions[1] / 2.0,
            dimensions[2] / 2.0
        );
        world.spawnParticle(CENTER_PARTICLE, center, 5, 0.2, 0.2, 0.2, 0);
    }
    
    /**
     * Show key blocks from the schematic (sample of non-air blocks).
     */
    private void showKeyBlocks(World world) {
        Material[][][] blocks = getRotatedBlocks();
        int sampleRate = Math.max(1, Math.max(blocks.length, Math.max(blocks[0].length, blocks[0][0].length)) / 20);
        
        for (int y = 0; y < blocks.length; y += sampleRate) {
            for (int z = 0; z < blocks[y].length; z += sampleRate) {
                for (int x = 0; x < blocks[y][z].length; x += sampleRate) {
                    if (blocks[y][z][x] != Material.AIR) {
                        Location blockLoc = baseLocation.clone().add(x + 0.5, y + 0.5, z + 0.5);
                        world.spawnParticle(Particle.HAPPY_VILLAGER, blockLoc, 1, 0, 0, 0, 0);
                    }
                }
            }
        }
    }
    
    /**
     * Show information display to the player.
     */
    private void showInfoDisplay() {
        // For compatibility, we'll skip the action bar and just show periodic messages
        // Action bar support varies by Bukkit version
    }
    
    /**
     * Show preview control instructions.
     */
    private void showPreviewInstructions() {
        player.sendMessage(ChatColor.GOLD + "=== Schematic Preview Controls ===");
        player.sendMessage(ChatColor.GREEN + "R" + ChatColor.WHITE + " - Rotate 90° clockwise");
        player.sendMessage(ChatColor.GREEN + "WASD" + ChatColor.WHITE + " - Move preview horizontally");
        player.sendMessage(ChatColor.GREEN + "Space/Shift" + ChatColor.WHITE + " - Move preview up/down");
        player.sendMessage(ChatColor.GREEN + "Left-click" + ChatColor.WHITE + " - Confirm placement");
        player.sendMessage(ChatColor.GREEN + "Right-click" + ChatColor.WHITE + " - Cancel preview");
        player.sendMessage(ChatColor.GRAY + "Preview will auto-cancel in 15 seconds");
    }
    
    /**
     * Rotate the preview 90 degrees clockwise.
     */
    public void rotate() {
        rotation = (rotation + 90) % 360;
        player.sendMessage(ChatColor.GREEN + "Rotated to " + rotation + "°");
        player.playSound(baseLocation, Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.2f);
    }
    
    /**
     * Move the preview in a direction.
     */
    public void move(String direction) {
        Location offset = movementOffsets.get(direction.toLowerCase());
        if (offset != null) {
            baseLocation.add(offset);
            player.sendMessage(ChatColor.GREEN + "Moved " + direction);
            player.playSound(baseLocation, Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.0f);
        }
    }
    
    /**
     * Get rotated dimensions based on current rotation.
     */
    private int[] getRotatedDimensions() {
        int width = schematic.getWidth();
        int height = schematic.getHeight();
        int depth = schematic.getDepth();
        
        if (rotation == 90 || rotation == 270) {
            return new int[]{depth, height, width};
        } else {
            return new int[]{width, height, depth};
        }
    }
    
    /**
     * Get rotated block array based on current rotation.
     */
    private Material[][][] getRotatedBlocks() {
        Material[][][] original = schematic.getBlocks();
        
        switch (rotation) {
            case 90:
                return rotateBlocks90(original);
            case 180:
                return rotateBlocks180(original);
            case 270:
                return rotateBlocks270(original);
            default:
                return original;
        }
    }
    
    /**
     * Rotate blocks 90 degrees clockwise.
     */
    private Material[][][] rotateBlocks90(Material[][][] blocks) {
        int height = blocks.length;
        int depth = blocks[0].length;
        int width = blocks[0][0].length;
        
        Material[][][] rotated = new Material[height][width][depth];
        
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    rotated[y][x][depth - 1 - z] = blocks[y][z][x];
                }
            }
        }
        
        return rotated;
    }
    
    /**
     * Rotate blocks 180 degrees.
     */
    private Material[][][] rotateBlocks180(Material[][][] blocks) {
        int height = blocks.length;
        int depth = blocks[0].length;
        int width = blocks[0][0].length;
        
        Material[][][] rotated = new Material[height][depth][width];
        
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    rotated[y][depth - 1 - z][width - 1 - x] = blocks[y][z][x];
                }
            }
        }
        
        return rotated;
    }
    
    /**
     * Rotate blocks 270 degrees clockwise.
     */
    private Material[][][] rotateBlocks270(Material[][][] blocks) {
        int height = blocks.length;
        int depth = blocks[0].length;
        int width = blocks[0][0].length;
        
        Material[][][] rotated = new Material[height][width][depth];
        
        for (int y = 0; y < height; y++) {
            for (int z = 0; z < depth; z++) {
                for (int x = 0; x < width; x++) {
                    rotated[y][width - 1 - x][z] = blocks[y][z][x];
                }
            }
        }
        
        return rotated;
    }
    
    /**
     * Count non-air blocks in the schematic.
     */
    private int countNonAirBlocks() {
        int count = 0;
        Material[][][] blocks = schematic.getBlocks();
        
        for (Material[][] layer : blocks) {
            for (Material[] row : layer) {
                for (Material block : row) {
                    if (block != Material.AIR) {
                        count++;
                    }
                }
            }
        }
        
        return count;
    }
    
    // Getters
    public boolean isActive() { return isActive; }
    public Location getBaseLocation() { return baseLocation.clone(); }
    public int getRotation() { return rotation; }
    public SchematicData getSchematic() { return schematic; }
}
