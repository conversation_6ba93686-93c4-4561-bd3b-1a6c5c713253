package com.apexdungeons.mobs;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Material;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Manages loading and organizing dungeon mob configurations from the dungeon_mobs folder.
 */
public class DungeonMobManager {
    private final ApexDungeons plugin;
    private final File dungeonMobsFolder;
    private final Map<String, DungeonMobConfig> loadedMobs = new HashMap<>();
    private final Map<String, List<DungeonMobConfig>> mobsByCategory = new HashMap<>();
    
    public DungeonMobManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.dungeonMobsFolder = new File(plugin.getDataFolder(), "dungeon_mobs");
        
        // Create dungeon_mobs folder if it doesn't exist
        if (!dungeonMobsFolder.exists()) {
            dungeonMobsFolder.mkdirs();
            plugin.getLogger().info("Created dungeon_mobs folder: " + dungeonMobsFolder.getPath());
            createExampleMobConfigs();
        }
        
        // Load all mob configurations
        loadAllMobConfigs();
    }
    
    /**
     * Load all mob configuration files from the dungeon_mobs folder.
     */
    public void loadAllMobConfigs() {
        loadedMobs.clear();
        mobsByCategory.clear();
        
        plugin.getLogger().info("Loading mob configurations from: " + dungeonMobsFolder.getAbsolutePath());
        
        File[] files = dungeonMobsFolder.listFiles((dir, name) -> 
            name.toLowerCase().endsWith(".yml") || name.toLowerCase().endsWith(".yaml"));
        
        if (files == null || files.length == 0) {
            plugin.getLogger().info("No mob configuration files found in " + dungeonMobsFolder.getPath());
            plugin.getLogger().info("Creating example mob configurations...");
            createExampleMobConfigs();
            return;
        }
        
        plugin.getLogger().info("Found " + files.length + " mob configuration files, loading...");
        int loaded = 0;
        int failed = 0;
        
        // Sort files alphabetically for consistent loading order
        Arrays.sort(files, (a, b) -> a.getName().compareToIgnoreCase(b.getName()));
        
        for (File file : files) {
            try {
                plugin.getLogger().info("Loading mob config: " + file.getName());
                DungeonMobConfig mobConfig = DungeonMobConfig.loadFromFile(file);
                if (mobConfig != null) {
                    loadedMobs.put(mobConfig.getId(), mobConfig);
                    
                    // Organize by category
                    mobsByCategory.computeIfAbsent(mobConfig.getCategory(), k -> new ArrayList<>()).add(mobConfig);
                    
                    loaded++;
                    plugin.getLogger().info("✓ Loaded mob: " + mobConfig.getDisplayName() + 
                        " [" + mobConfig.getCategory() + "/" + mobConfig.getDifficulty() + "]");
                } else {
                    failed++;
                    plugin.getLogger().warning("✗ Failed to load mob config: " + file.getName() + " (returned null)");
                }
            } catch (Exception e) {
                failed++;
                plugin.getLogger().warning("✗ Failed to load mob config: " + file.getName() + " - " + e.getMessage());
                if (plugin.getConfig().getBoolean("debug", false)) {
                    e.printStackTrace();
                }
            }
        }
        
        plugin.getLogger().info("Mob configuration loading complete: " + loaded + " loaded, " + failed + " failed");
        if (loaded > 0) {
            plugin.getLogger().info("Available categories: " + String.join(", ", mobsByCategory.keySet()));
            plugin.getLogger().info("Total mobs: " + loadedMobs.size());
        }
    }
    
    /**
     * Create example mob configuration files.
     */
    private void createExampleMobConfigs() {
        createMobConfig("zombie_basic", "Basic Zombie", "A simple undead mob", "undead", "easy", 
            Material.ZOMBIE_HEAD, "ZOMBIE", Arrays.asList("summon zombie %x% %y% %z% {CustomName:'\"Dungeon Zombie\"'}"));
            
        createMobConfig("skeleton_archer", "Skeleton Archer", "Ranged undead attacker", "undead", "normal",
            Material.SKELETON_SKULL, "SKELETON", Arrays.asList("summon skeleton %x% %y% %z% {CustomName:'\"Skeleton Archer\"', HandItems:[{id:bow,Count:1},{}]}"));
            
        createMobConfig("spider_venomous", "Venomous Spider", "Poisonous cave spider", "beast", "normal",
            Material.SPIDER_EYE, "CAVE_SPIDER", Arrays.asList("summon cave_spider %x% %y% %z% {CustomName:'\"Venomous Spider\"', ActiveEffects:[{Id:19,Amplifier:0,Duration:999999}]}"));
            
        createMobConfig("golem_guardian", "Stone Guardian", "Powerful dungeon guardian", "magical", "hard",
            Material.IRON_BLOCK, "IRON_GOLEM", Arrays.asList("summon iron_golem %x% %y% %z% {CustomName:'\"Stone Guardian\"', Health:100.0f, Attributes:[{Name:generic.max_health,Base:100.0}]}"));
            
        createMobConfig("lich_boss", "Dungeon Lich", "Powerful undead boss", "boss", "boss",
            Material.WITHER_SKELETON_SKULL, "WITHER_SKELETON", Arrays.asList(
                "summon wither_skeleton %x% %y% %z% {CustomName:'\"Dungeon Lich\"', Health:200.0f, Attributes:[{Name:generic.max_health,Base:200.0},{Name:generic.attack_damage,Base:12.0}], HandItems:[{id:netherite_sword,Count:1,tag:{Enchantments:[{id:sharpness,lvl:3}]}},{}], ArmorItems:[{},{},{},{id:netherite_helmet,Count:1}]}"));
    }
    
    /**
     * Helper method to create a mob configuration file.
     */
    private void createMobConfig(String id, String displayName, String description, String category, 
                                String difficulty, Material icon, String mobType, List<String> spawnCommands) {
        File configFile = new File(dungeonMobsFolder, id + ".yml");
        if (configFile.exists()) return; // Don't overwrite existing files
        
        try (FileWriter writer = new FileWriter(configFile)) {
            writer.write("# " + displayName + " Configuration\n");
            writer.write("display_name: \"" + displayName + "\"\n");
            writer.write("description: \"" + description + "\"\n");
            writer.write("category: \"" + category + "\"\n");
            writer.write("difficulty: \"" + difficulty + "\"\n");
            writer.write("icon: \"" + icon.name() + "\"\n\n");
            
            writer.write("# Mob spawning configuration\n");
            writer.write("mob_type: \"" + mobType + "\"\n");
            writer.write("spawn_radius: 6\n");
            writer.write("cooldown:\n");
            writer.write("  min: " + (difficulty.equals("boss") ? 60 : 30) + "\n");
            writer.write("  max: " + (difficulty.equals("boss") ? 120 : 60) + "\n");
            writer.write("max_concurrent: " + (difficulty.equals("boss") ? 1 : 2) + "\n");
            writer.write("is_boss: " + difficulty.equals("boss") + "\n\n");
            
            writer.write("# Commands to execute when spawning\n");
            writer.write("spawn_commands:\n");
            for (String command : spawnCommands) {
                writer.write("  - \"" + command + "\"\n");
            }
            
            writer.write("\n# Information shown to builders\n");
            writer.write("builder_info:\n");
            writer.write("  - \"" + description + "\"\n");
            writer.write("  - \"Difficulty: " + difficulty + "\"\n");
            writer.write("  - \"Category: " + category + "\"\n");
            
            plugin.getLogger().info("Created example mob config: " + configFile.getName());
        } catch (IOException e) {
            plugin.getLogger().warning("Failed to create mob config " + id + ": " + e.getMessage());
        }
    }
    
    // Getters
    public Map<String, DungeonMobConfig> getLoadedMobs() {
        return new HashMap<>(loadedMobs);
    }
    
    public Map<String, List<DungeonMobConfig>> getMobsByCategory() {
        return new HashMap<>(mobsByCategory);
    }
    
    public DungeonMobConfig getMobConfig(String id) {
        return loadedMobs.get(id);
    }
    
    public List<String> getCategories() {
        return new ArrayList<>(mobsByCategory.keySet());
    }
    
    public List<DungeonMobConfig> getMobsInCategory(String category) {
        return mobsByCategory.getOrDefault(category, new ArrayList<>());
    }
    
    public List<DungeonMobConfig> getBossMobs() {
        return loadedMobs.values().stream()
            .filter(DungeonMobConfig::isBoss)
            .collect(Collectors.toList());
    }
    
    public List<DungeonMobConfig> getMobsByDifficulty(String difficulty) {
        return loadedMobs.values().stream()
            .filter(mob -> mob.getDifficulty().equalsIgnoreCase(difficulty))
            .collect(Collectors.toList());
    }
    
    /**
     * Reload all mob configurations (public method for GUI refresh).
     */
    public void reloadMobConfigs() {
        loadAllMobConfigs();
    }
}
