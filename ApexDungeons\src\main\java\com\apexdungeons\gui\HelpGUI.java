package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

/**
 * Simple help handler.  Displays a brief tutorial to the player via chat.
 */
public class HelpGUI {
    public static void open(Player player) {
        player.sendMessage(ChatColor.AQUA + "=== Soaps Dungeons Help ===");
        player.sendMessage(ChatColor.GRAY + "Use the main GUI to create new dungeons.");
        player.sendMessage(ChatColor.GRAY + "Use the Architect Wand to preview and place rooms.");
        player.sendMessage(ChatColor.GRAY + "<PERSON><PERSON> can access advanced controls via /dgn admin.");
    }

    public static void open(Player player, ApexDungeons plugin) {
        open(player); // Use the existing method for now
    }
}