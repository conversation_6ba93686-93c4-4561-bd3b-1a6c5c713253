package com.apexdungeons.listeners;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.chests.ChestSpawnPoint;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;

/**
 * Handles chest spawn tool interactions.
 */
public class ChestSpawnToolListener implements Listener {
    private final ApexDungeons plugin;
    
    public ChestSpawnToolListener(ApexDungeons plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        if (item == null || event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        
        if (!plugin.getChestSpawnTool().isChestSpawnTool(item)) {
            return;
        }
        
        event.setCancelled(true);
        
        Location clickedLocation = event.getClickedBlock().getLocation().add(0, 1, 0);
        
        // Check if there's already a chest spawn point here
        ChestSpawnPoint existing = plugin.getChestSpawnManager().getChestSpawnPoint(clickedLocation);
        if (existing != null) {
            player.sendMessage(ChatColor.YELLOW + "⚠ There's already a chest spawn point here!");
            player.sendMessage(ChatColor.GRAY + "Loot Table: " + existing.getLootTable() + 
                ", Radius: " + existing.getRadius());
            
            // Show particles to indicate existing spawn point
            showChestSpawnParticles(clickedLocation, false);
            return;
        }
        
        // Check permissions
        if (!player.hasPermission("apexdungeons.admin") && !player.hasPermission("apexdungeons.chestspawn")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to place chest spawn points!");
            return;
        }
        
        handleChestSpawnTool(player, clickedLocation);
    }
    
    private void handleChestSpawnTool(Player player, Location location) {
        // Check if player has configured a loot table
        String lootTable = plugin.getChestSpawnData().getPlayerLootTableSelection(player.getUniqueId());
        if (lootTable == null) {
            player.sendMessage(ChatColor.RED + "❌ No loot table configured!");
            player.sendMessage(ChatColor.YELLOW + "Use " + ChatColor.AQUA + "/dgn chestspawn set <loot_table>" + 
                ChatColor.YELLOW + " first");
            player.sendMessage(ChatColor.GRAY + "Available tables: " + 
                String.join(", ", plugin.getChestLootManager().getLootTableNames()));
            return;
        }
        
        double radius = plugin.getChestSpawnData().getPlayerRadius(player.getUniqueId(), 3.0);
        
        // Add the chest spawn point
        plugin.getChestSpawnManager().addChestSpawnPoint(location, lootTable, radius);
        
        // Feedback to player
        player.sendMessage(ChatColor.GREEN + "✓ Chest spawn point created!");
        player.sendMessage(ChatColor.GRAY + "Loot Table: " + ChatColor.WHITE + lootTable);
        player.sendMessage(ChatColor.GRAY + "Radius: " + ChatColor.WHITE + radius + " blocks");
        player.sendMessage(ChatColor.GRAY + "Location: " + ChatColor.WHITE + 
            location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ());
        
        // Visual and audio feedback
        showChestSpawnParticles(location, true);
        player.playSound(location, Sound.BLOCK_CHEST_OPEN, 1.0f, 1.0f);
    }
    
    private void showChestSpawnParticles(Location location, boolean isNew) {
        if (isNew) {
            // Gold particles for new chest spawns
            location.getWorld().spawnParticle(Particle.CRIT,
                location.add(0.5, 0.5, 0.5), 20, 0.5, 0.5, 0.5, 0);
            location.getWorld().spawnParticle(Particle.HAPPY_VILLAGER,
                location, 15, 0.3, 0.3, 0.3, 0);
        } else {
            // Different particles for existing chest spawns
            location.getWorld().spawnParticle(Particle.ENCHANT,
                location.add(0.5, 0.5, 0.5), 15, 0.5, 0.5, 0.5, 1);
        }
    }
}
