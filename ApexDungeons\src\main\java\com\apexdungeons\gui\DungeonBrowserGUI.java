package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * GUI for browsing available dungeon templates and presets with previews and details.
 */
public class DungeonBrowserGUI {
    private static final String GUI_NAME = ChatColor.DARK_PURPLE + "📚 Dungeon Browser";

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory(player, 54, <PERSON><PERSON>_NAME);
        
        // Fill background
        fillBackground(inv);
        
        // Create theme preview buttons
        createThemeButtons(inv, plugin);
        
        // Create navigation buttons
        createNavigationButtons(inv);
        
        player.openInventory(inv);
        
        // Register event listener
        registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.PURPLE_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        
        // Fill border
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
    }

    private static void createThemeButtons(Inventory inv, ApexDungeons plugin) {
        // Castle Theme
        ItemStack castle = new ItemStack(Material.STONE_BRICKS);
        ItemMeta castleMeta = castle.getItemMeta();
        castleMeta.setDisplayName(ChatColor.GRAY + "🏰 Castle Theme");
        List<String> castleLore = new ArrayList<>();
        castleLore.add(ChatColor.YELLOW + "Medieval Fortress Dungeons");
        castleLore.add("");
        castleLore.add(ChatColor.GRAY + "Features:");
        castleLore.add(ChatColor.AQUA + "• Stone brick architecture");
        castleLore.add(ChatColor.AQUA + "• Multi-level towers");
        castleLore.add(ChatColor.AQUA + "• Throne rooms & armories");
        castleLore.add(ChatColor.AQUA + "• Medieval atmosphere");
        castleLore.add("");
        castleLore.add(ChatColor.YELLOW + "Difficulty: " + ChatColor.GREEN + "Medium");
        castleLore.add(ChatColor.YELLOW + "Recommended Level: " + ChatColor.WHITE + "10-20");
        castleLore.add(ChatColor.YELLOW + "Estimated Time: " + ChatColor.WHITE + "25-35 minutes");
        castleLore.add("");
        castleLore.add(ChatColor.GREEN + "▶ Click to preview!");
        castleMeta.setLore(castleLore);
        castle.setItemMeta(castleMeta);
        inv.setItem(20, castle);
        
        // Cave Theme
        ItemStack cave = new ItemStack(Material.COBBLESTONE);
        ItemMeta caveMeta = cave.getItemMeta();
        caveMeta.setDisplayName(ChatColor.DARK_GRAY + "🕳 Cave Theme");
        List<String> caveLore = new ArrayList<>();
        caveLore.add(ChatColor.YELLOW + "Underground Cave Systems");
        caveLore.add("");
        caveLore.add(ChatColor.GRAY + "Features:");
        caveLore.add(ChatColor.AQUA + "• Natural stone formations");
        caveLore.add(ChatColor.AQUA + "• Underground pools & rivers");
        caveLore.add(ChatColor.AQUA + "• Ore veins & crystals");
        caveLore.add(ChatColor.AQUA + "• Dark atmosphere");
        caveLore.add("");
        caveLore.add(ChatColor.YELLOW + "Difficulty: " + ChatColor.GOLD + "Hard");
        caveLore.add(ChatColor.YELLOW + "Recommended Level: " + ChatColor.WHITE + "15-25");
        caveLore.add(ChatColor.YELLOW + "Estimated Time: " + ChatColor.WHITE + "20-30 minutes");
        caveLore.add("");
        caveLore.add(ChatColor.GREEN + "▶ Click to preview!");
        caveMeta.setLore(caveLore);
        cave.setItemMeta(caveMeta);
        inv.setItem(22, cave);
        
        // Temple Theme
        ItemStack temple = new ItemStack(Material.SANDSTONE);
        ItemMeta templeMeta = temple.getItemMeta();
        templeMeta.setDisplayName(ChatColor.GOLD + "🏛 Temple Theme");
        List<String> templeLore = new ArrayList<>();
        templeLore.add(ChatColor.YELLOW + "Ancient Mystical Temples");
        templeLore.add("");
        templeLore.add(ChatColor.GRAY + "Features:");
        templeLore.add(ChatColor.AQUA + "• Golden decorations");
        templeLore.add(ChatColor.AQUA + "• Mystical chambers");
        templeLore.add(ChatColor.AQUA + "• Ancient traps & puzzles");
        templeLore.add(ChatColor.AQUA + "• Sacred atmosphere");
        templeLore.add("");
        templeLore.add(ChatColor.YELLOW + "Difficulty: " + ChatColor.RED + "Expert");
        templeLore.add(ChatColor.YELLOW + "Recommended Level: " + ChatColor.WHITE + "20-30");
        templeLore.add(ChatColor.YELLOW + "Estimated Time: " + ChatColor.WHITE + "45-60 minutes");
        templeLore.add("");
        templeLore.add(ChatColor.GREEN + "▶ Click to preview!");
        templeMeta.setLore(templeLore);
        temple.setItemMeta(templeMeta);
        inv.setItem(24, temple);
        
        // Size Variants Info
        ItemStack sizeInfo = new ItemStack(Material.COMPASS);
        ItemMeta sizeInfoMeta = sizeInfo.getItemMeta();
        sizeInfoMeta.setDisplayName(ChatColor.YELLOW + "📏 Size Variants");
        List<String> sizeInfoLore = new ArrayList<>();
        sizeInfoLore.add(ChatColor.GRAY + "Each theme offers multiple sizes:");
        sizeInfoLore.add("");
        sizeInfoLore.add(ChatColor.GREEN + "Small: " + ChatColor.WHITE + "5-8 rooms (15-20 min)");
        sizeInfoLore.add(ChatColor.YELLOW + "Medium: " + ChatColor.WHITE + "8-12 rooms (25-35 min)");
        sizeInfoLore.add(ChatColor.GOLD + "Large: " + ChatColor.WHITE + "12-18 rooms (45-60 min)");
        sizeInfoLore.add(ChatColor.RED + "Massive: " + ChatColor.WHITE + "18-25 rooms (60-90 min)");
        sizeInfoLore.add("");
        sizeInfoLore.add(ChatColor.GRAY + "Choose your preferred size");
        sizeInfoLore.add(ChatColor.GRAY + "when creating a dungeon!");
        sizeInfoMeta.setLore(sizeInfoLore);
        sizeInfo.setItemMeta(sizeInfoMeta);
        inv.setItem(31, sizeInfo);
        
        // Features Info
        ItemStack featuresInfo = new ItemStack(Material.ENCHANTED_BOOK);
        ItemMeta featuresInfoMeta = featuresInfo.getItemMeta();
        featuresInfoMeta.setDisplayName(ChatColor.LIGHT_PURPLE + "✨ Special Features");
        List<String> featuresInfoLore = new ArrayList<>();
        featuresInfoLore.add(ChatColor.GRAY + "All dungeons include:");
        featuresInfoLore.add("");
        featuresInfoLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Isolated world instances");
        featuresInfoLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Portal travel system");
        featuresInfoLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Custom mob spawning");
        featuresInfoLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Treasure chests & loot");
        featuresInfoLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Boss encounters");
        featuresInfoLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Visual & sound effects");
        featuresInfoLore.add("");
        featuresInfoLore.add(ChatColor.GRAY + "Experience the ultimate");
        featuresInfoLore.add(ChatColor.GRAY + "dungeon adventure!");
        featuresInfoMeta.setLore(featuresInfoLore);
        featuresInfo.setItemMeta(featuresInfoMeta);
        inv.setItem(40, featuresInfo);
    }

    private static void createNavigationButtons(Inventory inv) {
        // Back button
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(ChatColor.RED + "← Back to Main Menu");
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        
        // Create Dungeon button
        ItemStack create = new ItemStack(Material.NETHER_STAR);
        ItemMeta createMeta = create.getItemMeta();
        createMeta.setDisplayName(ChatColor.GREEN + "✚ Create Dungeon");
        List<String> createLore = new ArrayList<>();
        createLore.add(ChatColor.GRAY + "Ready to create your dungeon?");
        createLore.add(ChatColor.GREEN + "Click to start the creation process!");
        createMeta.setLore(createLore);
        create.setItemMeta(createMeta);
        inv.setItem(53, create);
    }

    private static void registerEventListener(ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player) e.getWhoClicked();
                    int slot = e.getRawSlot();
                    
                    switch (slot) {
                        case 20: // Castle Theme
                            clicker.closeInventory();
                            ThemePreviewGUI.open(clicker, plugin, "castle");
                            break;
                        case 22: // Cave Theme
                            clicker.closeInventory();
                            ThemePreviewGUI.open(clicker, plugin, "cave");
                            break;
                        case 24: // Temple Theme
                            clicker.closeInventory();
                            ThemePreviewGUI.open(clicker, plugin, "temple");
                            break;
                        case 45: // Back
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            break;
                        case 53: // Create Dungeon
                            clicker.closeInventory();
                            PresetGUI.open(clicker, plugin);
                            break;
                    }
                }
            }
        }, plugin);
    }
}
