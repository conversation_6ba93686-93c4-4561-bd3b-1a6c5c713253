package org.bukkit.plugin.java;

import java.io.File;
import java.io.InputStream;
import java.util.logging.Logger;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.PluginDescriptionFile;
import org.bukkit.plugin.PluginLoader;

/**
 * Minimal stub for Bukkit's JavaPlugin used solely for compilation.  Methods
 * return default values and do not interact with a real server.
 */
public abstract class JavaPlugin implements Plugin {
    public File getDataFolder() { return new File("."); }
    public Logger getLogger() { return Logger.getLogger("ApexDungeonsStub"); }
    public PluginDescriptionFile getDescription() { return null; }
    public void saveDefaultConfig() {}
    public void saveResource(String resourcePath, boolean replace) {}
    public InputStream getResource(String path) { return null; }
    public org.bukkit.configuration.file.FileConfiguration getConfig() { return null; }
    public void onEnable() {}
    public void onDisable() {}
    public PluginLoader getPluginLoader() { return null; }
    public String getName() { return "ApexDungeonsStub"; }
    private final org.bukkit.server.Server server = new org.bukkit.server.Server();

    /**
     * Returns a stub server instance.  Provided so plugin code can access
     * scheduler and plugin manager in a testing context.
     * @return stub server
     */
    public org.bukkit.server.Server getServer() { return server; }
    public boolean isEnabled() { return true; }
    public void onLoad() {}
    public void reloadConfig() {}
    public void saveConfig() {}
    public void saveDefaultConfig(org.bukkit.configuration.file.FileConfiguration config) {}
    public void saveResource(String resourcePath, boolean replace, boolean copy) {}
    public org.bukkit.command.PluginCommand getCommand(String name) { return null; }
}