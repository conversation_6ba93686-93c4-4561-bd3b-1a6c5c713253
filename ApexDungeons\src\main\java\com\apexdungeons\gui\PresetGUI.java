package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonManager;
import com.apexdungeons.gen.DungeonPreset;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.ArrayList;
import java.util.List;

/**
 * GUI for selecting a dungeon preset.  Presets are defined as YAML files in
 * plugins/ApexDungeons/presets/ and specify max rooms, branch chance and
 * themes.  On selection a dungeon is generated immediately.
 */
public class PresetGUI {
    private static final String GUI_NAME = ChatColor.DARK_GRAY + "Select Preset";

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory(player, 27, GUI_NAME);
        int index = 11;
        for (String presetName : new String[]{"small", "medium", "large"}) {
            ItemStack paper = new ItemStack(Material.PAPER);
            ItemMeta meta = paper.getItemMeta();
            meta.setDisplayName(ChatColor.GOLD + presetName.substring(0,1).toUpperCase() + presetName.substring(1) + " Preset");
            List<String> lore = new ArrayList<>();
            DungeonPreset preset = plugin.getDungeonManager().loadPreset(presetName);
            if (preset != null) {
                lore.add(ChatColor.GRAY + "Max Rooms: " + preset.getMaxRooms());
                lore.add(ChatColor.GRAY + "Branch Chance: " + preset.getBranchChance());
                lore.add(ChatColor.GRAY + "Themes: " + String.join(", ", preset.getThemes()));
            }
            meta.setLore(lore);
            paper.setItemMeta(meta);
            inv.setItem(index, paper);
            index += 2;
        }
        JavaPlugin pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    if (slot == 11 || slot == 13 || slot == 15) {
                        String presetName = switch (slot) {
                            case 11 -> "small";
                            case 13 -> "medium";
                            default -> "large";
                        };
                        e.getWhoClicked().closeInventory();
                        // Open naming GUI instead of directly creating dungeon
                        NamingGUI.open((Player) e.getWhoClicked(), plugin, presetName);
                    }
                }
            }
        }, pl);
        player.openInventory(inv);
    }
}