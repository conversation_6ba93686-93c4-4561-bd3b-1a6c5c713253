package org.bukkit.inventory;

import org.bukkit.persistence.PersistentDataContainer;

import java.util.List;

/**
 * Stub for ItemMeta.  Stores basic properties such as display name and lore.
 */
public class ItemMeta {
    private String displayName;
    private List<String> lore;
    private final PersistentDataContainer container = new PersistentDataContainer();
    public void setDisplayName(String name) { this.displayName = name; }
    public String getDisplayName() { return this.displayName; }
    public void setLore(List<String> lore) { this.lore = lore; }
    public List<String> getLore() { return this.lore; }
    public PersistentDataContainer getPersistentDataContainer() { return container; }
}