package com.apexdungeons.templates;

import org.bukkit.Location;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Represents a saved dungeon template that can be loaded and reused.
 */
public class DungeonTemplate implements Serializable {
    private String name;
    private String description;
    private String creator;
    private long createdTime;
    private String worldName;
    private Location spawnLocation;
    private Location exitLocation;
    private List<Location> startBlocks;
    private List<Location> endBlocks;
    private Map<String, Object> settings;
    private List<PortalData> portals;
    private String schematicFile; // Optional schematic file for the entire dungeon

    public DungeonTemplate(String name) {
        this.name = name;
        this.description = "";
        this.creator = "";
        this.createdTime = System.currentTimeMillis();
        this.startBlocks = new ArrayList<>();
        this.endBlocks = new ArrayList<>();
        this.settings = new HashMap<>();
        this.portals = new ArrayList<>();
    }

    /**
     * Create template from configuration.
     */
    public static DungeonTemplate fromConfig(FileConfiguration config) {
        String name = config.getString("template.name", "Unknown");
        DungeonTemplate template = new DungeonTemplate(name);
        
        template.description = config.getString("template.description", "");
        template.creator = config.getString("template.creator", "");
        template.createdTime = config.getLong("template.created", System.currentTimeMillis());
        template.worldName = config.getString("template.world", "");
        template.schematicFile = config.getString("template.schematic", "");
        
        // Load spawn location
        if (config.contains("spawn")) {
            template.spawnLocation = loadLocationFromConfig(config, "spawn");
        }
        
        // Load exit location
        if (config.contains("exit")) {
            template.exitLocation = loadLocationFromConfig(config, "exit");
        }
        
        // Load start blocks
        if (config.contains("start_blocks")) {
            List<Map<?, ?>> startBlocksList = config.getMapList("start_blocks");
            for (Map<?, ?> blockData : startBlocksList) {
                Location loc = loadLocationFromMap(blockData);
                if (loc != null) {
                    template.startBlocks.add(loc);
                }
            }
        }
        
        // Load end blocks
        if (config.contains("end_blocks")) {
            List<Map<?, ?>> endBlocksList = config.getMapList("end_blocks");
            for (Map<?, ?> blockData : endBlocksList) {
                Location loc = loadLocationFromMap(blockData);
                if (loc != null) {
                    template.endBlocks.add(loc);
                }
            }
        }
        
        // Load settings
        if (config.contains("settings")) {
            template.settings = config.getConfigurationSection("settings").getValues(false);
        }
        
        // Load portals
        if (config.contains("portals")) {
            List<Map<?, ?>> portalsList = config.getMapList("portals");
            for (Map<?, ?> portalData : portalsList) {
                PortalData portal = PortalData.fromMap(portalData);
                if (portal != null) {
                    template.portals.add(portal);
                }
            }
        }
        
        return template;
    }

    /**
     * Save template to configuration.
     */
    public void saveToConfig(FileConfiguration config) {
        config.set("template.name", name);
        config.set("template.description", description);
        config.set("template.creator", creator);
        config.set("template.created", createdTime);
        config.set("template.world", worldName);
        config.set("template.schematic", schematicFile);
        
        // Save spawn location
        if (spawnLocation != null) {
            saveLocationToConfig(config, "spawn", spawnLocation);
        }
        
        // Save exit location
        if (exitLocation != null) {
            saveLocationToConfig(config, "exit", exitLocation);
        }
        
        // Save start blocks
        List<Map<String, Object>> startBlocksList = new ArrayList<>();
        for (Location loc : startBlocks) {
            startBlocksList.add(locationToMap(loc));
        }
        config.set("start_blocks", startBlocksList);
        
        // Save end blocks
        List<Map<String, Object>> endBlocksList = new ArrayList<>();
        for (Location loc : endBlocks) {
            endBlocksList.add(locationToMap(loc));
        }
        config.set("end_blocks", endBlocksList);
        
        // Save settings
        if (!settings.isEmpty()) {
            for (Map.Entry<String, Object> entry : settings.entrySet()) {
                config.set("settings." + entry.getKey(), entry.getValue());
            }
        }
        
        // Save portals
        List<Map<String, Object>> portalsList = new ArrayList<>();
        for (PortalData portal : portals) {
            portalsList.add(portal.toMap());
        }
        config.set("portals", portalsList);
    }

    /**
     * Helper method to load location from config.
     */
    private static Location loadLocationFromConfig(FileConfiguration config, String path) {
        if (!config.contains(path + ".world")) return null;
        
        // Note: We store relative coordinates, actual world will be determined during loading
        double x = config.getDouble(path + ".x", 0);
        double y = config.getDouble(path + ".y", 64);
        double z = config.getDouble(path + ".z", 0);
        float yaw = (float) config.getDouble(path + ".yaw", 0);
        float pitch = (float) config.getDouble(path + ".pitch", 0);
        
        // Return location without world (will be set during loading)
        return new Location(null, x, y, z, yaw, pitch);
    }

    /**
     * Helper method to load location from map.
     */
    private static Location loadLocationFromMap(Map<?, ?> map) {
        if (!map.containsKey("x") || !map.containsKey("y") || !map.containsKey("z")) {
            return null;
        }
        
        double x = ((Number) map.get("x")).doubleValue();
        double y = ((Number) map.get("y")).doubleValue();
        double z = ((Number) map.get("z")).doubleValue();
        float yaw = map.containsKey("yaw") ? ((Number) map.get("yaw")).floatValue() : 0f;
        float pitch = map.containsKey("pitch") ? ((Number) map.get("pitch")).floatValue() : 0f;
        
        return new Location(null, x, y, z, yaw, pitch);
    }

    /**
     * Helper method to save location to config.
     */
    private void saveLocationToConfig(FileConfiguration config, String path, Location location) {
        config.set(path + ".x", location.getX());
        config.set(path + ".y", location.getY());
        config.set(path + ".z", location.getZ());
        config.set(path + ".yaw", location.getYaw());
        config.set(path + ".pitch", location.getPitch());
    }

    /**
     * Helper method to convert location to map.
     */
    private Map<String, Object> locationToMap(Location location) {
        Map<String, Object> map = new HashMap<>();
        map.put("x", location.getX());
        map.put("y", location.getY());
        map.put("z", location.getZ());
        map.put("yaw", location.getYaw());
        map.put("pitch", location.getPitch());
        return map;
    }

    // Getters and setters
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getCreator() { return creator; }
    public void setCreator(String creator) { this.creator = creator; }
    
    public long getCreatedTime() { return createdTime; }
    public void setCreatedTime(long createdTime) { this.createdTime = createdTime; }
    
    public String getWorldName() { return worldName; }
    public void setWorldName(String worldName) { this.worldName = worldName; }
    
    public Location getSpawnLocation() { return spawnLocation; }
    public void setSpawnLocation(Location spawnLocation) { this.spawnLocation = spawnLocation; }
    
    public Location getExitLocation() { return exitLocation; }
    public void setExitLocation(Location exitLocation) { this.exitLocation = exitLocation; }
    
    public List<Location> getStartBlocks() { return startBlocks; }
    public void setStartBlocks(List<Location> startBlocks) { this.startBlocks = startBlocks; }
    
    public List<Location> getEndBlocks() { return endBlocks; }
    public void setEndBlocks(List<Location> endBlocks) { this.endBlocks = endBlocks; }
    
    public Map<String, Object> getSettings() { return settings; }
    public void setSettings(Map<String, Object> settings) { this.settings = settings; }
    
    public List<PortalData> getPortals() { return portals; }
    public void setPortals(List<PortalData> portals) { this.portals = portals; }
    
    public String getSchematicFile() { return schematicFile; }
    public void setSchematicFile(String schematicFile) { this.schematicFile = schematicFile; }

    /**
     * Inner class for portal data.
     */
    public static class PortalData {
        private Location corner1;
        private Location corner2;
        private String destination;
        private String destinationType; // "dungeon", "world", "location"
        
        public PortalData(Location corner1, Location corner2, String destination, String destinationType) {
            this.corner1 = corner1;
            this.corner2 = corner2;
            this.destination = destination;
            this.destinationType = destinationType;
        }
        
        public static PortalData fromMap(Map<?, ?> map) {
            if (!map.containsKey("corner1") || !map.containsKey("corner2")) {
                return null;
            }
            
            Location corner1 = loadLocationFromMap((Map<?, ?>) map.get("corner1"));
            Location corner2 = loadLocationFromMap((Map<?, ?>) map.get("corner2"));
            String destination = (String) map.get("destination");
            String destinationType = (String) map.get("destination_type");
            
            return new PortalData(corner1, corner2, destination, destinationType);
        }
        
        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            map.put("corner1", locationToMap(corner1));
            map.put("corner2", locationToMap(corner2));
            map.put("destination", destination);
            map.put("destination_type", destinationType);
            return map;
        }
        
        private static Location loadLocationFromMap(Map<?, ?> map) {
            double x = ((Number) map.get("x")).doubleValue();
            double y = ((Number) map.get("y")).doubleValue();
            double z = ((Number) map.get("z")).doubleValue();
            return new Location(null, x, y, z);
        }
        
        private Map<String, Object> locationToMap(Location location) {
            Map<String, Object> map = new HashMap<>();
            map.put("x", location.getX());
            map.put("y", location.getY());
            map.put("z", location.getZ());
            return map;
        }
        
        // Getters and setters
        public Location getCorner1() { return corner1; }
        public Location getCorner2() { return corner2; }
        public String getDestination() { return destination; }
        public String getDestinationType() { return destinationType; }
    }
}
