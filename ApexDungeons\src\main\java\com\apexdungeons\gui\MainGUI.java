package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonPreset;
import com.apexdungeons.gen.DungeonManager;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Main GUI shown to players.  Provides buttons for creating dungeons, viewing
 * active dungeons and a help screen.
 */
public class MainGUI implements Listener {
    private static final String GUI_NAME = ChatColor.DARK_PURPLE + "🏰 Soaps Dungeons";
    private static final int SIZE = 27;

    public static void open(@NotNull Player player, @NotNull ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory(player, SIZE, GUI_NAME);
        // Create dungeon button
        ItemStack create = new ItemStack(Material.NETHER_STAR);
        ItemMeta cm = create.getItemMeta();
        cm.setDisplayName(ChatColor.GREEN + "Create Dungeon");
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "Generate a new dungeon using a preset");
        cm.setLore(lore);
        create.setItemMeta(cm);
        inv.setItem(11, create);
        // Active dungeons button
        ItemStack active = new ItemStack(Material.ENDER_EYE);
        ItemMeta am = active.getItemMeta();
        am.setDisplayName(ChatColor.AQUA + "Active Dungeons");
        am.setLore(List.of(ChatColor.GRAY + "Teleport to an existing dungeon"));
        active.setItemMeta(am);
        inv.setItem(13, active);
        // Help button
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta hm = help.getItemMeta();
        hm.setDisplayName(ChatColor.YELLOW + "Help");
        hm.setLore(List.of(ChatColor.GRAY + "Learn how to use ApexDungeons"));
        help.setItemMeta(hm);
        inv.setItem(15, help);
        // Register listener and open
        JavaPlugin pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    if (slot == 11) {
                        // Create dungeon preset selection
                        e.getWhoClicked().closeInventory();
                        PresetGUI.open((Player) e.getWhoClicked(), plugin);
                    } else if (slot == 13) {
                        e.getWhoClicked().closeInventory();
                        ActiveGUI.open((Player) e.getWhoClicked(), plugin);
                    } else if (slot == 15) {
                        e.getWhoClicked().closeInventory();
                        HelpGUI.open((Player) e.getWhoClicked());
                    }
                }
            }
        }, pl);
        player.openInventory(inv);
    }
}