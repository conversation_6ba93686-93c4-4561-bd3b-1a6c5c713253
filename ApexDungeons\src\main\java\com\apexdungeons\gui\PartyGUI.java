package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.party.Party;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * GUI for displaying and managing party members with player heads.
 */
public class PartyGUI implements Listener {
    private final ApexDungeons plugin;
    private static final String GUI_TITLE = ChatColor.DARK_PURPLE + "Party Management";

    public PartyGUI(ApexDungeons plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Open party GUI for a player.
     */
    public void openPartyGUI(Player player) {
        Party party = plugin.getPartyManager().getPlayerParty(player);
        if (party == null) {
            player.sendMessage(ChatColor.RED + "You are not in a party! Use /dgn party create to create one.");
            return;
        }

        Inventory inv = Bukkit.createInventory(null, 27, GUI_TITLE);
        
        // Fill background
        fillBackground(inv);
        
        // Add party member heads
        addPartyMembers(inv, party, player);
        
        // Add action buttons
        addActionButtons(inv, party, player);
        
        player.openInventory(inv);
    }

    /**
     * Fill background with glass panes.
     */
    private void fillBackground(Inventory inv) {
        ItemStack glass = new ItemStack(Material.BLACK_STAINED_GLASS_PANE);
        ItemMeta glassMeta = glass.getItemMeta();
        glassMeta.setDisplayName(" ");
        glass.setItemMeta(glassMeta);
        
        for (int i = 0; i < inv.getSize(); i++) {
            inv.setItem(i, glass);
        }
    }

    /**
     * Add party member heads to the GUI.
     */
    private void addPartyMembers(Inventory inv, Party party, Player viewer) {
        List<Player> members = party.getMembers();
        int[] memberSlots = {10, 11, 12, 13}; // Slots for up to 4 members
        
        for (int i = 0; i < Math.min(members.size(), 4); i++) {
            Player member = members.get(i);
            ItemStack head = createPlayerHead(member, party, viewer);
            inv.setItem(memberSlots[i], head);
        }
    }

    /**
     * Create a player head item with party information.
     */
    private ItemStack createPlayerHead(Player player, Party party, Player viewer) {
        ItemStack head = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta meta = (SkullMeta) head.getItemMeta();
        
        meta.setOwningPlayer(player);
        
        // Set display name
        if (party.isLeader(player)) {
            meta.setDisplayName(ChatColor.GOLD + "👑 " + player.getName() + " (Leader)");
        } else {
            meta.setDisplayName(ChatColor.GREEN + "👤 " + player.getName());
        }
        
        // Set lore with player information
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add(ChatColor.GRAY + "Status: " + (player.isOnline() ? ChatColor.GREEN + "Online" : ChatColor.RED + "Offline"));
        
        if (player.isOnline()) {
            lore.add(ChatColor.GRAY + "Health: " + ChatColor.RED + "❤ " + (int) player.getHealth() + "/20");
            lore.add(ChatColor.GRAY + "Level: " + ChatColor.AQUA + player.getLevel());
            
            if (player.getWorld() != null) {
                lore.add(ChatColor.GRAY + "World: " + ChatColor.YELLOW + player.getWorld().getName());
            }
        }
        
        lore.add("");
        
        // Add action options for party leader
        if (party.isLeader(viewer) && !player.equals(viewer)) {
            lore.add(ChatColor.YELLOW + "🔧 Leader Actions:");
            lore.add(ChatColor.RED + "• Right-click to kick");
            lore.add(ChatColor.BLUE + "• Shift+Right-click for transfer leadership");
        } else if (player.equals(viewer)) {
            lore.add(ChatColor.AQUA + "This is you!");
        }
        
        meta.setLore(lore);
        head.setItemMeta(meta);
        
        return head;
    }

    /**
     * Add action buttons to the GUI.
     */
    private void addActionButtons(Inventory inv, Party party, Player viewer) {
        // Party info button
        ItemStack infoButton = new ItemStack(Material.BOOK);
        ItemMeta infoMeta = infoButton.getItemMeta();
        infoMeta.setDisplayName(ChatColor.AQUA + "📋 Party Information");
        List<String> infoLore = new ArrayList<>();
        infoLore.add("");
        infoLore.add(ChatColor.GRAY + "Party Size: " + ChatColor.WHITE + party.getSize() + "/" + party.getMaxSize());
        infoLore.add(ChatColor.GRAY + "Available Slots: " + ChatColor.WHITE + party.getAvailableSlots());
        infoLore.add(ChatColor.GRAY + "Leader: " + ChatColor.GOLD + party.getLeader().getName());
        infoLore.add("");
        
        long createdMinutesAgo = (System.currentTimeMillis() - party.getCreatedTime()) / 60000;
        infoLore.add(ChatColor.GRAY + "Created: " + ChatColor.WHITE + createdMinutesAgo + " minutes ago");
        
        infoMeta.setLore(infoLore);
        infoButton.setItemMeta(infoMeta);
        inv.setItem(4, infoButton);
        
        // Invite button (only for leader)
        if (party.isLeader(viewer) && !party.isFull()) {
            ItemStack inviteButton = new ItemStack(Material.EMERALD);
            ItemMeta inviteMeta = inviteButton.getItemMeta();
            inviteMeta.setDisplayName(ChatColor.GREEN + "➕ Invite Player");
            List<String> inviteLore = new ArrayList<>();
            inviteLore.add("");
            inviteLore.add(ChatColor.GRAY + "Click to close GUI and use:");
            inviteLore.add(ChatColor.YELLOW + "/dgn party invite <player>");
            inviteLore.add("");
            inviteLore.add(ChatColor.AQUA + "Available slots: " + party.getAvailableSlots());
            inviteMeta.setLore(inviteLore);
            inviteButton.setItemMeta(inviteMeta);
            inv.setItem(19, inviteButton);
        }
        
        // Leave party button
        ItemStack leaveButton = new ItemStack(Material.BARRIER);
        ItemMeta leaveMeta = leaveButton.getItemMeta();
        if (party.isLeader(viewer)) {
            leaveMeta.setDisplayName(ChatColor.RED + "🚪 Disband Party");
            List<String> leaveLore = new ArrayList<>();
            leaveLore.add("");
            leaveLore.add(ChatColor.GRAY + "As the leader, leaving will");
            leaveLore.add(ChatColor.GRAY + "disband the entire party.");
            leaveLore.add("");
            leaveLore.add(ChatColor.RED + "⚠ This action cannot be undone!");
            leaveMeta.setLore(leaveLore);
        } else {
            leaveMeta.setDisplayName(ChatColor.RED + "🚪 Leave Party");
            List<String> leaveLore = new ArrayList<>();
            leaveLore.add("");
            leaveLore.add(ChatColor.GRAY + "Leave the current party.");
            leaveLore.add(ChatColor.GRAY + "You can be invited back later.");
            leaveMeta.setLore(leaveLore);
        }
        leaveButton.setItemMeta(leaveMeta);
        inv.setItem(22, leaveButton);
        
        // Start dungeon button (only for leader)
        if (party.isLeader(viewer)) {
            ItemStack startButton = new ItemStack(Material.DIAMOND_SWORD);
            ItemMeta startMeta = startButton.getItemMeta();
            startMeta.setDisplayName(ChatColor.GOLD + "⚔ Start Dungeon");
            List<String> startLore = new ArrayList<>();
            startLore.add("");
            startLore.add(ChatColor.GRAY + "Start a dungeon run with your party.");
            startLore.add(ChatColor.GRAY + "All members will be teleported together.");
            startLore.add("");
            startLore.add(ChatColor.YELLOW + "Click to see available dungeons!");
            startMeta.setLore(startLore);
            startButton.setItemMeta(startMeta);
            inv.setItem(25, startButton);
        }
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!event.getView().getTitle().equals(GUI_TITLE)) {
            return;
        }
        
        event.setCancelled(true);
        
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        ItemStack clicked = event.getCurrentItem();
        
        if (clicked == null || clicked.getType() == Material.AIR) {
            return;
        }
        
        Party party = plugin.getPartyManager().getPlayerParty(player);
        if (party == null) {
            player.closeInventory();
            player.sendMessage(ChatColor.RED + "You are no longer in a party!");
            return;
        }
        
        int slot = event.getSlot();
        
        // Handle player head clicks (member management)
        if (slot >= 10 && slot <= 13) {
            handleMemberClick(player, party, slot, event.isRightClick(), event.isShiftClick());
        }
        // Handle action buttons
        else if (slot == 19) { // Invite button
            if (party.isLeader(player) && !party.isFull()) {
                player.closeInventory();
                player.sendMessage(ChatColor.GREEN + "Use " + ChatColor.YELLOW + "/dgn party invite <player>" + 
                                 ChatColor.GREEN + " to invite someone to your party!");
            }
        }
        else if (slot == 22) { // Leave/Disband button
            player.closeInventory();
            if (plugin.getPartyManager().leaveParty(player)) {
                // Success message is handled in PartyManager
            } else {
                player.sendMessage(ChatColor.RED + "Failed to leave party!");
            }
        }
        else if (slot == 25) { // Start dungeon button
            if (party.isLeader(player)) {
                player.closeInventory();
                player.sendMessage(ChatColor.GREEN + "Use " + ChatColor.YELLOW + "/dgn start <dungeon>" + 
                                 ChatColor.GREEN + " to start a dungeon with your party!");
            }
        }
    }

    /**
     * Handle clicks on party member heads.
     */
    private void handleMemberClick(Player clicker, Party party, int slot, boolean rightClick, boolean shiftClick) {
        List<Player> members = party.getMembers();
        int memberIndex = slot - 10;
        
        if (memberIndex >= members.size()) {
            return;
        }
        
        Player target = members.get(memberIndex);
        
        // Only party leader can perform actions on other members
        if (!party.isLeader(clicker) || target.equals(clicker)) {
            return;
        }
        
        if (rightClick && shiftClick) {
            // Transfer leadership
            clicker.closeInventory();
            if (party.transferLeadership(target)) {
                clicker.sendMessage(ChatColor.GREEN + "Transferred party leadership to " + target.getName() + "!");
                target.sendMessage(ChatColor.GOLD + "You are now the party leader!");
                
                // Notify other members
                for (Player member : party.getMembers()) {
                    if (!member.equals(clicker) && !member.equals(target)) {
                        member.sendMessage(ChatColor.YELLOW + clicker.getName() + " transferred leadership to " + target.getName());
                    }
                }
            } else {
                clicker.sendMessage(ChatColor.RED + "Failed to transfer leadership!");
            }
        } else if (rightClick) {
            // Kick player
            clicker.closeInventory();
            if (plugin.getPartyManager().kickPlayer(clicker, target)) {
                // Success message is handled in PartyManager
            } else {
                clicker.sendMessage(ChatColor.RED + "Failed to kick player!");
            }
        }
    }
}
