package org.bukkit;

import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.inventory.Inventory;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.PluginManager;
import org.bukkit.scheduler.BukkitScheduler;

import java.util.ArrayList;
import java.util.List;

/**
 * Stub for Bukkit static methods.  Returns dummy objects where appropriate.
 */
public final class Bukkit {
    private static final PluginManager pluginManager = new PluginManager();
    private static final BukkitScheduler scheduler = new BukkitScheduler();
    private static final List<Player> players = new ArrayList<>();
    public static PluginManager getPluginManager() { return pluginManager; }
    public static BukkitScheduler getScheduler() { return scheduler; }
    public static Inventory createInventory(Player owner, int size, String title) { return new Inventory(owner, size, title); }
    public static List<Player> getOnlinePlayers() { return players; }
    public static Player getPlayerExact(String name) { return null; }
    public static Plugin getPlugin(String name) { return null; }
}