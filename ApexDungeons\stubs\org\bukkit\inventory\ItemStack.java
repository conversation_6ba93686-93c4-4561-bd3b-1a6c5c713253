package org.bukkit.inventory;

import org.bukkit.Material;

/**
 * Minimal stub for ItemStack.  Contains a Material and ItemMeta.  Methods are
 * sufficient for compilation but do not reflect real Bukkit behaviour.
 */
public class ItemStack {
    private Material type;
    private ItemMeta meta;
    private int amount = 1;
    public ItemStack(Material type) { this.type = type; }
    public Material getType() { return type; }
    public void setType(Material type) { this.type = type; }
    /**
     * Returns the meta for this item.  To satisfy code that expects
     * org.bukkit.inventory.meta.ItemMeta, this method returns an instance
     * of that class.  Internally the meta object is stored as the base
     * ItemMeta type but cast to the meta subpackage interface on return.
     * @return the item meta
     */
    public org.bukkit.inventory.meta.ItemMeta getItemMeta() {
        if (meta == null) meta = new ItemMeta();
        // The cast is safe because our meta subpackage class extends
        // org.bukkit.inventory.ItemMeta
        return (org.bukkit.inventory.meta.ItemMeta) (Object) meta;
    }
    /**
     * Sets the item meta.  Accepts a meta instance from either the base
     * ItemMeta class or the meta subpackage interface.  Stores it as the
     * base ItemMeta type internally.
     * @param meta meta instance
     */
    public void setItemMeta(org.bukkit.inventory.meta.ItemMeta meta) {
        this.meta = (ItemMeta) meta;
    }
    public void setAmount(int amount) { this.amount = amount; }
    public int getAmount() { return amount; }

    /**
     * Returns true if this item has metadata.  Stubbed implementation returns
     * whether the meta object has been set via setItemMeta().
     * @return true if meta not null
     */
    public boolean hasItemMeta() {
        return this.meta != null;
    }
}