package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.schematics.SchematicData;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.ArrayList;
import java.util.List;

/**
 * Confirmation GUI for placing large or complex schematics.
 * Provides detailed information and confirmation before placement.
 */
public class SchematicConfirmationGUI {
    private static final String GUI_NAME = ChatColor.DARK_RED + "⚠ Confirm Schematic Placement";

    public static void open(Player player, ApexDungeons plugin, String schematicName, Location location, int rotation) {
        SchematicData schematic = plugin.getSchematicManager().getSchematic(schematicName);
        if (schematic == null) {
            player.sendMessage(ChatColor.RED + "Schematic not found: " + schematicName);
            return;
        }

        Inventory inv = Bukkit.createInventory(player, 27, GUI_NAME);
        
        // Fill background
        fillBackground(inv);
        
        // Create confirmation items
        createSchematicInfo(inv, schematic, location, rotation);
        createConfirmationButtons(inv, schematicName, location, rotation);
        
        player.openInventory(inv);
        
        // Register event listener
        registerEventListener(plugin, schematicName, location, rotation);
    }

    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.RED_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        
        // Fill border with background
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
    }

    private static void createSchematicInfo(Inventory inv, SchematicData schematic, Location location, int rotation) {
        // Schematic info display
        ItemStack info = new ItemStack(Material.WRITTEN_BOOK);
        ItemMeta infoMeta = info.getItemMeta();
        infoMeta.setDisplayName(ChatColor.GOLD + "📋 Schematic Information");
        
        List<String> infoLore = new ArrayList<>();
        infoLore.add(ChatColor.GRAY + "Review the details before placement");
        infoLore.add("");
        infoLore.add(ChatColor.YELLOW + "Name: " + ChatColor.WHITE + schematic.getName());
        infoLore.add(ChatColor.YELLOW + "Dimensions: " + ChatColor.WHITE + 
            schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getDepth());
        
        // Count blocks
        int totalBlocks = schematic.getWidth() * schematic.getHeight() * schematic.getDepth();
        int nonAirBlocks = countNonAirBlocks(schematic);
        infoLore.add(ChatColor.YELLOW + "Total Blocks: " + ChatColor.WHITE + totalBlocks);
        infoLore.add(ChatColor.YELLOW + "Solid Blocks: " + ChatColor.WHITE + nonAirBlocks);
        infoLore.add(ChatColor.YELLOW + "Rotation: " + ChatColor.WHITE + rotation + "°");
        infoLore.add("");
        infoLore.add(ChatColor.YELLOW + "Placement Location:");
        infoLore.add(ChatColor.WHITE + "  X: " + location.getBlockX());
        infoLore.add(ChatColor.WHITE + "  Y: " + location.getBlockY());
        infoLore.add(ChatColor.WHITE + "  Z: " + location.getBlockZ());
        infoLore.add(ChatColor.WHITE + "  World: " + location.getWorld().getName());
        infoLore.add("");
        
        // Complexity warning
        String complexity = getComplexityLevel(nonAirBlocks);
        if (nonAirBlocks > 200) {
            infoLore.add(ChatColor.RED + "⚠ Large Structure Warning:");
            infoLore.add(ChatColor.YELLOW + "This is a " + complexity + " structure");
            infoLore.add(ChatColor.YELLOW + "Placement may take several seconds");
            infoLore.add(ChatColor.YELLOW + "and could cause temporary lag");
        } else {
            infoLore.add(ChatColor.GREEN + "✓ Structure Complexity: " + complexity);
        }
        
        infoMeta.setLore(infoLore);
        info.setItemMeta(infoMeta);
        inv.setItem(13, info);
    }

    private static void createConfirmationButtons(Inventory inv, String schematicName, Location location, int rotation) {
        // Confirm button
        ItemStack confirm = new ItemStack(Material.EMERALD_BLOCK);
        ItemMeta confirmMeta = confirm.getItemMeta();
        confirmMeta.setDisplayName(ChatColor.GREEN + "✓ Confirm Placement");
        List<String> confirmLore = new ArrayList<>();
        confirmLore.add(ChatColor.GRAY + "Place the schematic at the");
        confirmLore.add(ChatColor.GRAY + "specified location with current");
        confirmLore.add(ChatColor.GRAY + "rotation settings");
        confirmLore.add("");
        confirmLore.add(ChatColor.GREEN + "Click to confirm!");
        confirmMeta.setLore(confirmLore);
        confirm.setItemMeta(confirmMeta);
        inv.setItem(10, confirm);
        
        // Cancel button
        ItemStack cancel = new ItemStack(Material.REDSTONE_BLOCK);
        ItemMeta cancelMeta = cancel.getItemMeta();
        cancelMeta.setDisplayName(ChatColor.RED + "✗ Cancel Placement");
        List<String> cancelLore = new ArrayList<>();
        cancelLore.add(ChatColor.GRAY + "Cancel the schematic placement");
        cancelLore.add(ChatColor.GRAY + "and return to the previous menu");
        cancelLore.add("");
        cancelLore.add(ChatColor.RED + "Click to cancel!");
        cancelMeta.setLore(cancelLore);
        cancel.setItemMeta(cancelMeta);
        inv.setItem(16, cancel);
        
        // Preview button
        ItemStack preview = new ItemStack(Material.ENDER_EYE);
        ItemMeta previewMeta = preview.getItemMeta();
        previewMeta.setDisplayName(ChatColor.AQUA + "👁 Show Preview Again");
        List<String> previewLore = new ArrayList<>();
        previewLore.add(ChatColor.GRAY + "Return to the 3D preview mode");
        previewLore.add(ChatColor.GRAY + "to adjust position or rotation");
        previewLore.add("");
        previewLore.add(ChatColor.AQUA + "Click to preview!");
        previewMeta.setLore(previewLore);
        preview.setItemMeta(previewMeta);
        inv.setItem(14, preview);
    }

    private static void registerEventListener(ApexDungeons plugin, String schematicName, Location location, int rotation) {
        JavaPlugin pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    Player clicker = (Player) e.getWhoClicked();
                    
                    switch (slot) {
                        case 10: // Confirm
                            clicker.closeInventory();
                            confirmPlacement(clicker, plugin, schematicName, location, rotation);
                            break;
                        case 14: // Preview
                            clicker.closeInventory();
                            returnToPreview(clicker, plugin, schematicName, location, rotation);
                            break;
                        case 16: // Cancel
                            clicker.closeInventory();
                            clicker.sendMessage(ChatColor.YELLOW + "Schematic placement cancelled.");
                            break;
                    }
                }
            }
        }, pl);
    }

    private static void confirmPlacement(Player player, ApexDungeons plugin, String schematicName, Location location, int rotation) {
        player.sendMessage(ChatColor.GREEN + "Placing schematic: " + ChatColor.YELLOW + schematicName);
        player.sendMessage(ChatColor.GRAY + "Rotation: " + rotation + "° | Location: " + 
            location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ());
        
        // Place the schematic
        plugin.getSchematicManager().placeSchematic(schematicName, location).thenAccept(success -> {
            if (success) {
                player.sendMessage(ChatColor.GREEN + "✓ Schematic placed successfully!");
            } else {
                player.sendMessage(ChatColor.RED + "✗ Failed to place schematic!");
            }
        });
    }

    private static void returnToPreview(Player player, ApexDungeons plugin, String schematicName, Location location, int rotation) {
        // Get the schematic and create a new preview
        SchematicData schematic = plugin.getSchematicManager().getSchematic(schematicName);
        if (schematic != null) {
            com.apexdungeons.schematics.SchematicPreview preview = 
                new com.apexdungeons.schematics.SchematicPreview(plugin, player, schematic, location);
            
            // Set the rotation
            for (int i = 0; i < (rotation / 90); i++) {
                preview.rotate();
            }
            
            // Register with input handler and start preview
            plugin.getPreviewInputHandler().registerPreview(player, preview);
            preview.startPreview();
            
            player.sendMessage(ChatColor.GREEN + "Returned to preview mode");
        } else {
            player.sendMessage(ChatColor.RED + "Schematic not found: " + schematicName);
        }
    }

    private static int countNonAirBlocks(SchematicData schematic) {
        int count = 0;
        Material[][][] blocks = schematic.getBlocks();
        
        for (Material[][] layer : blocks) {
            for (Material[] row : layer) {
                for (Material block : row) {
                    if (block != Material.AIR) {
                        count++;
                    }
                }
            }
        }
        
        return count;
    }

    private static String getComplexityLevel(int blockCount) {
        if (blockCount < 50) {
            return ChatColor.GREEN + "Simple";
        } else if (blockCount < 200) {
            return ChatColor.YELLOW + "Medium";
        } else if (blockCount < 500) {
            return ChatColor.GOLD + "Complex";
        } else {
            return ChatColor.RED + "Very Complex";
        }
    }
}
