# MythicMobs & ModelEngine Integration

ApexDungeons integrates with [MythicMobs](https://www.spigotmc.org/resources/mythicmobs.5702/) and
[ModelEngine](https://www.spigotmc.org/resources/modelengine-minecraft-custom-models.79477/) to provide truly
customisable creatures and bosses within your dungeons.  Integration is automatic – if either plugin is detected at
runtime the relevant adapters are enabled.

## Mob Adapter System

The plugin uses a `MobAdapter` interface to abstract how mobs are created.  Two built in adapters are provided:

| Adapter | Description |
| --- | --- |
| `VanillaAdapter` | Spawns standard Bukkit mobs using `EntityType`. |
| `MythicMobsAdapter` | When MythicMobs is present, mobs defined as `MM:<internalName>` will be spawned through the MythicMobs API.  This adapter falls back to the vanilla adapter for `VANILLA:<type>` entries. |

At runtime the plugin chooses the appropriate adapter according to the `mobs.adapter` setting in `config.yml`.  When
set to `AUTO` the MythicMobs adapter is selected only if the plugin is installed; otherwise the vanilla adapter
handles all spawns.

## Using MythicMobs in Configurations

To use a MythicMobs mob in your dungeon configuration files simply prefix the internal name with `MM:`.  For
example, the following boss definition spawns a MythicMobs entity called `ZombieKnight`:

```yaml
bosses:
  mm_zombie_knight:
    type: MM:ZombieKnight
    healthMultiplier: 1.5
    damageMultiplier: 1.2
    announce: true
```

Similarly in `mobs.yml` you can create a spawn pool referencing MythicMobs creatures:

```yaml
pools:
  crypt:
    - type: MM:GhostWarrior
      weight: 60
    - type: VANILLA:ZOMBIE
      weight: 40
```

If ModelEngine is installed alongside MythicMobs, any mobs that are bound to a ModelEngine model will automatically
use the proper 3D model when spawned through the MythicMobs API.

## Fallback Behaviour

When neither MythicMobs nor ModelEngine are available on your server the plugin quietly falls back to spawning
vanilla mobs.  There is no need to change your configuration files – Mythic entries will simply be ignored.  This
ensures that your dungeons remain playable even without third‑party plugins.