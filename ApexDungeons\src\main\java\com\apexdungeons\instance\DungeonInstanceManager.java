package com.apexdungeons.instance;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import org.bukkit.*;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Manages dungeon instances for multiple teams running the same dungeon simultaneously.
 */
public class DungeonInstanceManager {
    private final ApexDungeons plugin;
    private final Map<String, List<DungeonInstanceData>> activeInstances = new HashMap<>();
    private final Map<UUID, DungeonInstanceData> playerInstances = new HashMap<>();
    private final Map<String, Integer> instanceCounters = new HashMap<>();

    public DungeonInstanceManager(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    /**
     * Create a new instance of a dungeon for a team.
     */
    public CompletableFuture<DungeonInstanceData> createInstance(String templateName, List<Player> team) {
        CompletableFuture<DungeonInstanceData> future = new CompletableFuture<>();
        
        // Generate unique instance ID
        int instanceNumber = instanceCounters.getOrDefault(templateName, 0) + 1;
        instanceCounters.put(templateName, instanceNumber);
        String instanceId = templateName + "_instance_" + instanceNumber + "_" + System.currentTimeMillis();
        
        plugin.getLogger().info("Creating dungeon instance: " + instanceId + " for " + team.size() + " players");
        
        // Create instance world asynchronously
        plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
            try {
                // Copy template world to new instance world
                World templateWorld = getTemplateWorld(templateName);
                if (templateWorld == null) {
                    future.complete(null);
                    return;
                }
                
                // Create instance world
                World instanceWorld = createInstanceWorld(instanceId, templateWorld);
                if (instanceWorld == null) {
                    future.complete(null);
                    return;
                }
                
                // Create instance data
                DungeonInstanceData instanceData = new DungeonInstanceData(
                    instanceId, templateName, instanceWorld, team, System.currentTimeMillis()
                );
                
                // Register instance on main thread
                plugin.getServer().getScheduler().runTask(plugin, () -> {
                    registerInstance(instanceData);
                    future.complete(instanceData);
                });
                
            } catch (Exception e) {
                plugin.getLogger().severe("Failed to create dungeon instance: " + e.getMessage());
                e.printStackTrace();
                future.complete(null);
            }
        });
        
        return future;
    }

    /**
     * Get template world for a dungeon.
     */
    private World getTemplateWorld(String templateName) {
        // First try to get existing dungeon world
        World world = plugin.getWorldManager().getDungeonWorld(templateName);
        if (world != null) {
            return world;
        }
        
        // If not found, try to find any world with the template name
        for (World w : Bukkit.getWorlds()) {
            if (w.getName().contains(templateName)) {
                return w;
            }
        }
        
        plugin.getLogger().warning("Template world not found for: " + templateName);
        return null;
    }

    /**
     * Create instance world by copying template world.
     */
    private World createInstanceWorld(String instanceId, World templateWorld) {
        try {
            String templateWorldName = templateWorld.getName();
            String instanceWorldName = "instance_" + instanceId;
            
            // Get world folders
            File templateFolder = templateWorld.getWorldFolder();
            File instanceFolder = new File(Bukkit.getWorldContainer(), instanceWorldName);
            
            plugin.getLogger().info("Copying world from " + templateFolder.getName() + " to " + instanceFolder.getName());
            
            // Copy world folder
            copyWorldFolder(templateFolder.toPath(), instanceFolder.toPath());
            
            // Create world on main thread
            CompletableFuture<World> worldFuture = new CompletableFuture<>();
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                try {
                    WorldCreator creator = new WorldCreator(instanceWorldName);
                    creator.environment(templateWorld.getEnvironment());
                    creator.type(templateWorld.getWorldType());
                    creator.generateStructures(false);
                    
                    World instanceWorld = creator.createWorld();
                    if (instanceWorld != null) {
                        // Set world properties
                        instanceWorld.setDifficulty(templateWorld.getDifficulty());
                        instanceWorld.setSpawnFlags(false, false); // No monsters/animals spawning
                        instanceWorld.setKeepSpawnInMemory(false);
                        
                        plugin.getLogger().info("Created instance world: " + instanceWorldName);
                    }
                    worldFuture.complete(instanceWorld);
                } catch (Exception e) {
                    plugin.getLogger().severe("Failed to create instance world: " + e.getMessage());
                    worldFuture.complete(null);
                }
            });
            
            // Wait for world creation (with timeout)
            return worldFuture.get(30, java.util.concurrent.TimeUnit.SECONDS);
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to create instance world: " + e.getMessage());
            return null;
        }
    }

    /**
     * Copy world folder recursively.
     */
    private void copyWorldFolder(Path source, Path target) throws IOException {
        if (!Files.exists(source)) {
            throw new IOException("Source world folder does not exist: " + source);
        }
        
        Files.walk(source).forEach(sourcePath -> {
            try {
                Path targetPath = target.resolve(source.relativize(sourcePath));
                
                // Skip session.lock and uid.dat files
                String fileName = sourcePath.getFileName().toString();
                if (fileName.equals("session.lock") || fileName.equals("uid.dat")) {
                    return;
                }
                
                if (Files.isDirectory(sourcePath)) {
                    Files.createDirectories(targetPath);
                } else {
                    Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                }
            } catch (IOException e) {
                plugin.getLogger().warning("Failed to copy file: " + sourcePath + " - " + e.getMessage());
            }
        });
    }

    /**
     * Register a new instance.
     */
    private void registerInstance(DungeonInstanceData instanceData) {
        String templateName = instanceData.getTemplateName();
        
        // Add to active instances
        activeInstances.computeIfAbsent(templateName, k -> new ArrayList<>()).add(instanceData);
        
        // Map players to instance
        for (Player player : instanceData.getTeam()) {
            playerInstances.put(player.getUniqueId(), instanceData);
        }
        
        plugin.getLogger().info("Registered dungeon instance: " + instanceData.getInstanceId() + 
            " (Total instances for " + templateName + ": " + activeInstances.get(templateName).size() + ")");
    }

    /**
     * Get instance for a player.
     */
    public DungeonInstanceData getPlayerInstance(Player player) {
        return playerInstances.get(player.getUniqueId());
    }

    /**
     * Remove player from their current instance.
     */
    public void removePlayerFromInstance(Player player) {
        DungeonInstanceData instance = playerInstances.remove(player.getUniqueId());
        if (instance != null) {
            instance.removePlayer(player);
            
            // If instance is empty, clean it up
            if (instance.getTeam().isEmpty()) {
                cleanupInstance(instance);
            }
        }
    }

    /**
     * Cleanup an empty instance.
     */
    private void cleanupInstance(DungeonInstanceData instanceData) {
        String templateName = instanceData.getTemplateName();
        String instanceId = instanceData.getInstanceId();
        
        // Remove from active instances
        List<DungeonInstanceData> instances = activeInstances.get(templateName);
        if (instances != null) {
            instances.remove(instanceData);
            if (instances.isEmpty()) {
                activeInstances.remove(templateName);
            }
        }
        
        // Unload and delete world
        World instanceWorld = instanceData.getInstanceWorld();
        if (instanceWorld != null) {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                // Kick any remaining players
                for (Player player : instanceWorld.getPlayers()) {
                    Location mainSpawn = Bukkit.getWorlds().get(0).getSpawnLocation();
                    player.teleport(mainSpawn);
                    player.sendMessage(ChatColor.YELLOW + "Instance closed - returned to main world.");
                }
                
                // Unload world
                boolean unloaded = Bukkit.unloadWorld(instanceWorld, false);
                if (unloaded) {
                    plugin.getLogger().info("Unloaded instance world: " + instanceWorld.getName());
                    
                    // Delete world folder asynchronously
                    plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
                        try {
                            File worldFolder = instanceWorld.getWorldFolder();
                            deleteWorldFolder(worldFolder);
                            plugin.getLogger().info("Deleted instance world folder: " + worldFolder.getName());
                        } catch (Exception e) {
                            plugin.getLogger().warning("Failed to delete instance world folder: " + e.getMessage());
                        }
                    });
                } else {
                    plugin.getLogger().warning("Failed to unload instance world: " + instanceWorld.getName());
                }
            });
        }
        
        plugin.getLogger().info("Cleaned up dungeon instance: " + instanceId);
    }

    /**
     * Delete world folder recursively.
     */
    private void deleteWorldFolder(File folder) throws IOException {
        if (!folder.exists()) {
            return;
        }
        
        Files.walk(folder.toPath())
            .sorted(Comparator.reverseOrder())
            .map(Path::toFile)
            .forEach(File::delete);
    }

    /**
     * Get all active instances for a template.
     */
    public List<DungeonInstanceData> getActiveInstances(String templateName) {
        return activeInstances.getOrDefault(templateName, new ArrayList<>());
    }

    /**
     * Get total number of active instances.
     */
    public int getTotalActiveInstances() {
        return activeInstances.values().stream().mapToInt(List::size).sum();
    }

    /**
     * Shutdown all instances.
     */
    public void shutdown() {
        plugin.getLogger().info("Shutting down " + getTotalActiveInstances() + " active instances...");
        
        for (List<DungeonInstanceData> instances : activeInstances.values()) {
            for (DungeonInstanceData instance : new ArrayList<>(instances)) {
                cleanupInstance(instance);
            }
        }
        
        activeInstances.clear();
        playerInstances.clear();
        instanceCounters.clear();
    }
}
