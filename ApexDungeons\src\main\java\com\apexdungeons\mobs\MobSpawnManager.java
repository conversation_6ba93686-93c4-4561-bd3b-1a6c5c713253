package com.apexdungeons.mobs;

import com.apexdungeons.ApexDungeons;
import org.bukkit.*;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages mob spawn points and triggers spawning when players enter proximity.
 */
public class MobSpawnManager implements Listener {
    private final ApexDungeons plugin;
    private final Map<String, MobSpawnPoint> spawnPoints = new ConcurrentHashMap<>();
    private final Map<String, Long> lastSpawnTimes = new ConcurrentHashMap<>();
    private final Set<String> activeSpawns = ConcurrentHashMap.newKeySet();
    
    // Configuration
    private double defaultRadius = 6.0; // Default 6 blocks as specified
    private long defaultCooldown = 30000; // 30 seconds default
    private int defaultMaxMobs = 3; // Default max concurrent mobs
    
    public MobSpawnManager(ApexDungeons plugin) {
        this.plugin = plugin;
        loadSpawnPoints();

        // Register event listener
        Bukkit.getPluginManager().registerEvents(this, plugin);

        // Start cleanup task
        startCleanupTask();

        // Start visual feedback task
        startVisualFeedbackTask();
    }
    
    /**
     * Add a mob spawn point.
     */
    public void addMobSpawnPoint(Location location, String mobName, double radius) {
        String key = locationToKey(location);
        MobSpawnPoint spawnPoint = new MobSpawnPoint(location, mobName, radius, false);
        spawnPoints.put(key, spawnPoint);
        saveSpawnPoints();
        plugin.getLogger().info("Added mob spawn point: " + mobName + " at " + locationToString(location));
    }
    
    /**
     * Add a boss spawn point.
     */
    public void addBossSpawnPoint(Location location, String bossName, double radius) {
        String key = locationToKey(location);
        MobSpawnPoint spawnPoint = new MobSpawnPoint(location, bossName, radius, true);
        spawnPoints.put(key, spawnPoint);
        saveSpawnPoints();
        plugin.getLogger().info("Added boss spawn point: " + bossName + " at " + locationToString(location));
    }
    
    /**
     * Remove a spawn point at the given location.
     */
    public boolean removeSpawnPoint(Location location) {
        String key = locationToKey(location);
        MobSpawnPoint removed = spawnPoints.remove(key);
        if (removed != null) {
            saveSpawnPoints();
            plugin.getLogger().info("Removed spawn point at " + locationToString(location));
            return true;
        }
        return false;
    }
    
    /**
     * Get spawn point at location.
     */
    public MobSpawnPoint getSpawnPoint(Location location) {
        String key = locationToKey(location);
        return spawnPoints.get(key);
    }
    
    /**
     * Get all spawn points.
     */
    public Collection<MobSpawnPoint> getAllSpawnPoints() {
        return new ArrayList<>(spawnPoints.values());
    }
    
    /**
     * Handle player movement to check for spawn triggers.
     */
    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        Location to = event.getTo();

        if (to == null) return;

        // Check if player moved to a different block
        Location from = event.getFrom();
        if (from.getBlockX() == to.getBlockX() &&
            from.getBlockY() == to.getBlockY() &&
            from.getBlockZ() == to.getBlockZ()) {
            return; // Same block, no need to check
        }

        // Only check spawn points in dungeon worlds
        if (!plugin.getWorldManager().isDungeonWorld(to.getWorld())) {
            return;
        }

        // Debug: Log when checking spawn points
        if (spawnPoints.size() > 0) {
            // Only log occasionally to avoid spam
            if (System.currentTimeMillis() % 5000 < 100) { // Every ~5 seconds
                plugin.getLogger().info("Checking " + spawnPoints.size() + " spawn points for player " + player.getName() + " in world " + to.getWorld().getName());
            }
        }

        // Check all spawn points for proximity
        for (MobSpawnPoint spawnPoint : spawnPoints.values()) {
            if (!spawnPoint.getLocation().getWorld().equals(to.getWorld())) continue;

            double distance = spawnPoint.getLocation().distance(to);
            if (distance <= spawnPoint.getRadius()) {
                plugin.getLogger().info("Player " + player.getName() + " triggered spawn point at distance " + distance + " (radius: " + spawnPoint.getRadius() + ")");
                triggerSpawn(spawnPoint, player);
            }
        }
    }
    
    /**
     * Trigger mob spawning at a spawn point using enhanced features.
     */
    private void triggerSpawn(MobSpawnPoint spawnPoint, Player triggeringPlayer) {
        plugin.getLogger().info("Triggering spawn for " + spawnPoint.getMobName() + " at " + locationToString(spawnPoint.getLocation()));

        // Check if spawn point is active
        if (!spawnPoint.isActive()) {
            plugin.getLogger().info("Spawn point is inactive, skipping");
            return;
        }

        // Check cooldown using spawn point's own cooldown system
        if (spawnPoint.isOnCooldown()) {
            plugin.getLogger().info("Spawn point on cooldown (" + spawnPoint.getRemainingCooldownSeconds() + "s remaining), skipping");
            return;
        }

        // Check if at max capacity
        if (spawnPoint.isAtMaxCapacity()) {
            plugin.getLogger().info("Spawn point at max capacity (" + spawnPoint.getSpawnedMobs().size() + "/" + spawnPoint.getMaxConcurrentMobs() + "), skipping");
            return;
        }

        // Update last spawn time
        spawnPoint.setLastSpawnTime(System.currentTimeMillis());

        plugin.getLogger().info("Starting enhanced mob spawn process for " + spawnPoint.getMobName());

        // Spawn mobs using enhanced system
        spawnMobsEnhanced(spawnPoint, triggeringPlayer);
    }
    
    /**
     * Enhanced mob spawning with tracking and feedback.
     */
    private void spawnMobsEnhanced(MobSpawnPoint spawnPoint, Player triggeringPlayer) {
        Location spawnLoc = spawnPoint.getLocation().clone();
        String mobName = spawnPoint.getMobName();
        boolean isBoss = spawnPoint.isBoss();

        plugin.getLogger().info("SpawnMobs called for " + mobName + " (boss: " + isBoss + ") at " + locationToString(spawnLoc));

        // Check if mob adapter is available
        if (plugin.getMobAdapter() == null) {
            plugin.getLogger().severe("MobAdapter is null! Cannot spawn mobs.");
            return;
        }

        plugin.getLogger().info("Using mob adapter: " + plugin.getMobAdapter().getAdapterName());

        // Calculate how many mobs to spawn based on available capacity
        int availableSlots = spawnPoint.getMaxConcurrentMobs() - spawnPoint.getSpawnedMobs().size();
        int mobCount = isBoss ? 1 : Math.min(availableSlots, 1 + new Random().nextInt(Math.min(3, availableSlots)));

        plugin.getLogger().info("Attempting to spawn " + mobCount + " mobs (available slots: " + availableSlots + ")");

        int successfulSpawns = 0;

        for (int i = 0; i < mobCount; i++) {
            // Add some randomness to spawn location
            Location randomLoc = spawnLoc.clone().add(
                (Math.random() - 0.5) * 4, // ±2 blocks X
                0,
                (Math.random() - 0.5) * 4  // ±2 blocks Z
            );

            // Find safe spawn location
            randomLoc = findSafeSpawnLocation(randomLoc);

            plugin.getLogger().info("Attempting to spawn " + mobName + " at " + locationToString(randomLoc));

            // Ensure the spawn location is loaded and valid
            if (!randomLoc.getChunk().isLoaded()) {
                randomLoc.getChunk().load();
                plugin.getLogger().info("Loaded chunk for spawn location");
            }

            LivingEntity spawned;
            try {
                if (isBoss) {
                    spawned = plugin.getMobAdapter().spawnBoss(mobName, randomLoc);
                } else {
                    spawned = plugin.getMobAdapter().spawnMob(mobName, randomLoc);
                }

                if (spawned != null) {
                    successfulSpawns++;

                    // Track the spawned mob
                    spawnPoint.addSpawnedMob(spawned.getUniqueId());

                    plugin.getLogger().info("Successfully spawned " + (isBoss ? "boss " : "mob ") +
                        mobName + " at " + locationToString(randomLoc) +
                        " (triggered by " + triggeringPlayer.getName() + ")");

                    // Show spawn effect
                    randomLoc.getWorld().spawnParticle(Particle.EXPLOSION, randomLoc, 1);
                    randomLoc.getWorld().playSound(randomLoc, Sound.ENTITY_ZOMBIE_VILLAGER_CONVERTED, 1.0f, 0.8f);
                } else {
                    plugin.getLogger().warning("Failed to spawn " + mobName + " - adapter returned null");
                }
            } catch (Exception e) {
                plugin.getLogger().severe("Exception while spawning " + mobName + ": " + e.getMessage());
                e.printStackTrace();
            }
        }

        plugin.getLogger().info("Spawn process completed: " + successfulSpawns + "/" + mobCount + " mobs spawned successfully");

        // If no mobs spawned, try a fallback spawn
        if (successfulSpawns == 0) {
            plugin.getLogger().warning("No mobs spawned successfully, trying fallback spawn");
            tryFallbackSpawn(spawnLoc, triggeringPlayer);
        }
    }

    /**
     * Try to spawn a basic mob as fallback when normal spawning fails.
     */
    private void tryFallbackSpawn(Location location, Player triggeringPlayer) {
        try {
            plugin.getLogger().info("Attempting fallback spawn of ZOMBIE");
            LivingEntity fallback = plugin.getMobAdapter().spawnMob("ZOMBIE", location);
            if (fallback != null) {
                plugin.getLogger().info("Fallback spawn successful - spawned ZOMBIE");
            } else {
                plugin.getLogger().severe("Even fallback spawn failed!");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Fallback spawn failed with exception: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Find a safe location to spawn mobs (not inside blocks).
     */
    private Location findSafeSpawnLocation(Location location) {
        Location safe = location.clone();
        
        // Check if current location is safe
        if (safe.getBlock().getType().isAir() && 
            safe.clone().add(0, 1, 0).getBlock().getType().isAir()) {
            return safe;
        }
        
        // Try to find safe location nearby
        for (int y = -2; y <= 5; y++) {
            Location test = safe.clone().add(0, y, 0);
            if (test.getBlock().getType().isAir() && 
                test.clone().add(0, 1, 0).getBlock().getType().isAir() &&
                !test.clone().add(0, -1, 0).getBlock().getType().isAir()) {
                return test;
            }
        }
        
        return safe; // Return original if no safe location found
    }
    
    /**
     * Start cleanup task to remove old spawn data.
     */
    private void startCleanupTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                long now = System.currentTimeMillis();
                lastSpawnTimes.entrySet().removeIf(entry ->
                    now - entry.getValue() > defaultCooldown * 10); // Keep for 10x cooldown
            }
        }.runTaskTimerAsynchronously(plugin, 6000L, 6000L); // Every 5 minutes
    }
    
    /**
     * Convert location to string key.
     */
    private String locationToKey(Location location) {
        return location.getWorld().getName() + ":" + 
               location.getBlockX() + ":" + 
               location.getBlockY() + ":" + 
               location.getBlockZ();
    }
    
    /**
     * Convert location to readable string.
     */
    private String locationToString(Location location) {
        return String.format("%s(%d,%d,%d)", 
            location.getWorld().getName(),
            location.getBlockX(),
            location.getBlockY(),
            location.getBlockZ());
    }
    
    /**
     * Load spawn points from file.
     */
    private void loadSpawnPoints() {
        File file = new File(plugin.getDataFolder(), "mob_spawns.yml");
        if (!file.exists()) return;
        
        FileConfiguration config = YamlConfiguration.loadConfiguration(file);
        
        for (String key : config.getKeys(false)) {
            try {
                String worldName = config.getString(key + ".world");
                int x = config.getInt(key + ".x");
                int y = config.getInt(key + ".y");
                int z = config.getInt(key + ".z");
                String mobName = config.getString(key + ".mob");
                double radius = config.getDouble(key + ".radius", defaultRadius);
                boolean isBoss = config.getBoolean(key + ".boss", false);
                
                Location location = new Location(Bukkit.getWorld(worldName), x, y, z);
                MobSpawnPoint spawnPoint = new MobSpawnPoint(location, mobName, radius, isBoss);

                // Load enhanced properties with defaults
                long cooldownMs = config.getLong(key + ".cooldownMs", defaultCooldown);
                int maxConcurrentMobs = config.getInt(key + ".maxConcurrentMobs", defaultMaxMobs);
                boolean active = config.getBoolean(key + ".active", true);
                String displayName = config.getString(key + ".displayName", spawnPoint.getDisplayName());

                // Apply enhanced properties
                spawnPoint.setCooldownMs(cooldownMs);
                spawnPoint.setMaxConcurrentMobs(maxConcurrentMobs);
                spawnPoint.setActive(active);
                spawnPoint.setDisplayName(displayName);

                spawnPoints.put(key, spawnPoint);
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to load spawn point " + key + ": " + e.getMessage());
            }
        }
        
        plugin.getLogger().info("Loaded " + spawnPoints.size() + " mob spawn points");
    }
    
    /**
     * Save spawn points to file.
     */
    private void saveSpawnPoints() {
        File file = new File(plugin.getDataFolder(), "mob_spawns.yml");
        FileConfiguration config = new YamlConfiguration();
        
        for (Map.Entry<String, MobSpawnPoint> entry : spawnPoints.entrySet()) {
            String key = entry.getKey();
            MobSpawnPoint point = entry.getValue();
            Location loc = point.getLocation();
            
            // Basic properties
            config.set(key + ".world", loc.getWorld().getName());
            config.set(key + ".x", loc.getBlockX());
            config.set(key + ".y", loc.getBlockY());
            config.set(key + ".z", loc.getBlockZ());
            config.set(key + ".mob", point.getMobName());
            config.set(key + ".radius", point.getRadius());
            config.set(key + ".boss", point.isBoss());

            // Enhanced properties
            config.set(key + ".cooldownMs", point.getCooldownMs());
            config.set(key + ".maxConcurrentMobs", point.getMaxConcurrentMobs());
            config.set(key + ".active", point.isActive());
            config.set(key + ".displayName", point.getDisplayName());
        }
        
        try {
            config.save(file);
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save mob spawn points: " + e.getMessage());
        }
    }
    
    /**
     * Test mob spawning at a specific location (for debugging).
     */
    public boolean testMobSpawn(Location location, String mobName, boolean isBoss) {
        plugin.getLogger().info("Testing mob spawn: " + mobName + " at " + locationToString(location));

        try {
            LivingEntity spawned;
            if (isBoss) {
                spawned = plugin.getMobAdapter().spawnBoss(mobName, location);
            } else {
                spawned = plugin.getMobAdapter().spawnMob(mobName, location);
            }

            if (spawned != null) {
                plugin.getLogger().info("Test spawn successful: " + spawned.getType());
                return true;
            } else {
                plugin.getLogger().warning("Test spawn failed: adapter returned null");
                return false;
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Test spawn failed with exception: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Start the visual feedback task that shows spawn point information and particles.
     */
    private void startVisualFeedbackTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                for (MobSpawnPoint spawnPoint : spawnPoints.values()) {
                    showVisualFeedback(spawnPoint);
                }
            }
        }.runTaskTimer(plugin, 0L, 20L); // Run every second
    }

    /**
     * Show visual feedback for a spawn point including floating text and particles.
     */
    private void showVisualFeedback(MobSpawnPoint spawnPoint) {
        Location loc = spawnPoint.getLocation();
        World world = loc.getWorld();
        if (world == null) return;

        // Check if any players are nearby to show effects
        boolean playersNearby = world.getPlayers().stream()
            .anyMatch(p -> p.getLocation().distance(loc) <= 20); // Show effects within 20 blocks

        if (!playersNearby) return;

        // Show floating text display above spawn point
        Location textLoc = loc.clone().add(0, 2.5, 0);

        // Create floating text lines
        String[] lines = {
            ChatColor.YELLOW + spawnPoint.getDisplayName(),
            ChatColor.GRAY + "Range: " + ChatColor.WHITE + (int)spawnPoint.getRadius() + " blocks",
            getStatusColor(spawnPoint) + spawnPoint.getStatus()
        };

        // Show text to nearby players using chat messages
        for (Player player : world.getPlayers()) {
            if (player.getLocation().distance(loc) <= 15) {
                // Only show to players looking at the spawn point
                if (isPlayerLookingAt(player, loc)) {
                    for (String line : lines) {
                        player.sendMessage(line);
                    }
                }
            }
        }

        // Show detection radius particles
        showDetectionRadiusParticles(spawnPoint);

        // Show status-specific particles
        showStatusParticles(spawnPoint);
    }

    /**
     * Get color for status text based on spawn point state.
     */
    private ChatColor getStatusColor(MobSpawnPoint spawnPoint) {
        if (!spawnPoint.isActive()) {
            return ChatColor.DARK_GRAY;
        } else if (spawnPoint.isOnCooldown()) {
            return ChatColor.RED;
        } else if (spawnPoint.isAtMaxCapacity()) {
            return ChatColor.YELLOW;
        } else {
            return ChatColor.GREEN;
        }
    }

    /**
     * Show detection radius particles around spawn point.
     */
    private void showDetectionRadiusParticles(MobSpawnPoint spawnPoint) {
        Location center = spawnPoint.getLocation();
        World world = center.getWorld();
        if (world == null) return;

        double radius = spawnPoint.getRadius();
        Particle particle = spawnPoint.isBoss() ? Particle.FLAME : Particle.ENCHANT;

        // Create circle of particles at ground level
        for (int i = 0; i < 16; i++) {
            double angle = 2 * Math.PI * i / 16;
            double x = center.getX() + radius * Math.cos(angle);
            double z = center.getZ() + radius * Math.sin(angle);
            Location particleLoc = new Location(world, x, center.getY() + 0.1, z);

            world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }

    /**
     * Show status-specific particles at spawn point.
     */
    private void showStatusParticles(MobSpawnPoint spawnPoint) {
        Location loc = spawnPoint.getLocation().add(0, 1, 0);
        World world = loc.getWorld();
        if (world == null) return;

        if (!spawnPoint.isActive()) {
            world.spawnParticle(Particle.SMOKE, loc, 3, 0.2, 0.2, 0.2, 0);
        } else if (spawnPoint.isOnCooldown()) {
            world.spawnParticle(Particle.FLAME, loc, 5, 0.3, 0.3, 0.3, 0);
        } else if (spawnPoint.isAtMaxCapacity()) {
            world.spawnParticle(Particle.CRIT, loc, 3, 0.2, 0.2, 0.2, 0);
        } else {
            world.spawnParticle(Particle.HAPPY_VILLAGER, loc, 2, 0.1, 0.1, 0.1, 0);
        }
    }

    /**
     * Check if a player is looking at a specific location.
     */
    private boolean isPlayerLookingAt(Player player, Location target) {
        Location playerLoc = player.getEyeLocation();
        double distance = playerLoc.distance(target);

        if (distance > 15) return false; // Too far away

        // Get the direction vector from player to target
        org.bukkit.util.Vector toTarget = target.toVector().subtract(playerLoc.toVector()).normalize();
        org.bukkit.util.Vector playerDirection = playerLoc.getDirection().normalize();

        // Calculate the angle between the vectors
        double dot = toTarget.dot(playerDirection);
        double angle = Math.acos(dot);

        // Player is looking at target if angle is less than 30 degrees (0.52 radians)
        return angle < 0.52;
    }

    /**
     * Handle mob death to remove from tracking.
     */
    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();
        UUID entityId = entity.getUniqueId();

        // Remove from all spawn point tracking
        for (MobSpawnPoint spawnPoint : spawnPoints.values()) {
            spawnPoint.removeSpawnedMob(entityId);
        }
    }

    /**
     * Shutdown the manager.
     */
    public void shutdown() {
        saveSpawnPoints();
        spawnPoints.clear();
        lastSpawnTimes.clear();
        activeSpawns.clear();

        // Clean up detection entities
        for (MobSpawnPoint spawnPoint : spawnPoints.values()) {
            ArmorStand detectionEntity = spawnPoint.getDetectionEntity();
            if (detectionEntity != null && !detectionEntity.isDead()) {
                detectionEntity.remove();
            }
        }
    }
}
