package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * GUI for configuring player preferences and dungeon settings.
 */
public class SettingsGUI {
    private static final String GUI_NAME = ChatColor.YELLOW + "⚙ Settings & Preferences";
    private static final Map<UUID, PlayerSettings> playerSettings = new HashMap<>();

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory(player, 54, GUI_NAME);
        
        // Fill background
        fillBackground(inv);
        
        // Create settings options
        createSettingsOptions(inv, player, plugin);
        
        // Create navigation buttons
        createNavigationButtons(inv);
        
        player.openInventory(inv);
        
        // Register event listener
        registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.ORANGE_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        
        // Fill border
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
    }

    private static void createSettingsOptions(Inventory inv, Player player, ApexDungeons plugin) {
        PlayerSettings settings = getPlayerSettings(player);
        
        // Visual Effects Setting
        ItemStack visualEffects = new ItemStack(settings.visualEffects ? Material.LIME_DYE : Material.GRAY_DYE);
        ItemMeta visualMeta = visualEffects.getItemMeta();
        visualMeta.setDisplayName(ChatColor.LIGHT_PURPLE + "✨ Visual Effects");
        List<String> visualLore = new ArrayList<>();
        visualLore.add(ChatColor.GRAY + "Toggle particle effects and");
        visualLore.add(ChatColor.GRAY + "visual enhancements");
        visualLore.add("");
        visualLore.add(ChatColor.YELLOW + "Status: " + (settings.visualEffects ? 
            ChatColor.GREEN + "Enabled" : ChatColor.RED + "Disabled"));
        visualLore.add("");
        visualLore.add(ChatColor.GRAY + "Effects include:");
        visualLore.add(ChatColor.AQUA + "• Portal particles");
        visualLore.add(ChatColor.AQUA + "• Creation celebrations");
        visualLore.add(ChatColor.AQUA + "• Completion fireworks");
        visualLore.add("");
        visualLore.add(ChatColor.GREEN + "▶ Click to toggle!");
        visualMeta.setLore(visualLore);
        visualEffects.setItemMeta(visualMeta);
        inv.setItem(11, visualEffects);
        
        // Sound Effects Setting
        ItemStack soundEffects = new ItemStack(settings.soundEffects ? Material.NOTE_BLOCK : Material.BARRIER);
        ItemMeta soundMeta = soundEffects.getItemMeta();
        soundMeta.setDisplayName(ChatColor.AQUA + "🔊 Sound Effects");
        List<String> soundLore = new ArrayList<>();
        soundLore.add(ChatColor.GRAY + "Toggle sound effects and");
        soundLore.add(ChatColor.GRAY + "audio notifications");
        soundLore.add("");
        soundLore.add(ChatColor.YELLOW + "Status: " + (settings.soundEffects ? 
            ChatColor.GREEN + "Enabled" : ChatColor.RED + "Disabled"));
        soundLore.add("");
        soundLore.add(ChatColor.GRAY + "Sounds include:");
        soundLore.add(ChatColor.AQUA + "• Portal travel sounds");
        soundLore.add(ChatColor.AQUA + "• Dungeon completion chimes");
        soundLore.add(ChatColor.AQUA + "• Ambient dungeon atmosphere");
        soundLore.add("");
        soundLore.add(ChatColor.GREEN + "▶ Click to toggle!");
        soundMeta.setLore(soundLore);
        soundEffects.setItemMeta(soundMeta);
        inv.setItem(13, soundEffects);
        
        // Chat Notifications Setting
        ItemStack chatNotifications = new ItemStack(settings.chatNotifications ? Material.WRITABLE_BOOK : Material.BOOK);
        ItemMeta chatMeta = chatNotifications.getItemMeta();
        chatMeta.setDisplayName(ChatColor.GREEN + "💬 Chat Notifications");
        List<String> chatLore = new ArrayList<>();
        chatLore.add(ChatColor.GRAY + "Toggle chat messages and");
        chatLore.add(ChatColor.GRAY + "status notifications");
        chatLore.add("");
        chatLore.add(ChatColor.YELLOW + "Status: " + (settings.chatNotifications ? 
            ChatColor.GREEN + "Enabled" : ChatColor.RED + "Disabled"));
        chatLore.add("");
        chatLore.add(ChatColor.GRAY + "Notifications include:");
        chatLore.add(ChatColor.AQUA + "• Dungeon creation updates");
        chatLore.add(ChatColor.AQUA + "• Portal travel messages");
        chatLore.add(ChatColor.AQUA + "• Achievement unlocks");
        chatLore.add("");
        chatLore.add(ChatColor.GREEN + "▶ Click to toggle!");
        chatMeta.setLore(chatLore);
        chatNotifications.setItemMeta(chatMeta);
        inv.setItem(15, chatNotifications);
        
        // Auto-Teleport Setting
        ItemStack autoTeleport = new ItemStack(settings.autoTeleport ? Material.ENDER_PEARL : Material.ENDER_EYE);
        ItemMeta teleportMeta = autoTeleport.getItemMeta();
        teleportMeta.setDisplayName(ChatColor.DARK_PURPLE + "🌀 Auto-Teleport");
        List<String> teleportLore = new ArrayList<>();
        teleportLore.add(ChatColor.GRAY + "Automatically teleport to");
        teleportLore.add(ChatColor.GRAY + "dungeons after creation");
        teleportLore.add("");
        teleportLore.add(ChatColor.YELLOW + "Status: " + (settings.autoTeleport ? 
            ChatColor.GREEN + "Enabled" : ChatColor.RED + "Disabled"));
        teleportLore.add("");
        teleportLore.add(ChatColor.GRAY + "When enabled, you'll be");
        teleportLore.add(ChatColor.GRAY + "automatically transported to");
        teleportLore.add(ChatColor.GRAY + "your dungeon once it's ready");
        teleportLore.add("");
        teleportLore.add(ChatColor.GREEN + "▶ Click to toggle!");
        teleportMeta.setLore(teleportLore);
        autoTeleport.setItemMeta(teleportMeta);
        inv.setItem(29, autoTeleport);
        
        // Difficulty Preference
        ItemStack difficulty = new ItemStack(getDifficultyMaterial(settings.preferredDifficulty));
        ItemMeta difficultyMeta = difficulty.getItemMeta();
        difficultyMeta.setDisplayName(ChatColor.RED + "⚔ Preferred Difficulty");
        List<String> difficultyLore = new ArrayList<>();
        difficultyLore.add(ChatColor.GRAY + "Set your preferred dungeon");
        difficultyLore.add(ChatColor.GRAY + "difficulty level");
        difficultyLore.add("");
        difficultyLore.add(ChatColor.YELLOW + "Current: " + ChatColor.WHITE + settings.preferredDifficulty);
        difficultyLore.add("");
        difficultyLore.add(ChatColor.GRAY + "Available difficulties:");
        difficultyLore.add(ChatColor.GREEN + "• Easy" + ChatColor.GRAY + " - Relaxed exploration");
        difficultyLore.add(ChatColor.YELLOW + "• Medium" + ChatColor.GRAY + " - Balanced challenge");
        difficultyLore.add(ChatColor.GOLD + "• Hard" + ChatColor.GRAY + " - Serious challenge");
        difficultyLore.add(ChatColor.RED + "• Expert" + ChatColor.GRAY + " - Ultimate test");
        difficultyLore.add("");
        difficultyLore.add(ChatColor.GREEN + "▶ Click to cycle!");
        difficultyMeta.setLore(difficultyLore);
        difficulty.setItemMeta(difficultyMeta);
        inv.setItem(31, difficulty);
        
        // Theme Preference
        ItemStack theme = new ItemStack(getThemeMaterial(settings.preferredTheme));
        ItemMeta themeMeta = theme.getItemMeta();
        themeMeta.setDisplayName(ChatColor.GOLD + "🎨 Preferred Theme");
        List<String> themeLore = new ArrayList<>();
        themeLore.add(ChatColor.GRAY + "Set your favorite dungeon");
        themeLore.add(ChatColor.GRAY + "theme for quick access");
        themeLore.add("");
        themeLore.add(ChatColor.YELLOW + "Current: " + ChatColor.WHITE + settings.preferredTheme);
        themeLore.add("");
        themeLore.add(ChatColor.GRAY + "Available themes:");
        themeLore.add(ChatColor.GRAY + "• Castle" + ChatColor.GRAY + " - Medieval fortresses");
        themeLore.add(ChatColor.DARK_GRAY + "• Cave" + ChatColor.GRAY + " - Underground systems");
        themeLore.add(ChatColor.GOLD + "• Temple" + ChatColor.GRAY + " - Ancient ruins");
        themeLore.add("");
        themeLore.add(ChatColor.GREEN + "▶ Click to cycle!");
        themeMeta.setLore(themeLore);
        theme.setItemMeta(themeMeta);
        inv.setItem(33, theme);
    }

    private static void createNavigationButtons(Inventory inv) {
        // Back button
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(ChatColor.RED + "← Back to Main Menu");
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        
        // Reset to Defaults button
        ItemStack reset = new ItemStack(Material.BARRIER);
        ItemMeta resetMeta = reset.getItemMeta();
        resetMeta.setDisplayName(ChatColor.RED + "🔄 Reset to Defaults");
        List<String> resetLore = new ArrayList<>();
        resetLore.add(ChatColor.GRAY + "Reset all settings to");
        resetLore.add(ChatColor.GRAY + "their default values");
        resetLore.add("");
        resetLore.add(ChatColor.RED + "⚠ This cannot be undone!");
        resetMeta.setLore(resetLore);
        reset.setItemMeta(resetMeta);
        inv.setItem(49, reset);
        
        // Save Settings button
        ItemStack save = new ItemStack(Material.EMERALD);
        ItemMeta saveMeta = save.getItemMeta();
        saveMeta.setDisplayName(ChatColor.GREEN + "💾 Save Settings");
        List<String> saveLore = new ArrayList<>();
        saveLore.add(ChatColor.GRAY + "Save your current settings");
        saveLore.add(ChatColor.GRAY + "and apply changes");
        saveMeta.setLore(saveLore);
        save.setItemMeta(saveMeta);
        inv.setItem(53, save);
    }

    private static PlayerSettings getPlayerSettings(Player player) {
        return playerSettings.computeIfAbsent(player.getUniqueId(), k -> new PlayerSettings());
    }

    private static Material getDifficultyMaterial(String difficulty) {
        return switch (difficulty.toLowerCase()) {
            case "easy" -> Material.WOODEN_SWORD;
            case "medium" -> Material.IRON_SWORD;
            case "hard" -> Material.DIAMOND_SWORD;
            case "expert" -> Material.NETHERITE_SWORD;
            default -> Material.IRON_SWORD;
        };
    }

    private static Material getThemeMaterial(String theme) {
        return switch (theme.toLowerCase()) {
            case "castle" -> Material.STONE_BRICKS;
            case "cave" -> Material.COBBLESTONE;
            case "temple" -> Material.SANDSTONE;
            default -> Material.STONE_BRICKS;
        };
    }

    private static void registerEventListener(ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player) e.getWhoClicked();
                    PlayerSettings settings = getPlayerSettings(clicker);
                    int slot = e.getRawSlot();
                    
                    switch (slot) {
                        case 11: // Visual Effects
                            settings.visualEffects = !settings.visualEffects;
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            break;
                        case 13: // Sound Effects
                            settings.soundEffects = !settings.soundEffects;
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            break;
                        case 15: // Chat Notifications
                            settings.chatNotifications = !settings.chatNotifications;
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            break;
                        case 29: // Auto-Teleport
                            settings.autoTeleport = !settings.autoTeleport;
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            break;
                        case 31: // Difficulty Preference
                            settings.cycleDifficulty();
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            break;
                        case 33: // Theme Preference
                            settings.cycleTheme();
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            break;
                        case 45: // Back
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            break;
                        case 49: // Reset to Defaults
                            playerSettings.put(clicker.getUniqueId(), new PlayerSettings());
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            clicker.sendMessage(ChatColor.GREEN + "Settings reset to defaults!");
                            break;
                        case 53: // Save Settings
                            clicker.closeInventory();
                            clicker.sendMessage(ChatColor.GREEN + "Settings saved successfully!");
                            EnhancedMainGUI.open(clicker, plugin);
                            break;
                    }
                }
            }
        }, plugin);
    }

    /**
     * Player settings data class
     */
    public static class PlayerSettings {
        public boolean visualEffects = true;
        public boolean soundEffects = true;
        public boolean chatNotifications = true;
        public boolean autoTeleport = false;
        public String preferredDifficulty = "Medium";
        public String preferredTheme = "Castle";

        public void cycleDifficulty() {
            preferredDifficulty = switch (preferredDifficulty) {
                case "Easy" -> "Medium";
                case "Medium" -> "Hard";
                case "Hard" -> "Expert";
                case "Expert" -> "Easy";
                default -> "Medium";
            };
        }

        public void cycleTheme() {
            preferredTheme = switch (preferredTheme) {
                case "Castle" -> "Cave";
                case "Cave" -> "Temple";
                case "Temple" -> "Castle";
                default -> "Castle";
            };
        }
    }
}
