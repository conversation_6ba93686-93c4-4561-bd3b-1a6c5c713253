package com.apexdungeons.saved;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * Manages saved dungeon templates that can be loaded and reused.
 */
public class SavedDungeonManager {
    private final ApexDungeons plugin;
    private final File savedDungeonsFile;
    private final Map<String, SavedDungeon> savedDungeons = new HashMap<>();
    
    public SavedDungeonManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.savedDungeonsFile = new File(plugin.getDataFolder(), "saved_dungeons.yml");
        loadSavedDungeons();
    }
    
    /**
     * Save a dungeon as a reusable template.
     */
    public boolean saveDungeon(String dungeonName, String savedName, Player creator) {
        DungeonInstance dungeon = plugin.getDungeonManager().getDungeon(dungeonName);
        if (dungeon == null) {
            plugin.getLogger().warning("Cannot save dungeon: '" + dungeonName + "' not found");
            return false;
        }
        
        World dungeonWorld = dungeon.getOrigin().getWorld();
        if (dungeonWorld == null) {
            plugin.getLogger().warning("Cannot save dungeon: world is null");
            return false;
        }
        
        SavedDungeon savedDungeon = new SavedDungeon();
        savedDungeon.setName(savedName);
        savedDungeon.setOriginalName(dungeonName);
        savedDungeon.setCreator(creator.getName());
        savedDungeon.setCreatedTime(System.currentTimeMillis());
        savedDungeon.setDescription("Saved from dungeon: " + dungeonName);
        
        // Scan the world for important blocks
        scanDungeonWorld(dungeonWorld, savedDungeon);
        
        // Get spawn and exit locations
        Location spawnLoc = plugin.getWorldManager().getDungeonSpawnLocation(dungeonName);
        if (spawnLoc != null) {
            savedDungeon.setSpawnLocation(spawnLoc);
        }
        
        // Note: Exit location functionality not implemented yet
        // Location exitLoc = plugin.getWorldManager().getDungeonExitLocation(dungeonName);
        // if (exitLoc != null) {
        //     savedDungeon.setExitLocation(exitLoc);
        // }
        
        // Save dungeon settings
        Map<String, Object> settings = new HashMap<>();
        settings.put("max_players", 4); // Default max players
        settings.put("difficulty", "normal");
        settings.put("time_limit", 0);
        savedDungeon.setSettings(settings);
        
        // Store in memory and save to file
        savedDungeons.put(savedName, savedDungeon);
        return saveToDisk();
    }
    
    /**
     * Scan dungeon world for start blocks, end blocks, and spawn points.
     */
    private void scanDungeonWorld(World world, SavedDungeon savedDungeon) {
        List<Location> startBlocks = new ArrayList<>();
        List<Location> endBlocks = new ArrayList<>();
        List<Location> mobSpawns = new ArrayList<>();
        List<Location> bossSpawns = new ArrayList<>();
        List<Location> chestSpawns = new ArrayList<>();
        
        // Scan a reasonable area around spawn (200x200x200)
        Location center = new Location(world, 0, 64, 0);
        int radius = 100;
        
        for (int x = -radius; x <= radius; x += 5) {
            for (int y = 0; y <= 128; y += 5) {
                for (int z = -radius; z <= radius; z += 5) {
                    Location loc = center.clone().add(x, y, z);
                    
                    switch (loc.getBlock().getType()) {
                        case EMERALD_BLOCK:
                            startBlocks.add(loc.clone());
                            break;
                        case DIAMOND_BLOCK:
                            endBlocks.add(loc.clone());
                            break;
                        case SPAWNER:
                            mobSpawns.add(loc.clone());
                            break;
                        case CHEST:
                            chestSpawns.add(loc.clone());
                            break;
                        default:
                            // Ignore other block types
                            break;
                    }
                }
            }
        }
        
        savedDungeon.setStartBlocks(startBlocks);
        savedDungeon.setEndBlocks(endBlocks);
        savedDungeon.setMobSpawns(mobSpawns);
        savedDungeon.setBossSpawns(bossSpawns);
        savedDungeon.setChestSpawns(chestSpawns);
        
        plugin.getLogger().info("Scanned dungeon: " + startBlocks.size() + " start blocks, " + 
            endBlocks.size() + " end blocks, " + mobSpawns.size() + " mob spawns, " + 
            chestSpawns.size() + " chest spawns");
    }
    
    /**
     * Load a saved dungeon as a new active dungeon.
     */
    public boolean loadSavedDungeon(String savedName, String newDungeonName, Player player) {
        SavedDungeon savedDungeon = savedDungeons.get(savedName);
        if (savedDungeon == null) {
            return false;
        }
        
        // Check if dungeon already exists
        if (plugin.getDungeonManager().getDungeon(newDungeonName) != null) {
            return false;
        }
        
        // Create new dungeon world
        plugin.getWorldManager().createDungeonWorld(newDungeonName).thenAccept(world -> {
            if (world != null) {
                // Apply saved dungeon to the new world
                boolean success = applySavedDungeon(savedDungeon, newDungeonName, world);
                
                plugin.getServer().getScheduler().runTask(plugin, () -> {
                    if (success) {
                        player.sendMessage("§aSuccessfully loaded saved dungeon '" + savedName + "' as '" + newDungeonName + "'!");
                    } else {
                        player.sendMessage("§cFailed to apply saved dungeon.");
                    }
                });
            } else {
                plugin.getServer().getScheduler().runTask(plugin, () -> {
                    player.sendMessage("§cFailed to create dungeon world.");
                });
            }
        });
        
        return true;
    }
    
    /**
     * Apply saved dungeon data to a new world.
     */
    private boolean applySavedDungeon(SavedDungeon savedDungeon, String newDungeonName, World targetWorld) {
        try {
            // Create dungeon instance
            Location origin = new Location(targetWorld, 0, 64, 0);
            DungeonInstance newDungeon = new DungeonInstance(plugin, newDungeonName, targetWorld, origin, 1);
            
            // Place start and end blocks
            for (Location startLoc : savedDungeon.getStartBlocks()) {
                Location newLoc = new Location(targetWorld, startLoc.getX(), startLoc.getY(), startLoc.getZ());
                newLoc.getBlock().setType(org.bukkit.Material.EMERALD_BLOCK);
            }
            
            for (Location endLoc : savedDungeon.getEndBlocks()) {
                Location newLoc = new Location(targetWorld, endLoc.getX(), endLoc.getY(), endLoc.getZ());
                newLoc.getBlock().setType(org.bukkit.Material.DIAMOND_BLOCK);
            }
            
            // Set spawn and exit locations (functionality not implemented yet)
            // if (savedDungeon.getSpawnLocation() != null) {
            //     Location spawnLoc = savedDungeon.getSpawnLocation();
            //     Location newSpawnLoc = new Location(targetWorld, spawnLoc.getX(), spawnLoc.getY(), spawnLoc.getZ());
            //     plugin.getWorldManager().setDungeonSpawnLocation(newDungeonName, newSpawnLoc);
            // }
            //
            // if (savedDungeon.getExitLocation() != null) {
            //     Location exitLoc = savedDungeon.getExitLocation();
            //     Location newExitLoc = new Location(targetWorld, exitLoc.getX(), exitLoc.getY(), exitLoc.getZ());
            //     plugin.getWorldManager().setDungeonExitLocation(newDungeonName, newExitLoc);
            // }
            
            // Register the dungeon
            plugin.getDungeonManager().addDungeon(newDungeon);
            plugin.getWorldManager().registerDungeonWorld(newDungeonName, targetWorld);
            
            plugin.getLogger().info("Applied saved dungeon '" + savedDungeon.getName() + "' to create '" + newDungeonName + "'");
            return true;
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to apply saved dungeon: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Get all saved dungeons.
     */
    public Map<String, SavedDungeon> getSavedDungeons() {
        return new HashMap<>(savedDungeons);
    }
    
    /**
     * Get saved dungeon by name.
     */
    public SavedDungeon getSavedDungeon(String name) {
        return savedDungeons.get(name);
    }
    
    /**
     * Delete a saved dungeon.
     */
    public boolean deleteSavedDungeon(String name) {
        if (savedDungeons.remove(name) != null) {
            return saveToDisk();
        }
        return false;
    }
    
    /**
     * Check if a saved dungeon exists.
     */
    public boolean exists(String name) {
        return savedDungeons.containsKey(name);
    }
    
    /**
     * Load saved dungeons from disk.
     */
    private void loadSavedDungeons() {
        if (!savedDungeonsFile.exists()) {
            return;
        }
        
        FileConfiguration config = YamlConfiguration.loadConfiguration(savedDungeonsFile);
        
        for (String key : config.getKeys(false)) {
            try {
                SavedDungeon savedDungeon = SavedDungeon.fromConfig(config.getConfigurationSection(key));
                savedDungeons.put(key, savedDungeon);
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to load saved dungeon '" + key + "': " + e.getMessage());
            }
        }
        
        plugin.getLogger().info("Loaded " + savedDungeons.size() + " saved dungeons");
    }
    
    /**
     * Save all saved dungeons to disk.
     */
    private boolean saveToDisk() {
        try {
            FileConfiguration config = new YamlConfiguration();
            
            for (Map.Entry<String, SavedDungeon> entry : savedDungeons.entrySet()) {
                entry.getValue().saveToConfig(config.createSection(entry.getKey()));
            }
            
            config.save(savedDungeonsFile);
            return true;
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save dungeons to disk: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Shutdown and save data.
     */
    public void shutdown() {
        saveToDisk();
        savedDungeons.clear();
    }
}
