package com.apexdungeons.gen;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.BoundingBox;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * Represents a single dungeon instance.  Holds references to the world,
 * origin position, active players and whether the dungeon is still being
 * generated.  Simple helper methods are provided for spawning bosses.
 */
public class DungeonInstance {
    private final ApexDungeons plugin;
    private final String name;
    private final String displayName;
    private final String creator;
    private final long creationTime;
    private final World world;
    private final Location origin;
    private final int roomCount;
    private boolean generating;
    private final Set<UUID> players = new HashSet<>();

    public DungeonInstance(ApexDungeons plugin, String name, World world, Location origin, int roomCount) {
        this(plugin, name, name, "Unknown", System.currentTimeMillis(), world, origin, roomCount);
    }

    public DungeonInstance(ApexDungeons plugin, String name, String displayName, String creator, long creationTime, World world, Location origin, int roomCount) {
        this.plugin = plugin;
        this.name = name;
        this.displayName = displayName;
        this.creator = creator;
        this.creationTime = creationTime;
        this.world = world;
        this.origin = origin;
        this.roomCount = roomCount;
        this.generating = true;
    }

    public String getName() {
        return name;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getCreator() {
        return creator;
    }

    public long getCreationTime() {
        return creationTime;
    }

    public World getWorld() {
        return world;
    }

    public Location getOrigin() {
        return origin;
    }

    public int getRoomCount() {
        return roomCount;
    }

    public boolean isGenerating() {
        return generating;
    }

    public void setGenerating(boolean generating) {
        this.generating = generating;
    }

    public void addPlayer(Player player) {
        players.add(player.getUniqueId());
    }

    public void removePlayer(Player player) {
        players.remove(player.getUniqueId());
    }

    public boolean containsPlayer(Player player) {
        return players.contains(player.getUniqueId());
    }

    public Set<UUID> getPlayers() {
        return new HashSet<>(players);
    }

    /**
     * Spawn a boss at the centre of the dungeon.  This implementation simply
     * spawns the default boss defined in bosses.yml at the origin location.
     */
    public void spawnBoss() {
        // Spawn a basic boss mob (simplified for now)
        // TODO: Implement boss spawning system
        plugin.getLogger().info("Boss spawning not yet implemented for dungeon: " + name);
    }
}