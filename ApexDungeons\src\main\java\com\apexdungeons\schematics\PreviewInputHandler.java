package com.apexdungeons.schematics;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.SchematicConfirmationGUI;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import java.util.*;

/**
 * Handles keyboard input for schematic preview controls.
 * Listens for chat messages that represent keyboard controls.
 */
public class PreviewInputHandler implements Listener {
    private final ApexDungeons plugin;
    private final Map<UUID, SchematicPreview> activePreviews = new HashMap<>();
    
    // Control mappings
    private static final Map<String, String> CONTROL_MAPPINGS = new HashMap<>();
    
    static {
        // Movement controls
        CONTROL_MAPPINGS.put("w", "north");
        CONTROL_MAPPINGS.put("a", "west");
        CONTROL_MAPPINGS.put("s", "south");
        CONTROL_MAPPINGS.put("d", "east");
        CONTROL_MAPPINGS.put("space", "up");
        CONTROL_MAPPINGS.put("shift", "down");
        
        // Rotation controls
        CONTROL_MAPPINGS.put("r", "rotate");
        CONTROL_MAPPINGS.put("q", "rotate_left");
        CONTROL_MAPPINGS.put("e", "rotate_right");
        
        // Confirmation controls
        CONTROL_MAPPINGS.put("enter", "confirm");
        CONTROL_MAPPINGS.put("escape", "cancel");
        CONTROL_MAPPINGS.put("esc", "cancel");
    }
    
    public PreviewInputHandler(ApexDungeons plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * Register a preview for input handling.
     */
    public void registerPreview(Player player, SchematicPreview preview) {
        activePreviews.put(player.getUniqueId(), preview);
        showControlInstructions(player);
    }
    
    /**
     * Unregister a preview from input handling.
     */
    public void unregisterPreview(Player player) {
        activePreviews.remove(player.getUniqueId());
    }
    
    /**
     * Handle chat input for preview controls.
     */
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        SchematicPreview preview = activePreviews.get(playerId);
        
        if (preview == null || !preview.isActive()) {
            return;
        }
        
        String message = event.getMessage().toLowerCase().trim();
        String action = CONTROL_MAPPINGS.get(message);
        
        if (action != null) {
            event.setCancelled(true);
            
            // Run on main thread
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                handlePreviewControl(player, preview, action);
            });
        }
    }
    
    /**
     * Handle preview control actions.
     */
    private void handlePreviewControl(Player player, SchematicPreview preview, String action) {
        if (!preview.isActive()) {
            return;
        }
        
        switch (action) {
            case "north":
            case "south":
            case "east":
            case "west":
            case "up":
            case "down":
                preview.move(action);
                break;
            case "rotate":
            case "rotate_right":
                preview.rotate();
                break;
            case "rotate_left":
                // Rotate 3 times for left rotation (270 degrees)
                preview.rotate();
                preview.rotate();
                preview.rotate();
                break;
            case "confirm":
                confirmPlacement(player, preview);
                break;
            case "cancel":
                cancelPreview(player, preview);
                break;
        }
    }
    
    /**
     * Confirm schematic placement.
     */
    private void confirmPlacement(Player player, SchematicPreview preview) {
        if (!preview.isActive()) {
            return;
        }

        // Get schematic info
        String schematicName = preview.getSchematic().getName();
        int blockCount = countNonAirBlocks(preview.getSchematic());

        // Stop the preview
        preview.stopPreview();
        unregisterPreview(player);

        // For large/complex schematics, show confirmation GUI
        if (blockCount > 200) {
            player.sendMessage(ChatColor.YELLOW + "Large schematic detected - showing confirmation dialog...");
            SchematicConfirmationGUI.open(player, plugin, schematicName, preview.getBaseLocation(), preview.getRotation());
        } else {
            // Small schematics can be placed directly
            player.sendMessage(ChatColor.GREEN + "Placing schematic: " + ChatColor.YELLOW + schematicName);
            player.sendMessage(ChatColor.GRAY + "Rotation: " + preview.getRotation() + "°");
            placeRotatedSchematic(player, preview);
        }
    }

    /**
     * Count non-air blocks in a schematic.
     */
    private int countNonAirBlocks(SchematicData schematic) {
        int count = 0;
        Material[][][] blocks = schematic.getBlocks();

        for (Material[][] layer : blocks) {
            for (Material[] row : layer) {
                for (Material block : row) {
                    if (block != Material.AIR) {
                        count++;
                    }
                }
            }
        }

        return count;
    }
    
    /**
     * Cancel the preview.
     */
    private void cancelPreview(Player player, SchematicPreview preview) {
        preview.stopPreview();
        unregisterPreview(player);
        player.sendMessage(ChatColor.YELLOW + "Schematic preview cancelled.");
    }
    
    /**
     * Place the schematic with the current rotation.
     */
    private void placeRotatedSchematic(Player player, SchematicPreview preview) {
        // Use the plugin's schematic manager to place the schematic
        // This is a simplified version - in a full implementation, you'd handle rotation properly
        plugin.getSchematicManager().placeSchematic(
            preview.getSchematic().getName(), 
            preview.getBaseLocation()
        ).thenAccept(success -> {
            if (success) {
                player.sendMessage(ChatColor.GREEN + "Schematic placed successfully!");
            } else {
                player.sendMessage(ChatColor.RED + "Failed to place schematic!");
            }
        });
    }
    
    /**
     * Show control instructions to the player.
     */
    private void showControlInstructions(Player player) {
        player.sendMessage(ChatColor.GOLD + "=== Enhanced Preview Controls ===");
        player.sendMessage(ChatColor.GREEN + "Movement:");
        player.sendMessage(ChatColor.AQUA + "  W/A/S/D" + ChatColor.WHITE + " - Move horizontally");
        player.sendMessage(ChatColor.AQUA + "  Space/Shift" + ChatColor.WHITE + " - Move up/down");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "Rotation:");
        player.sendMessage(ChatColor.AQUA + "  R" + ChatColor.WHITE + " - Rotate 90° clockwise");
        player.sendMessage(ChatColor.AQUA + "  Q/E" + ChatColor.WHITE + " - Rotate left/right");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "Actions:");
        player.sendMessage(ChatColor.AQUA + "  Enter" + ChatColor.WHITE + " - Confirm placement");
        player.sendMessage(ChatColor.AQUA + "  Escape" + ChatColor.WHITE + " - Cancel preview");
        player.sendMessage("");
        player.sendMessage(ChatColor.GRAY + "Type the control keys in chat to use them!");
        player.sendMessage(ChatColor.GRAY + "Or use left-click to confirm, shift+right-click to cancel");
    }
    
    /**
     * Handle player quit to clean up previews.
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        UUID playerId = event.getPlayer().getUniqueId();
        SchematicPreview preview = activePreviews.remove(playerId);
        if (preview != null) {
            preview.stopPreview();
        }
    }
    
    /**
     * Get active preview for a player.
     */
    public SchematicPreview getActivePreview(Player player) {
        return activePreviews.get(player.getUniqueId());
    }
    
    /**
     * Check if a player has an active preview.
     */
    public boolean hasActivePreview(Player player) {
        SchematicPreview preview = activePreviews.get(player.getUniqueId());
        return preview != null && preview.isActive();
    }
    
    /**
     * Get all active previews.
     */
    public Map<UUID, SchematicPreview> getActivePreviews() {
        return new HashMap<>(activePreviews);
    }
    
    /**
     * Shutdown and cleanup.
     */
    public void shutdown() {
        // Stop all active previews
        for (SchematicPreview preview : activePreviews.values()) {
            preview.stopPreview();
        }
        activePreviews.clear();
        
        plugin.getLogger().info("PreviewInputHandler shutdown complete.");
    }
}
