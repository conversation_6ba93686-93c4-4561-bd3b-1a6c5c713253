package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.schematics.SchematicData;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;

/**
 * Enhanced Schematic Selection GUI with favorites, search, and categorization.
 * Loads ALL schematics from the schematics folder.
 */
public class SchematicSelectionGUI {
    private static final String GUI_NAME = ChatColor.GOLD + "Master Builder Wand " + ChatColor.GRAY + "- Select Schematic";
    private static final Set<String> favoriteSchematicsByPlayer = new HashSet<>();
    private static int currentPage = 0;
    private static String searchFilter = "";
    
    public static void open(Player player, ApexDungeons plugin) {
        open(player, plugin, 0, "");
    }
    
    public static void open(Player player, ApexDungeons plugin, int page, String filter) {
        currentPage = page;
        searchFilter = filter;
        
        Inventory inv = Bukkit.createInventory(player, 54, GUI_NAME);
        
        // Fill background
        fillBackground(inv);
        
        // Populate schematics
        populateSchematics(inv, plugin, player, page, filter);
        
        // Add navigation and utility buttons
        addUtilityButtons(inv, plugin, player, page, filter);
        
        player.openInventory(inv);
        
        // Register event listener
        registerEventListener(plugin);
    }
    
    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);
        
        // Fill border slots
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
    }
    
    /**
     * Populate the GUI with available schematics.
     */
    private static void populateSchematics(Inventory inv, ApexDungeons plugin, Player player, int page, String filter) {
        Set<String> allSchematicNames = plugin.getSchematicManager().getLoadedSchematicNames();
        List<String> filteredNames = new ArrayList<>();
        
        // Apply search filter
        for (String name : allSchematicNames) {
            if (filter.isEmpty() || name.toLowerCase().contains(filter.toLowerCase())) {
                filteredNames.add(name);
            }
        }
        
        // Sort: favorites first, then alphabetically
        filteredNames.sort((a, b) -> {
            boolean aFav = isFavorite(player, a);
            boolean bFav = isFavorite(player, b);
            if (aFav && !bFav) return -1;
            if (!aFav && bFav) return 1;
            return a.compareToIgnoreCase(b);
        });
        
        // Available slots for schematics (excluding border and utility slots)
        int[] schematicSlots = {
            10, 11, 12, 13, 14, 15, 16,
            19, 20, 21, 22, 23, 24, 25,
            28, 29, 30, 31, 32, 33, 34,
            37, 38, 39, 40, 41, 42, 43
        };
        
        int startIndex = page * schematicSlots.length;
        for (int i = 0; i < schematicSlots.length && (startIndex + i) < filteredNames.size(); i++) {
            String schematicName = filteredNames.get(startIndex + i);
            ItemStack schematicItem = createSchematicItem(schematicName, plugin, player);
            inv.setItem(schematicSlots[i], schematicItem);
        }
    }
    
    /**
     * Create an item representing a schematic.
     */
    private static ItemStack createSchematicItem(String schematicName, ApexDungeons plugin, Player player) {
        SchematicData schematic = plugin.getSchematicManager().getSchematic(schematicName);
        
        // Smart material selection based on schematic name
        Material material = getSchematicMaterial(schematicName);
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        boolean isFavorite = isFavorite(player, schematicName);
        String displayName = (isFavorite ? ChatColor.GOLD + "⭐ " : ChatColor.AQUA) + schematicName;
        meta.setDisplayName(displayName);
        
        List<String> lore = new ArrayList<>();
        if (schematic != null) {
            lore.add(ChatColor.GRAY + "Dimensions: " + ChatColor.WHITE + 
                schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getDepth());
            lore.add(ChatColor.GRAY + "Blocks: " + ChatColor.WHITE + (schematic.getWidth() * schematic.getHeight() * schematic.getDepth()));
        }
        lore.add("");
        if (isFavorite) {
            lore.add(ChatColor.GOLD + "⭐ Favorited");
        }
        lore.add(ChatColor.GREEN + "▶ Left-click to select");
        lore.add(ChatColor.YELLOW + "▶ Right-click to " + (isFavorite ? "unfavorite" : "favorite"));
        lore.add(ChatColor.BLUE + "▶ Shift+click for instant preview");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }
    
    /**
     * Get appropriate material for schematic based on name.
     */
    private static Material getSchematicMaterial(String name) {
        String lowerName = name.toLowerCase();
        if (lowerName.contains("house") || lowerName.contains("home")) return Material.BRICK;
        if (lowerName.contains("castle") || lowerName.contains("fort")) return Material.STONE_BRICKS;
        if (lowerName.contains("tower")) return Material.COBBLESTONE;
        if (lowerName.contains("bridge")) return Material.OAK_PLANKS;
        if (lowerName.contains("ship") || lowerName.contains("boat")) return Material.DARK_OAK_PLANKS;
        if (lowerName.contains("tree")) return Material.OAK_LOG;
        if (lowerName.contains("farm")) return Material.HAY_BLOCK;
        if (lowerName.contains("mine") || lowerName.contains("cave")) return Material.COAL_ORE;
        if (lowerName.contains("temple") || lowerName.contains("church")) return Material.SANDSTONE;
        if (lowerName.contains("modern")) return Material.QUARTZ_BLOCK;
        return Material.STRUCTURE_BLOCK; // Default
    }
    
    /**
     * Add utility buttons for navigation and features.
     */
    private static void addUtilityButtons(Inventory inv, ApexDungeons plugin, Player player, int page, String filter) {
        // Refresh button
        ItemStack refresh = new ItemStack(Material.EMERALD);
        ItemMeta refreshMeta = refresh.getItemMeta();
        refreshMeta.setDisplayName(ChatColor.GREEN + "🔄 Refresh Schematics");
        List<String> refreshLore = new ArrayList<>();
        refreshLore.add(ChatColor.GRAY + "Reload all schematics from folder");
        refreshLore.add(ChatColor.YELLOW + "Total: " + ChatColor.WHITE + plugin.getSchematicManager().getLoadedSchematicNames().size() + " schematics");
        refreshMeta.setLore(refreshLore);
        refresh.setItemMeta(refreshMeta);
        inv.setItem(45, refresh);
        
        // Search button
        ItemStack search = new ItemStack(Material.COMPASS);
        ItemMeta searchMeta = search.getItemMeta();
        searchMeta.setDisplayName(ChatColor.YELLOW + "🔍 Search Filter");
        List<String> searchLore = new ArrayList<>();
        if (!filter.isEmpty()) {
            searchLore.add(ChatColor.GRAY + "Current filter: " + ChatColor.WHITE + filter);
            searchLore.add(ChatColor.GRAY + "Click to clear filter");
        } else {
            searchLore.add(ChatColor.GRAY + "No filter active");
            searchLore.add(ChatColor.GRAY + "Type in chat to search");
        }
        searchMeta.setLore(searchLore);
        search.setItemMeta(searchMeta);
        inv.setItem(46, search);
        
        // Favorites button
        ItemStack favorites = new ItemStack(Material.NETHER_STAR);
        ItemMeta favMeta = favorites.getItemMeta();
        favMeta.setDisplayName(ChatColor.GOLD + "⭐ Show Favorites Only");
        List<String> favLore = new ArrayList<>();
        favLore.add(ChatColor.GRAY + "View only your favorite schematics");
        favMeta.setLore(favLore);
        favorites.setItemMeta(favMeta);
        inv.setItem(47, favorites);
        
        // Help button
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(ChatColor.AQUA + "❓ Help & Controls");
        List<String> helpLore = new ArrayList<>();
        helpLore.add(ChatColor.YELLOW + "Master Builder Wand Guide:");
        helpLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Left-click: Select schematic");
        helpLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Right-click: Toggle favorite");
        helpLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Shift+click: Instant preview");
        helpLore.add("");
        helpLore.add(ChatColor.GREEN + "Made by Vexy");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(53, help);
        
        // Navigation arrows if needed
        Set<String> allNames = plugin.getSchematicManager().getLoadedSchematicNames();
        int totalPages = (int) Math.ceil(allNames.size() / 28.0);
        
        if (page > 0) {
            ItemStack prev = new ItemStack(Material.ARROW);
            ItemMeta prevMeta = prev.getItemMeta();
            prevMeta.setDisplayName(ChatColor.YELLOW + "← Previous Page");
            prev.setItemMeta(prevMeta);
            inv.setItem(48, prev);
        }
        
        if (page < totalPages - 1) {
            ItemStack next = new ItemStack(Material.ARROW);
            ItemMeta nextMeta = next.getItemMeta();
            nextMeta.setDisplayName(ChatColor.YELLOW + "Next Page →");
            next.setItemMeta(nextMeta);
            inv.setItem(50, next);
        }
        
        // Page indicator
        ItemStack pageInfo = new ItemStack(Material.PAPER);
        ItemMeta pageMeta = pageInfo.getItemMeta();
        pageMeta.setDisplayName(ChatColor.WHITE + "Page " + (page + 1) + " of " + Math.max(1, totalPages));
        pageInfo.setItemMeta(pageMeta);
        inv.setItem(49, pageInfo);
    }
    
    /**
     * Check if a schematic is favorited by a player.
     */
    private static boolean isFavorite(Player player, String schematicName) {
        return favoriteSchematicsByPlayer.contains(player.getUniqueId() + ":" + schematicName);
    }
    
    /**
     * Toggle favorite status for a schematic.
     */
    private static void toggleFavorite(Player player, String schematicName) {
        String key = player.getUniqueId() + ":" + schematicName;
        if (favoriteSchematicsByPlayer.contains(key)) {
            favoriteSchematicsByPlayer.remove(key);
            player.sendMessage(ChatColor.YELLOW + "Removed " + ChatColor.AQUA + schematicName + ChatColor.YELLOW + " from favorites");
        } else {
            favoriteSchematicsByPlayer.add(key);
            player.sendMessage(ChatColor.GOLD + "Added " + ChatColor.AQUA + schematicName + ChatColor.GOLD + " to favorites ⭐");
        }
    }
    
    private static void registerEventListener(ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (!e.getView().getTitle().equals(GUI_NAME) || !(e.getWhoClicked() instanceof Player)) return;
                
                e.setCancelled(true);
                Player player = (Player) e.getWhoClicked();
                int slot = e.getRawSlot();
                ItemStack clicked = e.getCurrentItem();
                
                if (clicked == null || !clicked.hasItemMeta()) return;
                
                // Handle schematic selection
                if (slot >= 10 && slot <= 43 && !isBorderSlot(slot)) {
                    String schematicName = extractSchematicName(clicked.getItemMeta().getDisplayName());
                    
                    if (e.getClick() == ClickType.RIGHT) {
                        // Toggle favorite
                        toggleFavorite(player, schematicName);
                        open(player, plugin, currentPage, searchFilter); // Refresh GUI
                    } else if (e.getClick() == ClickType.SHIFT_LEFT) {
                        // Instant preview
                        player.closeInventory();
                        plugin.getMasterBuilderWand().setSelectedSchematic(player, schematicName);
                        player.sendMessage(ChatColor.GREEN + "✓ Selected and ready for preview: " + ChatColor.AQUA + schematicName);
                    } else {
                        // Select schematic
                        player.closeInventory();
                        plugin.getMasterBuilderWand().setSelectedSchematic(player, schematicName);
                    }
                }
                
                // Handle utility buttons
                switch (slot) {
                    case 45: // Refresh
                        plugin.getSchematicManager().loadSchematics();
                        open(player, plugin, currentPage, searchFilter);
                        player.sendMessage(ChatColor.GREEN + "✓ Schematics refreshed!");
                        break;
                    case 46: // Search
                        player.closeInventory();
                        player.sendMessage(ChatColor.YELLOW + "Type a search term in chat (or 'cancel' to abort):");
                        // TODO: Implement chat listener for search
                        break;
                    case 47: // Favorites only
                        // TODO: Implement favorites filter
                        break;
                    case 48: // Previous page
                        if (currentPage > 0) {
                            open(player, plugin, currentPage - 1, searchFilter);
                        }
                        break;
                    case 50: // Next page
                        open(player, plugin, currentPage + 1, searchFilter);
                        break;
                }
            }
        }, plugin);
    }
    
    private static boolean isBorderSlot(int slot) {
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int border : borderSlots) {
            if (slot == border) return true;
        }
        return false;
    }
    
    private static String extractSchematicName(String displayName) {
        // Remove color codes and star prefix
        return ChatColor.stripColor(displayName).replace("⭐ ", "");
    }
}
