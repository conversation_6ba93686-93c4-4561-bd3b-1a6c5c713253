package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * GUI for detailed dungeon management options including teleportation, deletion,
 * player management, and dungeon information.
 */
public class DungeonOptionsGUI {
    private static final String GUI_NAME = ChatColor.DARK_GREEN + "🏰 Dungeon Options";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MMM dd, yyyy HH:mm");

    public static void open(Player player, ApexDungeons plugin, DungeonInstance dungeon) {
        Inventory inv = Bukkit.createInventory(player, 54, GUI_NAME);
        
        // Fill background
        fillBackground(inv);
        
        // Create dungeon info display
        createDungeonInfo(inv, dungeon);
        
        // Create action buttons
        createActionButtons(inv, player, plugin, dungeon);
        
        // Create navigation buttons
        createNavigationButtons(inv, plugin);
        
        player.openInventory(inv);
        
        // Register event listener
        registerEventListener(plugin, dungeon);
    }

    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.GREEN_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        
        // Fill border
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
    }

    private static void createDungeonInfo(Inventory inv, DungeonInstance dungeon) {
        // Main dungeon info
        ItemStack info = new ItemStack(Material.FILLED_MAP);
        ItemMeta infoMeta = info.getItemMeta();
        infoMeta.setDisplayName(ChatColor.GOLD + "📋 " + dungeon.getDisplayName());
        List<String> infoLore = new ArrayList<>();
        infoLore.add(ChatColor.GRAY + "━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        infoLore.add(ChatColor.YELLOW + "Internal Name: " + ChatColor.WHITE + dungeon.getName());
        infoLore.add(ChatColor.YELLOW + "Creator: " + ChatColor.WHITE + dungeon.getCreator());
        infoLore.add(ChatColor.YELLOW + "World: " + ChatColor.WHITE + dungeon.getWorld().getName());
        infoLore.add(ChatColor.YELLOW + "Rooms: " + ChatColor.WHITE + dungeon.getRoomCount());
        infoLore.add(ChatColor.YELLOW + "Created: " + ChatColor.WHITE + DATE_FORMAT.format(new Date(dungeon.getCreationTime())));
        
        // Status
        if (dungeon.isGenerating()) {
            infoLore.add(ChatColor.YELLOW + "Status: " + ChatColor.GOLD + "⚠ Generating...");
        } else {
            infoLore.add(ChatColor.YELLOW + "Status: " + ChatColor.GREEN + "✓ Ready");
        }
        
        // Location info
        Location origin = dungeon.getOrigin();
        infoLore.add(ChatColor.YELLOW + "Origin: " + ChatColor.WHITE + 
            origin.getBlockX() + ", " + origin.getBlockY() + ", " + origin.getBlockZ());
        
        infoLore.add(ChatColor.GRAY + "━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        infoMeta.setLore(infoLore);
        info.setItemMeta(infoMeta);
        inv.setItem(13, info);
        
        // Player list
        ItemStack players = new ItemStack(Material.PLAYER_HEAD);
        ItemMeta playersMeta = players.getItemMeta();
        playersMeta.setDisplayName(ChatColor.AQUA + "👥 Active Players");
        List<String> playersLore = new ArrayList<>();
        playersLore.add(ChatColor.GRAY + "Players currently in this dungeon");
        playersLore.add("");
        
        if (dungeon.getPlayers().isEmpty()) {
            playersLore.add(ChatColor.GRAY + "No players currently online");
        } else {
            playersLore.add(ChatColor.YELLOW + "Online Players:");
            for (UUID playerId : dungeon.getPlayers()) {
                Player onlinePlayer = Bukkit.getPlayer(playerId);
                if (onlinePlayer != null) {
                    playersLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + onlinePlayer.getName());
                }
            }
        }
        
        playersLore.add("");
        playersLore.add(ChatColor.YELLOW + "Total: " + ChatColor.WHITE + dungeon.getPlayers().size() + " players");
        playersMeta.setLore(playersLore);
        players.setItemMeta(playersMeta);
        inv.setItem(31, players);
    }

    private static void createActionButtons(Inventory inv, Player player, ApexDungeons plugin, DungeonInstance dungeon) {
        boolean isOwner = dungeon.getCreator().equals(player.getName());
        boolean isAdmin = player.hasPermission("apexdungeons.admin");
        
        // Teleport button
        ItemStack teleport = new ItemStack(Material.ENDER_PEARL);
        ItemMeta teleportMeta = teleport.getItemMeta();
        teleportMeta.setDisplayName(ChatColor.GREEN + "🌀 Teleport to Dungeon");
        List<String> teleportLore = new ArrayList<>();
        teleportLore.add(ChatColor.GRAY + "Travel to this dungeon instantly");
        if (dungeon.isGenerating()) {
            teleportLore.add("");
            teleportLore.add(ChatColor.RED + "⚠ Dungeon is still generating!");
            teleportLore.add(ChatColor.GRAY + "Please wait for completion");
        } else {
            teleportLore.add("");
            teleportLore.add(ChatColor.GREEN + "▶ Click to teleport!");
        }
        teleportMeta.setLore(teleportLore);
        teleport.setItemMeta(teleportMeta);
        inv.setItem(19, teleport);
        
        // Portal Management button
        ItemStack portals = new ItemStack(Material.NETHER_PORTAL);
        ItemMeta portalsMeta = portals.getItemMeta();
        portalsMeta.setDisplayName(ChatColor.LIGHT_PURPLE + "🌌 Portal Management");
        List<String> portalsLore = new ArrayList<>();
        portalsLore.add(ChatColor.GRAY + "Manage portals for this dungeon");
        portalsLore.add("");
        portalsLore.add(ChatColor.AQUA + "• View existing portals");
        portalsLore.add(ChatColor.AQUA + "• Create new portals");
        portalsLore.add(ChatColor.AQUA + "• Remove old portals");
        portalsLore.add("");
        portalsLore.add(ChatColor.GREEN + "▶ Click to manage!");
        portalsMeta.setLore(portalsLore);
        portals.setItemMeta(portalsMeta);
        inv.setItem(21, portals);
        
        // Player Management button (owner/admin only)
        if (isOwner || isAdmin) {
            ItemStack playerMgmt = new ItemStack(Material.IRON_SWORD);
            ItemMeta playerMeta = playerMgmt.getItemMeta();
            playerMeta.setDisplayName(ChatColor.YELLOW + "⚔ Player Management");
            List<String> playerLore = new ArrayList<>();
            playerLore.add(ChatColor.GRAY + "Manage players in this dungeon");
            playerLore.add("");
            playerLore.add(ChatColor.AQUA + "• Kick players");
            playerLore.add(ChatColor.AQUA + "• Ban/unban players");
            playerLore.add(ChatColor.AQUA + "• Set permissions");
            playerLore.add("");
            playerLore.add(ChatColor.GREEN + "▶ Click to manage!");
            playerMeta.setLore(playerLore);
            playerMgmt.setItemMeta(playerMeta);
            inv.setItem(23, playerMgmt);
        }
        
        // Settings button (owner/admin only)
        if (isOwner || isAdmin) {
            ItemStack settings = new ItemStack(Material.REDSTONE);
            ItemMeta settingsMeta = settings.getItemMeta();
            settingsMeta.setDisplayName(ChatColor.GOLD + "⚙ Dungeon Settings");
            List<String> settingsLore = new ArrayList<>();
            settingsLore.add(ChatColor.GRAY + "Configure dungeon properties");
            settingsLore.add("");
            settingsLore.add(ChatColor.AQUA + "• Rename dungeon");
            settingsLore.add(ChatColor.AQUA + "• Change permissions");
            settingsLore.add(ChatColor.AQUA + "• Modify spawn point");
            settingsLore.add("");
            settingsLore.add(ChatColor.GREEN + "▶ Click to configure!");
            settingsMeta.setLore(settingsLore);
            settings.setItemMeta(settingsMeta);
            inv.setItem(25, settings);
        }
        
        // Statistics button
        ItemStack stats = new ItemStack(Material.CLOCK);
        ItemMeta statsMeta = stats.getItemMeta();
        statsMeta.setDisplayName(ChatColor.AQUA + "📊 Dungeon Statistics");
        List<String> statsLore = new ArrayList<>();
        statsLore.add(ChatColor.GRAY + "View detailed dungeon statistics");
        statsLore.add("");
        statsLore.add(ChatColor.YELLOW + "Statistics include:");
        statsLore.add(ChatColor.AQUA + "• Total visitors");
        statsLore.add(ChatColor.AQUA + "• Average session time");
        statsLore.add(ChatColor.AQUA + "• Most active times");
        statsLore.add(ChatColor.AQUA + "• Performance metrics");
        statsLore.add("");
        statsLore.add(ChatColor.GREEN + "▶ Click to view!");
        statsMeta.setLore(statsLore);
        stats.setItemMeta(statsMeta);
        inv.setItem(37, stats);
        
        // Export/Backup button (owner/admin only)
        if (isOwner || isAdmin) {
            ItemStack backup = new ItemStack(Material.CHEST);
            ItemMeta backupMeta = backup.getItemMeta();
            backupMeta.setDisplayName(ChatColor.BLUE + "💾 Backup & Export");
            List<String> backupLore = new ArrayList<>();
            backupLore.add(ChatColor.GRAY + "Create backups and exports");
            backupLore.add("");
            backupLore.add(ChatColor.AQUA + "• Save dungeon schematic");
            backupLore.add(ChatColor.AQUA + "• Export as template");
            backupLore.add(ChatColor.AQUA + "• Create world backup");
            backupLore.add("");
            backupLore.add(ChatColor.GREEN + "▶ Click to backup!");
            backupMeta.setLore(backupLore);
            backup.setItemMeta(backupMeta);
            inv.setItem(39, backup);
        }
        
        // Delete button (owner/admin only)
        if (isOwner || isAdmin) {
            ItemStack delete = new ItemStack(Material.BARRIER);
            ItemMeta deleteMeta = delete.getItemMeta();
            deleteMeta.setDisplayName(ChatColor.RED + "🗑 Delete Dungeon");
            List<String> deleteLore = new ArrayList<>();
            deleteLore.add(ChatColor.GRAY + "Permanently delete this dungeon");
            deleteLore.add("");
            deleteLore.add(ChatColor.RED + "⚠ WARNING: This action cannot be undone!");
            deleteLore.add(ChatColor.RED + "⚠ All data will be permanently lost!");
            deleteLore.add("");
            deleteLore.add(ChatColor.YELLOW + "This will:");
            deleteLore.add(ChatColor.AQUA + "• Remove the dungeon world");
            deleteLore.add(ChatColor.AQUA + "• Delete all portals");
            deleteLore.add(ChatColor.AQUA + "• Kick all players");
            deleteLore.add("");
            deleteLore.add(ChatColor.RED + "▶ Click to delete!");
            deleteMeta.setLore(deleteLore);
            delete.setItemMeta(deleteMeta);
            inv.setItem(41, delete);
        }
    }

    private static void createNavigationButtons(Inventory inv, ApexDungeons plugin) {
        // Back button
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(ChatColor.RED + "← Back to Dungeon List");
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        
        // Refresh button
        ItemStack refresh = new ItemStack(Material.LIME_DYE);
        ItemMeta refreshMeta = refresh.getItemMeta();
        refreshMeta.setDisplayName(ChatColor.GREEN + "🔄 Refresh Information");
        List<String> refreshLore = new ArrayList<>();
        refreshLore.add(ChatColor.GRAY + "Update dungeon information");
        refreshLore.add(ChatColor.GRAY + "with the latest data");
        refreshMeta.setLore(refreshLore);
        refresh.setItemMeta(refreshMeta);
        inv.setItem(53, refresh);
    }

    private static void registerEventListener(ApexDungeons plugin, DungeonInstance dungeon) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player) e.getWhoClicked();
                    int slot = e.getRawSlot();
                    
                    boolean isOwner = dungeon.getCreator().equals(clicker.getName());
                    boolean isAdmin = clicker.hasPermission("apexdungeons.admin");
                    
                    switch (slot) {
                        case 19: // Teleport
                            if (dungeon.isGenerating()) {
                                clicker.sendMessage(ChatColor.YELLOW + "Dungeon is still generating. Please wait...");
                                return;
                            }
                            
                            Location spawnLoc = plugin.getWorldManager().getDungeonSpawnLocation(dungeon.getName());
                            if (spawnLoc != null) {
                                clicker.closeInventory();
                                plugin.getEffectsManager().playDungeonEntryEffects(clicker, dungeon);
                                clicker.teleport(spawnLoc);
                                dungeon.addPlayer(clicker);
                            } else {
                                clicker.sendMessage(ChatColor.RED + "Failed to find dungeon spawn location!");
                            }
                            break;
                            
                        case 21: // Portal Management
                            clicker.closeInventory();
                            // TODO: Implement portal management GUI
                            clicker.sendMessage(ChatColor.YELLOW + "Portal management coming soon!");
                            break;
                            
                        case 23: // Player Management
                            if (isOwner || isAdmin) {
                                clicker.closeInventory();
                                // TODO: Implement player management GUI
                                clicker.sendMessage(ChatColor.YELLOW + "Player management coming soon!");
                            }
                            break;
                            
                        case 25: // Settings
                            if (isOwner || isAdmin) {
                                clicker.closeInventory();
                                // TODO: Implement dungeon settings GUI
                                clicker.sendMessage(ChatColor.YELLOW + "Dungeon settings coming soon!");
                            }
                            break;
                            
                        case 37: // Statistics
                            clicker.closeInventory();
                            // TODO: Implement dungeon statistics GUI
                            clicker.sendMessage(ChatColor.YELLOW + "Dungeon statistics coming soon!");
                            break;
                            
                        case 39: // Backup
                            if (isOwner || isAdmin) {
                                clicker.closeInventory();
                                // TODO: Implement backup functionality
                                clicker.sendMessage(ChatColor.YELLOW + "Backup functionality coming soon!");
                            }
                            break;
                            
                        case 41: // Delete
                            if (isOwner || isAdmin) {
                                clicker.closeInventory();
                                DungeonDeleteConfirmGUI.open(clicker, plugin, dungeon);
                            }
                            break;
                            
                        case 45: // Back
                            clicker.closeInventory();
                            DungeonManagementGUI.open(clicker, plugin);
                            break;
                            
                        case 53: // Refresh
                            clicker.closeInventory();
                            DungeonOptionsGUI.open(clicker, plugin, dungeon);
                            clicker.sendMessage(ChatColor.GREEN + "Information refreshed!");
                            break;
                    }
                }
            }
        }, plugin);
    }
}
