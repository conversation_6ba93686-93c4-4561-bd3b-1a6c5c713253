package com.apexdungeons.party;

import org.bukkit.entity.Player;

import java.util.*;

/**
 * Represents a party of players for dungeon runs.
 */
public class Party {
    private final UUID partyId;
    private final Player leader;
    private final Set<Player> members;
    private final long createdTime;
    private final int maxSize;

    public Party(Player leader) {
        this.partyId = UUID.randomUUID();
        this.leader = leader;
        this.members = new LinkedHashSet<>();
        this.members.add(leader);
        this.createdTime = System.currentTimeMillis();
        this.maxSize = 4; // Maximum 4 players per party
    }

    /**
     * Add a player to the party.
     */
    public boolean addMember(Player player) {
        if (members.size() >= maxSize) {
            return false;
        }
        
        if (members.contains(player)) {
            return false;
        }
        
        return members.add(player);
    }

    /**
     * Remove a player from the party.
     */
    public boolean removeMember(Player player) {
        if (player.equals(leader)) {
            return false; // Cannot remove leader
        }
        
        return members.remove(player);
    }

    /**
     * Check if player is in the party.
     */
    public boolean hasMember(Player player) {
        return members.contains(player);
    }

    /**
     * Check if player is the leader.
     */
    public boolean isLeader(Player player) {
        return player.equals(leader);
    }

    /**
     * Get all members as a list.
     */
    public List<Player> getMembers() {
        return new ArrayList<>(members);
    }

    /**
     * Get members excluding the leader.
     */
    public List<Player> getMembersExcludingLeader() {
        List<Player> result = new ArrayList<>(members);
        result.remove(leader);
        return result;
    }

    /**
     * Check if party is full.
     */
    public boolean isFull() {
        return members.size() >= maxSize;
    }

    /**
     * Check if party is empty (only leader).
     */
    public boolean isEmpty() {
        return members.size() <= 1;
    }

    /**
     * Get party size.
     */
    public int getSize() {
        return members.size();
    }

    /**
     * Get available slots.
     */
    public int getAvailableSlots() {
        return maxSize - members.size();
    }

    /**
     * Disband the party.
     */
    public void disband() {
        members.clear();
    }

    /**
     * Transfer leadership to another member.
     */
    public boolean transferLeadership(Player newLeader) {
        if (!members.contains(newLeader) || newLeader.equals(leader)) {
            return false;
        }
        
        // Remove old leader and add new leader at the beginning
        members.remove(leader);
        members.remove(newLeader);
        
        Set<Player> newMembers = new LinkedHashSet<>();
        newMembers.add(newLeader);
        newMembers.add(leader);
        newMembers.addAll(members);
        
        members.clear();
        members.addAll(newMembers);
        
        return true;
    }

    /**
     * Get formatted party info.
     */
    public String getFormattedInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Party (").append(members.size()).append("/").append(maxSize).append("):\n");
        
        for (Player member : members) {
            if (member.equals(leader)) {
                info.append("👑 ").append(member.getName()).append(" (Leader)\n");
            } else {
                info.append("👤 ").append(member.getName()).append("\n");
            }
        }
        
        return info.toString();
    }

    // Getters
    public UUID getPartyId() { return partyId; }
    public Player getLeader() { return leader; }
    public long getCreatedTime() { return createdTime; }
    public int getMaxSize() { return maxSize; }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Party party = (Party) obj;
        return Objects.equals(partyId, party.partyId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(partyId);
    }

    @Override
    public String toString() {
        return "Party{" +
                "partyId=" + partyId +
                ", leader=" + leader.getName() +
                ", size=" + members.size() +
                ", maxSize=" + maxSize +
                '}';
    }
}
