package com.apexdungeons.help;

import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

/**
 * Comprehensive guide for the ApexDungeons room system.
 * Provides detailed instructions for dungeon builders.
 */
public class RoomSystemGuide {

    /**
     * Show the main room system overview.
     */
    public static void showMainGuide(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════════════");
        player.sendMessage(ChatColor.GOLD + "    🏰 APEX DUNGEONS ROOM SYSTEM 🏰");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════════════");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "📋 QUICK START GUIDE:");
        player.sendMessage(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Create a dungeon world: " + ChatColor.YELLOW + "/dgn create <name>");
        player.sendMessage(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Get building tools: " + ChatColor.YELLOW + "/dgn tools");
        player.sendMessage(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Build your start room with entrance");
        player.sendMessage(ChatColor.AQUA + "4. " + ChatColor.WHITE + "Connect rooms using the Room Connector");
        player.sendMessage(ChatColor.AQUA + "5. " + ChatColor.WHITE + "Place End Block in final room");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "📖 DETAILED GUIDES:");
        player.sendMessage(ChatColor.AQUA + "• " + ChatColor.YELLOW + "/dgn help rooms" + ChatColor.WHITE + " - Room types and design");
        player.sendMessage(ChatColor.AQUA + "• " + ChatColor.YELLOW + "/dgn help connections" + ChatColor.WHITE + " - Connecting rooms");
        player.sendMessage(ChatColor.AQUA + "• " + ChatColor.YELLOW + "/dgn help blocks" + ChatColor.WHITE + " - Start/End blocks");
        player.sendMessage(ChatColor.AQUA + "• " + ChatColor.YELLOW + "/dgn help schematics" + ChatColor.WHITE + " - Using schematics");
        player.sendMessage("");
        player.sendMessage(ChatColor.GRAY + "💡 Tip: Use " + ChatColor.YELLOW + "/dgn tools" + ChatColor.GRAY + " to access all building tools!");
    }

    /**
     * Show detailed room types and design guide.
     */
    public static void showRoomGuide(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════════════");
        player.sendMessage(ChatColor.GOLD + "        🏠 ROOM TYPES & DESIGN 🏠");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════════════");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🚪 START ROOM:");
        player.sendMessage(ChatColor.WHITE + "• The first room players enter");
        player.sendMessage(ChatColor.WHITE + "• Must contain a " + ChatColor.GREEN + "Start Block" + ChatColor.WHITE + " (Emerald Block)");
        player.sendMessage(ChatColor.WHITE + "• Should be welcoming and clearly show the path forward");
        player.sendMessage(ChatColor.WHITE + "• Recommended size: 7x7 or larger");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🏆 END ROOM:");
        player.sendMessage(ChatColor.WHITE + "• The final room where players complete the dungeon");
        player.sendMessage(ChatColor.WHITE + "• Must contain an " + ChatColor.AQUA + "End Block" + ChatColor.WHITE + " (Diamond Block)");
        player.sendMessage(ChatColor.WHITE + "• Often contains the main challenge or boss area");
        player.sendMessage(ChatColor.WHITE + "• Should feel rewarding and climactic");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🔗 CONNECTING ROOMS:");
        player.sendMessage(ChatColor.WHITE + "• Intermediate rooms between start and end");
        player.sendMessage(ChatColor.WHITE + "• Can contain puzzles, challenges, or story elements");
        player.sendMessage(ChatColor.WHITE + "• Use " + ChatColor.YELLOW + "Room Connector" + ChatColor.WHITE + " tool to link them");
        player.sendMessage(ChatColor.WHITE + "• Minimum 5x5 size recommended");
        player.sendMessage("");
        player.sendMessage(ChatColor.YELLOW + "💡 Design Tips:");
        player.sendMessage(ChatColor.GRAY + "• Leave 3-block high doorways for connections");
        player.sendMessage(ChatColor.GRAY + "• Use consistent themes and materials");
        player.sendMessage(ChatColor.GRAY + "• Add lighting to guide players");
        player.sendMessage(ChatColor.GRAY + "• Test your dungeon by playing through it!");
    }

    /**
     * Show room connection system guide.
     */
    public static void showConnectionGuide(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════════════");
        player.sendMessage(ChatColor.GOLD + "       🔗 ROOM CONNECTION SYSTEM 🔗");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════════════");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🔧 USING THE ROOM CONNECTOR:");
        player.sendMessage(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Get the tool: " + ChatColor.YELLOW + "/dgn tools " + ChatColor.WHITE + "→ Room Connector");
        player.sendMessage(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Left-click on the first room's wall");
        player.sendMessage(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Right-click on the second room's wall");
        player.sendMessage(ChatColor.AQUA + "4. " + ChatColor.WHITE + "Type in chat: " + ChatColor.YELLOW + "1" + ChatColor.WHITE + ", " + ChatColor.YELLOW + "2" + ChatColor.WHITE + ", or " + ChatColor.YELLOW + "3" + ChatColor.WHITE + " for connection type");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🚪 CONNECTION TYPES:");
        player.sendMessage(ChatColor.AQUA + "Type 1 - Simple Doorway:");
        player.sendMessage(ChatColor.WHITE + "• Creates a 3x3 opening between rooms");
        player.sendMessage(ChatColor.WHITE + "• Best for adjacent rooms");
        player.sendMessage(ChatColor.WHITE + "• Clean and simple connection");
        player.sendMessage("");
        player.sendMessage(ChatColor.AQUA + "Type 2 - Decorated Doorway:");
        player.sendMessage(ChatColor.WHITE + "• 3x3 opening with stone brick frame");
        player.sendMessage(ChatColor.WHITE + "• Adds architectural detail");
        player.sendMessage(ChatColor.WHITE + "• Good for important connections");
        player.sendMessage("");
        player.sendMessage(ChatColor.AQUA + "Type 3 - Corridor Passage:");
        player.sendMessage(ChatColor.WHITE + "• Creates a hallway between distant rooms");
        player.sendMessage(ChatColor.WHITE + "• Automatically builds the connecting path");
        player.sendMessage(ChatColor.WHITE + "• Use for rooms that are far apart");
        player.sendMessage("");
        player.sendMessage(ChatColor.YELLOW + "💡 Connection Tips:");
        player.sendMessage(ChatColor.GRAY + "• Plan your room layout before connecting");
        player.sendMessage(ChatColor.GRAY + "• Leave space for doorways when building");
        player.sendMessage(ChatColor.GRAY + "• Test connections by walking through them");
        player.sendMessage(ChatColor.GRAY + "• Use Type 3 for creative corridor designs");
    }

    /**
     * Show start and end blocks guide.
     */
    public static void showBlocksGuide(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════════════");
        player.sendMessage(ChatColor.GOLD + "      🎯 START & END BLOCKS GUIDE 🎯");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════════════");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "💎 START BLOCK (Emerald Block):");
        player.sendMessage(ChatColor.WHITE + "• Marks the beginning of dungeon challenges");
        player.sendMessage(ChatColor.WHITE + "• Players right-click to start their timer");
        player.sendMessage(ChatColor.WHITE + "• Place in your entrance/start room");
        player.sendMessage(ChatColor.WHITE + "• Only one per dungeon recommended");
        player.sendMessage("");
        player.sendMessage(ChatColor.AQUA + "How to use:");
        player.sendMessage(ChatColor.GRAY + "1. Get from " + ChatColor.YELLOW + "/dgn tools" + ChatColor.GRAY + " → Dungeon Blocks");
        player.sendMessage(ChatColor.GRAY + "2. Place in a prominent, accessible location");
        player.sendMessage(ChatColor.GRAY + "3. Players right-click to begin challenge");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "💎 END BLOCK (Diamond Block):");
        player.sendMessage(ChatColor.WHITE + "• Marks the completion point of the dungeon");
        player.sendMessage(ChatColor.WHITE + "• Players right-click to finish and get rewards");
        player.sendMessage(ChatColor.WHITE + "• Place in your final/boss room");
        player.sendMessage(ChatColor.WHITE + "• Only one per dungeon recommended");
        player.sendMessage("");
        player.sendMessage(ChatColor.AQUA + "How to use:");
        player.sendMessage(ChatColor.GRAY + "1. Get from " + ChatColor.YELLOW + "/dgn tools" + ChatColor.GRAY + " → Dungeon Blocks");
        player.sendMessage(ChatColor.GRAY + "2. Place in the final room after all challenges");
        player.sendMessage(ChatColor.GRAY + "3. Players right-click to complete and get rewards");
        player.sendMessage("");
        player.sendMessage(ChatColor.YELLOW + "⚠ IMPORTANT NOTES:");
        player.sendMessage(ChatColor.RED + "• Players must activate Start Block before End Block");
        player.sendMessage(ChatColor.RED + "• Breaking these blocks cancels active challenges");
        player.sendMessage(ChatColor.RED + "• Blocks don't drop items when broken (by design)");
        player.sendMessage(ChatColor.GREEN + "• Rewards: 2 Diamonds, 5 Emeralds, 1 Golden Apple, 100 XP");
    }

    /**
     * Show schematics usage guide.
     */
    public static void showSchematicsGuide(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════════════");
        player.sendMessage(ChatColor.GOLD + "        📐 SCHEMATICS SYSTEM 📐");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════════════");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "📁 ADDING SCHEMATIC FILES:");
        player.sendMessage(ChatColor.WHITE + "• Place " + ChatColor.YELLOW + ".schem" + ChatColor.WHITE + ", " + ChatColor.YELLOW + ".schematic" + ChatColor.WHITE + ", or " + ChatColor.YELLOW + ".nbt" + ChatColor.WHITE + " files in:");
        player.sendMessage(ChatColor.GRAY + "  " + ChatColor.AQUA + "plugins/ApexDungeons/schematics/");
        player.sendMessage(ChatColor.WHITE + "• Use " + ChatColor.YELLOW + "/dgn reloadschematics" + ChatColor.WHITE + " to load new files");
        player.sendMessage(ChatColor.WHITE + "• Files appear as tools in " + ChatColor.YELLOW + "/dgn tools");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🎮 USING SCHEMATIC TOOLS:");
        player.sendMessage(ChatColor.AQUA + "Preview Mode:");
        player.sendMessage(ChatColor.WHITE + "• Right-click with tool to show 3D outline");
        player.sendMessage(ChatColor.WHITE + "• Use " + ChatColor.YELLOW + "W/A/S/D" + ChatColor.WHITE + " keys to move preview");
        player.sendMessage(ChatColor.WHITE + "• Press " + ChatColor.YELLOW + "R" + ChatColor.WHITE + " to rotate 90 degrees");
        player.sendMessage(ChatColor.WHITE + "• Press " + ChatColor.YELLOW + "Enter" + ChatColor.WHITE + " to confirm placement");
        player.sendMessage(ChatColor.WHITE + "• Shift+Right-click to cancel preview");
        player.sendMessage("");
        player.sendMessage(ChatColor.AQUA + "Direct Placement:");
        player.sendMessage(ChatColor.WHITE + "• Left-click to place immediately (no preview)");
        player.sendMessage(ChatColor.WHITE + "• Faster for experienced builders");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "🏗️ BUILDING WITH SCHEMATICS:");
        player.sendMessage(ChatColor.WHITE + "• Use schematics for room templates");
        player.sendMessage(ChatColor.WHITE + "• Combine multiple schematics for complex designs");
        player.sendMessage(ChatColor.WHITE + "• Modify placed structures as needed");
        player.sendMessage(ChatColor.WHITE + "• Add Start/End blocks after placing structures");
        player.sendMessage("");
        player.sendMessage(ChatColor.YELLOW + "💡 Pro Tips:");
        player.sendMessage(ChatColor.GRAY + "• Preview before placing to avoid mistakes");
        player.sendMessage(ChatColor.GRAY + "• Rotate schematics to fit your layout");
        player.sendMessage(ChatColor.GRAY + "• Use smaller schematics for room details");
        player.sendMessage(ChatColor.GRAY + "• Create your own schematics with WorldEdit!");
    }
}
