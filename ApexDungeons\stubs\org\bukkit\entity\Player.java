package org.bukkit.entity;

import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Minimal stub for Player.  Provides placeholder implementations for methods
 * required during compilation.  All methods are effectively no-ops.
 */
public class Player extends LivingEntity {
    private final UUID uuid = UUID.randomUUID();
    private final World world = new World();
    private final Location location = new Location(world, 0, 64, 0);
    private final Inventory inventory = new Inventory(this, 36, "Player");
    public void sendMessage(String message) {}
    public boolean hasPermission(String perm) { return true; }
    public World getWorld() { return world; }
    public Location getLocation() { return location; }
    public void teleport(Location loc) { }
    public UUID getUniqueId() { return uuid; }
    public void closeInventory() {}
    public void openInventory(Inventory inv) {}
    public Inventory getInventory() { return inventory; }
    public void addItem(ItemStack stack) {}
    public org.bukkit.block.Block getTargetBlockExact(int range) { return null; }

    /**
     * Returns the player's display name.  Stubbed to return a constant.
     * @return the name
     */
    public String getName() { return "Player"; }
}