package org.bukkit.configuration.file;

import java.io.File;

import org.bukkit.configuration.InvalidConfigurationException;

/**
 * Stub for YamlConfiguration.  Extends FileConfiguration with minimal
 * functionality.  All data retrieval methods return defaults.
 */
public class YamlConfiguration extends FileConfiguration {
    public static YamlConfiguration loadConfiguration(File file) { return new YamlConfiguration(); }
    public void load(File file) throws java.io.IOException, InvalidConfigurationException {}
    public void load(java.io.Reader reader) throws InvalidConfigurationException {}
    public void loadFromString(String contents) throws InvalidConfigurationException {}
}