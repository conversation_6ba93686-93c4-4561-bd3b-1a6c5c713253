package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.ArrayList;
import java.util.List;

/**
 * Enhanced main GUI with improved navigation, dungeon previews, and management options.
 * Features a modern design with better user experience and comprehensive functionality.
 */
public class EnhancedMainGUI {
    private static final String GUI_NAME = ChatColor.GOLD + "Soaps Dungeons " + ChatColor.GRAY + "- Made by Vexy";

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory(player, 54, <PERSON><PERSON>_<PERSON>AME);
        
        // Fill background with decorative glass panes
        fillBackground(inv);
        
        // Create main action buttons
        createMainButtons(inv, player, plugin);
        
        // Create navigation and utility buttons
        createUtilityButtons(inv, player, plugin);
        
        player.openInventory(inv);
        
        // Register event listener
        registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        
        ItemStack accent = new ItemStack(Material.BLUE_STAINED_GLASS_PANE);
        ItemMeta accentMeta = accent.getItemMeta();
        accentMeta.setDisplayName(" ");
        accent.setItemMeta(accentMeta);
        
        // Fill border with background
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
        
        // Add accent pieces
        int[] accentSlots = {1, 7, 46, 52};
        for (int slot : accentSlots) {
            inv.setItem(slot, accent);
        }
    }

    private static void createMainButtons(Inventory inv, Player player, ApexDungeons plugin) {
        // Create New Dungeon button
        ItemStack create = new ItemStack(Material.DIAMOND_PICKAXE);
        ItemMeta createMeta = create.getItemMeta();
        createMeta.setDisplayName(ChatColor.GREEN + "✚ Create New Dungeon");
        List<String> createLore = new ArrayList<>();
        createLore.add(ChatColor.GRAY + "Create a new flat dungeon world");
        createLore.add(ChatColor.GRAY + "instantly with just a name!");
        createLore.add("");
        createLore.add(ChatColor.YELLOW + "⚡ Super Fast Process:");
        createLore.add(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Click this button");
        createLore.add(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Enter dungeon name in chat");
        createLore.add(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Done! World created instantly");
        createLore.add("");
        createLore.add(ChatColor.YELLOW + "✨ You get:");
        createLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Completely flat superflat world");
        createLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Optimized for fast generation");
        createLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Ready for building immediately");
        createLore.add("");
        createLore.add(ChatColor.GREEN + "▶ Click for instant creation!");
        createMeta.setLore(createLore);
        create.setItemMeta(createMeta);
        inv.setItem(20, create);
        
        // Active Dungeons button
        ItemStack active = new ItemStack(Material.FILLED_MAP);
        ItemMeta activeMeta = active.getItemMeta();
        activeMeta.setDisplayName(ChatColor.BLUE + "⚔ Active Dungeons");
        List<String> activeLore = new ArrayList<>();
        activeLore.add(ChatColor.GRAY + "View and manage your");
        activeLore.add(ChatColor.GRAY + "currently active dungeons");
        activeLore.add("");
        int dungeonCount = plugin.getDungeonManager().getDungeons().size();
        activeLore.add(ChatColor.YELLOW + "Active: " + ChatColor.WHITE + dungeonCount + ChatColor.GRAY + " dungeons");
        if (dungeonCount > 0) {
            activeLore.add("");
            activeLore.add(ChatColor.GREEN + "▶ Click to view dungeons!");
        } else {
            activeLore.add("");
            activeLore.add(ChatColor.GRAY + "No active dungeons");
        }
        activeMeta.setLore(activeLore);
        active.setItemMeta(activeMeta);
        inv.setItem(22, active);
        
        // Dungeon Browser button
        ItemStack browser = new ItemStack(Material.BOOKSHELF);
        ItemMeta browserMeta = browser.getItemMeta();
        browserMeta.setDisplayName(ChatColor.LIGHT_PURPLE + "📚 Dungeon Browser");
        List<String> browserLore = new ArrayList<>();
        browserLore.add(ChatColor.GRAY + "Browse available dungeon");
        browserLore.add(ChatColor.GRAY + "templates and presets");
        browserLore.add("");
        browserLore.add(ChatColor.YELLOW + "Features:");
        browserLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Preview dungeon layouts");
        browserLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "View difficulty ratings");
        browserLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Check requirements");
        browserLore.add("");
        browserLore.add(ChatColor.GREEN + "▶ Click to browse!");
        browserMeta.setLore(browserLore);
        browser.setItemMeta(browserMeta);
        inv.setItem(24, browser);

        // Building Tools button
        ItemStack buildingTools = new ItemStack(Material.GOLDEN_PICKAXE);
        ItemMeta toolsMeta = buildingTools.getItemMeta();
        toolsMeta.setDisplayName(ChatColor.GOLD + "🔨 Building Tools");
        List<String> toolsLore = new ArrayList<>();
        toolsLore.add(ChatColor.GRAY + "Professional dungeon building");
        toolsLore.add(ChatColor.GRAY + "tools and utilities");
        toolsLore.add("");
        toolsLore.add(ChatColor.YELLOW + "🛠️ Essential Tools:");
        toolsLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Dungeon Start & End Blocks");
        toolsLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Advanced Schematic Tools");
        toolsLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Room Connection System");
        toolsLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Mob Spawn Management");
        toolsLore.add("");
        toolsLore.add(ChatColor.GREEN + "▶ Click to access building tools!");
        toolsMeta.setLore(toolsLore);
        buildingTools.setItemMeta(toolsMeta);
        inv.setItem(25, buildingTools);
    }

    private static void createUtilityButtons(Inventory inv, Player player, ApexDungeons plugin) {
        // Statistics button
        ItemStack stats = new ItemStack(Material.CLOCK);
        ItemMeta statsMeta = stats.getItemMeta();
        statsMeta.setDisplayName(ChatColor.GOLD + "📊 Statistics");
        List<String> statsLore = new ArrayList<>();
        statsLore.add(ChatColor.GRAY + "View your dungeon");
        statsLore.add(ChatColor.GRAY + "creation statistics");
        statsLore.add("");
        statsLore.add(ChatColor.YELLOW + "Your Stats:");
        statsLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Dungeons Created: " + ChatColor.YELLOW + "0");
        statsLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Total Playtime: " + ChatColor.YELLOW + "0h 0m");
        statsLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Favorite Theme: " + ChatColor.YELLOW + "None");
        statsLore.add("");
        statsLore.add(ChatColor.GREEN + "▶ Click to view details!");
        statsMeta.setLore(statsLore);
        stats.setItemMeta(statsMeta);
        inv.setItem(30, stats);
        
        // Saved Dungeons button
        ItemStack savedDungeons = new ItemStack(Material.BOOKSHELF);
        ItemMeta savedMeta = savedDungeons.getItemMeta();
        savedMeta.setDisplayName(ChatColor.LIGHT_PURPLE + "📚 Saved Dungeons");
        List<String> savedLore = new ArrayList<>();
        savedLore.add(ChatColor.GRAY + "Browse and load your");
        savedLore.add(ChatColor.GRAY + "saved dungeon templates");
        savedLore.add("");
        int savedCount = plugin.getSavedDungeonManager().getSavedDungeons().size();
        savedLore.add(ChatColor.YELLOW + "Saved: " + ChatColor.WHITE + savedCount + ChatColor.GRAY + " dungeons");
        savedLore.add("");
        savedLore.add(ChatColor.YELLOW + "Features:");
        savedLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Load saved dungeons instantly");
        savedLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "View dungeon details");
        savedLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Manage your templates");
        savedLore.add("");
        savedLore.add(ChatColor.GREEN + "▶ Click to browse!");
        savedMeta.setLore(savedLore);
        savedDungeons.setItemMeta(savedMeta);
        inv.setItem(32, savedDungeons);
        
        // Help button
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(ChatColor.AQUA + "❓ Help & Guide");
        List<String> helpLore = new ArrayList<>();
        helpLore.add(ChatColor.GRAY + "Learn how to use");
        helpLore.add(ChatColor.GRAY + "Apex Dungeons effectively");
        helpLore.add("");
        helpLore.add(ChatColor.YELLOW + "Topics:");
        helpLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Getting Started");
        helpLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Dungeon Creation");
        helpLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Portal System");
        helpLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Commands & Permissions");
        helpLore.add("");
        helpLore.add(ChatColor.GREEN + "▶ Click for help!");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(42, help);
        
        // Admin Tools button (if player has permission)
        if (player.hasPermission("apexdungeons.admin")) {
            ItemStack admin = new ItemStack(Material.COMMAND_BLOCK);
            ItemMeta adminMeta = admin.getItemMeta();
            adminMeta.setDisplayName(ChatColor.RED + "🔧 Admin Tools");
            List<String> adminLore = new ArrayList<>();
            adminLore.add(ChatColor.GRAY + "Administrative functions");
            adminLore.add(ChatColor.GRAY + "and server management");
            adminLore.add("");
            adminLore.add(ChatColor.YELLOW + "Admin Features:");
            adminLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Force Delete Dungeons");
            adminLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Server Statistics");
            adminLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Plugin Configuration");
            adminLore.add("");
            adminLore.add(ChatColor.RED + "⚠ Admin Access Required");
            adminMeta.setLore(adminLore);
            admin.setItemMeta(adminMeta);
            inv.setItem(40, admin);
        }
    }

    private static void registerEventListener(ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player) e.getWhoClicked();
                    int slot = e.getRawSlot();
                    
                    switch (slot) {
                        case 20: // Create New Dungeon
                            clicker.closeInventory();
                            DungeonCreationGUI.open(clicker, plugin);
                            break;
                        case 22: // Active Dungeons
                            clicker.closeInventory();
                            ActiveGUI.open(clicker, plugin);
                            break;
                        case 24: // Dungeon Browser
                            clicker.closeInventory();
                            DungeonBrowserGUI.open(clicker, plugin);
                            break;
                        case 25: // Building Tools
                            clicker.closeInventory();
                            BuildingToolsGUI.open(clicker, plugin);
                            break;
                        case 30: // Statistics
                            clicker.closeInventory();
                            StatisticsGUI.open(clicker, plugin);
                            break;
                        case 32: // Saved Dungeons
                            clicker.closeInventory();
                            SavedDungeonsGUI.open(clicker, plugin);
                            break;
                        case 42: // Help
                            clicker.closeInventory();
                            HelpGUI.open(clicker, plugin);
                            break;
                        case 40: // Admin Tools
                            if (clicker.hasPermission("apexdungeons.admin")) {
                                clicker.closeInventory();
                                AdminGUI.open(clicker, plugin);
                            } else {
                                clicker.sendMessage(ChatColor.RED + "You don't have permission to access admin tools!");
                            }
                            break;
                    }
                }
            }
        }, plugin);
    }
}
