package com.apexdungeons.gen;

import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a preset configuration for generating dungeons.  Each preset
 * defines the maximum number of rooms, branch chance and a list of themes
 * which determine which rooms can be used.
 */
public class DungeonPreset {
    private final int maxRooms;
    private final double branchChance;
    private final List<String> themes;

    public DungeonPreset(int maxRooms, double branchChance, List<String> themes) {
        this.maxRooms = maxRooms;
        this.branchChance = branchChance;
        this.themes = themes;
    }

    public int getMaxRooms() {
        return maxRooms;
    }

    public double getBranchChance() {
        return branchChance;
    }

    public List<String> getThemes() {
        return themes;
    }

    /**
     * Load a preset from a YAML file.
     */
    public static DungeonPreset load(File file) {
        if (file == null || !file.exists()) return null;
        YamlConfiguration conf = new YamlConfiguration();
        try {
            conf.load(file);
        } catch (IOException | org.bukkit.configuration.InvalidConfigurationException e) {
            e.printStackTrace();
            return null;
        }

        // Support both old simple format and new complex format
        int maxRooms;
        double branchChance;
        List<String> themes = new ArrayList<>();

        // Try new format first
        if (conf.isConfigurationSection("generation")) {
            maxRooms = conf.getInt("generation.max_rooms", 15);
            branchChance = 0.3; // Default for new format
        } else {
            // Fall back to old format
            maxRooms = conf.getInt("maxRooms", conf.getInt("max_rooms", 10));
            branchChance = conf.getDouble("branchChance", conf.getDouble("branch_chance", 0.2));
        }

        // Get themes
        if (conf.contains("theme.name")) {
            // Single theme from new format
            themes.add(conf.getString("theme.name"));
        } else if (conf.contains("themes")) {
            // Multiple themes
            themes.addAll(conf.getStringList("themes"));
        } else {
            // Default theme based on filename
            String filename = file.getName().replace(".yml", "");
            themes.add(filename);
        }

        return new DungeonPreset(maxRooms, branchChance, themes);
    }
}