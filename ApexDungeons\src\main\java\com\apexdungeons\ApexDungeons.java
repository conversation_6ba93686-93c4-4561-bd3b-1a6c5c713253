package com.apexdungeons;

import com.apexdungeons.blocks.DungeonBlockManager;
import com.apexdungeons.commands.DgnCommand;
import com.apexdungeons.config.DungeonConfig;
import com.apexdungeons.effects.EffectsManager;
import com.apexdungeons.gui.PartyGUI;
import com.apexdungeons.instance.DungeonInstanceManager;

import com.apexdungeons.party.PartyManager;
import com.apexdungeons.templates.DungeonTemplateManager;
import com.apexdungeons.gen.DungeonManager;
import com.apexdungeons.chests.ChestLootManager;
import com.apexdungeons.chests.ChestSpawnData;
import com.apexdungeons.chests.ChestSpawnManager;
import com.apexdungeons.integration.MobAdapter;
import com.apexdungeons.integration.MythicMobsAdapter;
import com.apexdungeons.integration.VanillaAdapter;
import com.apexdungeons.listeners.ChestSpawnToolListener;
import com.apexdungeons.listeners.MobSpawnToolListener;
import com.apexdungeons.mobs.DungeonMobManager;
import com.apexdungeons.mobs.MobSpawnData;
import com.apexdungeons.mobs.MobSpawnManager;
import com.apexdungeons.saved.SavedDungeonManager;
import com.apexdungeons.tools.ChestSpawnTool;
import com.apexdungeons.player.PlayerLocationManager;

import com.apexdungeons.schematics.PreviewInputHandler;
import com.apexdungeons.schematics.SchematicManager;


import com.apexdungeons.tools.MasterBuilderWand;
import com.apexdungeons.tools.MobSpawnTool;
import com.apexdungeons.tools.RoomConnector;
import com.apexdungeons.tools.SchematicTool;
import com.apexdungeons.wand.WandManager;
import com.apexdungeons.world.WorldManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.configuration.InvalidConfigurationException;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.logging.Level;

/**
 * ApexDungeons is the main entry point for the plugin.  It initialises
 * configuration files, registers commands and sets up managers for dungeons,
 * wands and mob spawning.
 */
public class ApexDungeons extends JavaPlugin {

    private DungeonManager dungeonManager;
    private WandManager wandManager;
    private WorldManager worldManager;

    private EffectsManager effectsManager;
    private PlayerLocationManager playerLocationManager;
    private DungeonBlockManager dungeonBlockManager;
    private SchematicManager schematicManager;
    private SchematicTool schematicTool;
    private MasterBuilderWand masterBuilderWand;
    private PreviewInputHandler previewInputHandler;
    private RoomConnector roomConnector;
    private MobAdapter mobAdapter;
    private MobSpawnTool mobSpawnTool;
    private MobSpawnManager mobSpawnManager;
    private MobSpawnData mobSpawnData;
    private DungeonMobManager dungeonMobManager;
    private ChestSpawnTool chestSpawnTool;
    private ChestSpawnManager chestSpawnManager;
    private ChestSpawnData chestSpawnData;
    private ChestLootManager chestLootManager;
    private SavedDungeonManager savedDungeonManager;
    private DungeonConfig dungeonConfig;
    private DungeonTemplateManager templateManager;

    private DungeonInstanceManager instanceManager;
    private PartyManager partyManager;
    private PartyGUI partyGUI;

    @Override
    public void onEnable() {
        // Save default config and copy default resources
        saveDefaultConfig();
            saveResourceIfMissing("loot.yml");
            saveResourceIfMissing("mobs.yml");
            saveResourceIfMissing("bosses.yml");
            saveResourceIfMissing("messages.yml");
            saveResourceIfMissing("presets/small.yml");
            saveResourceIfMissing("presets/medium.yml");
            saveResourceIfMissing("presets/large.yml");
            saveResourceIfMissing("presets/normal.yml");
            saveResourceIfMissing("presets/temple.yml");
            saveResourceIfMissing("presets/crypt.yml");

            // copy default rooms
            getLogger().info("Copying default room blueprints...");
            saveResourceFolderIfMissing("rooms");

            // Initialise managers
            getLogger().info("Initializing managers...");
            this.worldManager = new WorldManager(this);

            this.effectsManager = new EffectsManager(this);
            this.playerLocationManager = new PlayerLocationManager(this);
            this.dungeonBlockManager = new DungeonBlockManager(this);
            this.schematicManager = new SchematicManager(this);
            this.schematicTool = new SchematicTool(this);
            this.masterBuilderWand = new MasterBuilderWand(this);
            this.previewInputHandler = new PreviewInputHandler(this);
            this.roomConnector = new RoomConnector(this);
            this.dungeonManager = new DungeonManager(this);
            this.wandManager = new WandManager(this);
            this.mobAdapter = initializeMobAdapter();
            this.mobSpawnTool = new MobSpawnTool(this);
            this.mobSpawnData = new MobSpawnData(this);
            this.mobSpawnManager = new MobSpawnManager(this);
            this.dungeonMobManager = new DungeonMobManager(this);
            this.chestLootManager = new ChestLootManager(this);
            this.chestSpawnTool = new ChestSpawnTool(this);
            this.chestSpawnData = new ChestSpawnData(this);
            this.chestSpawnManager = new ChestSpawnManager(this);
            this.savedDungeonManager = new SavedDungeonManager(this);
            this.dungeonConfig = new DungeonConfig(this);
            this.templateManager = new DungeonTemplateManager(this);

            this.instanceManager = new DungeonInstanceManager(this);
            this.partyManager = new PartyManager(this);
            this.partyGUI = new PartyGUI(this);

            // Register command
            getLogger().info("Registering commands and listeners...");
            getCommand("dgn").setExecutor(new DgnCommand(this));

            // Register mob spawn tool listener
            getServer().getPluginManager().registerEvents(new MobSpawnToolListener(this), this);

            // Register chest spawn tool listener
            getServer().getPluginManager().registerEvents(new ChestSpawnToolListener(this), this);

            getLogger().info("ApexDungeons has been enabled.");
    }

    @Override
    public void onDisable() {
        if (dungeonManager != null) {
            dungeonManager.shutdown();
        }

        if (worldManager != null) {
            worldManager.shutdown();
        }
        if (playerLocationManager != null) {
            playerLocationManager.shutdown();
        }
        if (mobSpawnManager != null) {
            mobSpawnManager.shutdown();
        }
        if (mobSpawnData != null) {
            mobSpawnData.shutdown();
        }
        if (savedDungeonManager != null) {
            savedDungeonManager.shutdown();
        }
        if (chestSpawnManager != null) {
            chestSpawnManager.shutdown();
        }
        if (chestSpawnData != null) {
            chestSpawnData.shutdown();
        }
        if (chestLootManager != null) {
            chestLootManager.shutdown();
        }
        if (dungeonBlockManager != null) {
            dungeonBlockManager.shutdown();
        }
        if (schematicManager != null) {
            schematicManager.shutdown();
        }
        if (schematicTool != null) {
            schematicTool.shutdown();
        }
        if (previewInputHandler != null) {
            previewInputHandler.shutdown();
        }
        if (roomConnector != null) {
            roomConnector.shutdown();
        }
        getLogger().info("ApexDungeons has been disabled.");
    }

    /**
     * Copy a resource from the JAR to the plugin data folder only if it does not already exist.
     * @param resourcePath Path inside the JAR.
     */
    private void saveResourceIfMissing(String resourcePath) {
        File outFile = new File(getDataFolder(), resourcePath);
        if (!outFile.exists()) {
            // Create parent directories
            if (outFile.getParentFile() != null) {
                outFile.getParentFile().mkdirs();
            }
            try (InputStream in = getResource(resourcePath)) {
                if (in == null) {
                    getLogger().warning("Resource " + resourcePath + " not found inside jar.");
                    return;
                }
                Files.copy(in, outFile.toPath());
            } catch (IOException ex) {
                getLogger().log(Level.SEVERE, "Failed to save resource " + resourcePath, ex);
            }
        }
    }

    /**
     * Copy every file from a folder inside the JAR to the data folder if missing.  This method
     * iterates over the directory listing using the class loader.  Only top level files are copied.
     * @param folderPath folder inside the jar
     */
    private void saveResourceFolderIfMissing(String folderPath) {
        try {
            // Under Bukkit, we cannot list jar directories easily, so we copy all known blueprint files
            getLogger().info("Copying blueprint files from " + folderPath + "...");

            // Original blueprint files
            saveResourceIfMissing(folderPath + "/starter_room.yml");
            saveResourceIfMissing(folderPath + "/corridor.yml");
            saveResourceIfMissing(folderPath + "/boss_room.yml");

            // New castle theme blueprints
            saveResourceIfMissing(folderPath + "/castle_hall.yml");
            saveResourceIfMissing(folderPath + "/castle_tower.yml");
            saveResourceIfMissing(folderPath + "/castle_dungeon.yml");

            // New cave theme blueprints
            saveResourceIfMissing(folderPath + "/cave_tunnel.yml");
            saveResourceIfMissing(folderPath + "/underground_lake.yml");

            // New temple theme blueprints
            saveResourceIfMissing(folderPath + "/temple_chamber.yml");

            // New crypt theme blueprints
            saveResourceIfMissing(folderPath + "/crypt_chamber.yml");

            getLogger().info("Blueprint files copied successfully!");

        } catch (Exception ex) {
            getLogger().log(Level.SEVERE, "Failed to copy resource folder " + folderPath, ex);
        }
    }



    public DungeonManager getDungeonManager() {
        return dungeonManager;
    }

    public WandManager getWandManager() {
        return wandManager;
    }

    public WorldManager getWorldManager() {
        return worldManager;
    }



    public EffectsManager getEffectsManager() {
        return effectsManager;
    }



    public PlayerLocationManager getPlayerLocationManager() {
        return playerLocationManager;
    }

    public DungeonConfig getDungeonConfig() {
        return dungeonConfig;
    }

    public DungeonTemplateManager getTemplateManager() {
        return templateManager;
    }

    public MobAdapter getMobAdapter() {
        return mobAdapter;
    }

    public MobSpawnTool getMobSpawnTool() {
        return mobSpawnTool;
    }

    public MobSpawnManager getMobSpawnManager() {
        return mobSpawnManager;
    }

    public MobSpawnData getMobSpawnData() {
        return mobSpawnData;
    }

    public SavedDungeonManager getSavedDungeonManager() {
        return savedDungeonManager;
    }

    public ChestSpawnTool getChestSpawnTool() {
        return chestSpawnTool;
    }

    public ChestSpawnManager getChestSpawnManager() {
        return chestSpawnManager;
    }

    public ChestSpawnData getChestSpawnData() {
        return chestSpawnData;
    }

    public ChestLootManager getChestLootManager() {
        return chestLootManager;
    }

    /**
     * Initialize the mob adapter based on available plugins.
     */
    private MobAdapter initializeMobAdapter() {
        if (getServer().getPluginManager().getPlugin("MythicMobs") != null) {
            getLogger().info("MythicMobs detected, using MythicMobs adapter");
            return new MythicMobsAdapter(this);
        } else {
            getLogger().info("MythicMobs not found, using vanilla adapter");
            return new VanillaAdapter();
        }
    }



    public DungeonInstanceManager getInstanceManager() {
        return instanceManager;
    }

    public PartyManager getPartyManager() {
        return partyManager;
    }

    public PartyGUI getPartyGUI() {
        return partyGUI;
    }

    public DungeonBlockManager getDungeonBlockManager() {
        return dungeonBlockManager;
    }

    public SchematicManager getSchematicManager() {
        return schematicManager;
    }

    public SchematicTool getSchematicTool() {
        return schematicTool;
    }

    public MasterBuilderWand getMasterBuilderWand() {
        return masterBuilderWand;
    }



    public PreviewInputHandler getPreviewInputHandler() {
        return previewInputHandler;
    }

    public RoomConnector getRoomConnector() {
        return roomConnector;
    }

    public DungeonMobManager getDungeonMobManager() {
        return dungeonMobManager;
    }
}