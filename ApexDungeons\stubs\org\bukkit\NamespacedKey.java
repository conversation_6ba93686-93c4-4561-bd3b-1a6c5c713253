package org.bukkit;

import org.bukkit.plugin.Plugin;

/**
 * Stub for NamespacedKey.  Holds a namespace and a key string.
 */
public class NamespacedKey {
    private final String namespace;
    private final String key;
    public NamespacedKey(Plugin plugin, String key) {
        this.namespace = plugin.getName().toLowerCase();
        this.key = key;
    }
    public NamespacedKey(String namespace, String key) {
        this.namespace = namespace;
        this.key = key;
    }
    public String getNamespace() { return namespace; }
    public String getKey() { return key; }
}