package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.ArrayList;
import java.util.List;

/**
 * GUI listing all active dungeons on the server.  Clicking on an entry will
 * teleport the player to that dungeon's entry point.
 */
public class ActiveGUI {
    private static final String GUI_NAME = ChatColor.DARK_GRAY + "Active Dungeons";

    public static void open(Player player, ApexDungeons plugin) {
        List<DungeonInstance> dungeons = new ArrayList<>(plugin.getDungeonManager().getActiveDungeons());
        int rows = Math.max(1, (int) Math.ceil(dungeons.size() / 9.0));
        Inventory inv = Bukkit.createInventory(player, rows * 9, GUI_NAME);
        for (int i = 0; i < dungeons.size(); i++) {
            DungeonInstance inst = dungeons.get(i);
            ItemStack map = new ItemStack(Material.FILLED_MAP);
            ItemMeta meta = map.getItemMeta();
            meta.setDisplayName(ChatColor.AQUA + inst.getDisplayName());
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "Creator: " + inst.getCreator());
            lore.add(ChatColor.GRAY + "Rooms: " + inst.getRoomCount());
            lore.add(ChatColor.GRAY + "World: " + inst.getWorld().getName());
            if (inst.isGenerating()) {
                lore.add(ChatColor.YELLOW + "(Generating)");
            } else {
                lore.add(ChatColor.GREEN + "(Ready)");
            }
            lore.add("");
            lore.add(ChatColor.YELLOW + "Click to teleport");
            meta.setLore(lore);
            map.setItemMeta(meta);
            inv.setItem(i, map);
        }
        JavaPlugin pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    if (slot >= 0 && slot < dungeons.size()) {
                        DungeonInstance inst = dungeons.get(slot);
                        e.getWhoClicked().closeInventory();
                        plugin.getDungeonManager().tpToDungeon(inst.getName(), (Player) e.getWhoClicked());
                    }
                }
            }
        }, pl);
        player.openInventory(inv);
    }
}