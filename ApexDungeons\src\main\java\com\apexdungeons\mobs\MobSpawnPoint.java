package com.apexdungeons.mobs;

import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.entity.TextDisplay;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * Enhanced mob spawn point with proximity detection, persistent spawning, and visual feedback.
 * Supports cooldowns, max concurrent mobs, and invisible detection entities.
 */
public class MobSpawnPoint {
    private final Location location;
    private final String mobName;
    private final double radius;
    private final boolean isBoss;

    // Enhanced features
    private long lastSpawnTime = 0;
    private long cooldownMs = 30000; // 30 seconds default
    private int maxConcurrentMobs = 3; // Default max concurrent mobs
    private final Set<UUID> spawnedMobs = new HashSet<>(); // Track spawned mobs
    private ArmorStand detectionEntity; // Invisible detection entity
    private boolean isActive = true;
    private String displayName; // Custom display name for visual feedback
    private TextDisplay builderDisplay; // Builder-only text display
    private DungeonMobConfig mobConfig; // Associated mob configuration

    public MobSpawnPoint(Location location, String mobName, double radius, boolean isBoss) {
        this.location = location.clone();
        this.mobName = mobName;
        this.radius = radius;
        this.isBoss = isBoss;
        this.displayName = (isBoss ? "Boss: " : "Mob: ") + mobName;
        this.maxConcurrentMobs = isBoss ? 1 : 3; // Bosses typically spawn alone
        this.cooldownMs = isBoss ? 60000 : 30000; // Bosses have longer cooldown
    }
    
    public Location getLocation() {
        return location.clone();
    }

    public String getMobName() {
        return mobName;
    }

    public double getRadius() {
        return radius;
    }

    public boolean isBoss() {
        return isBoss;
    }

    // Enhanced feature getters and setters
    public long getLastSpawnTime() {
        return lastSpawnTime;
    }

    public void setLastSpawnTime(long lastSpawnTime) {
        this.lastSpawnTime = lastSpawnTime;
    }

    public long getCooldownMs() {
        return cooldownMs;
    }

    public void setCooldownMs(long cooldownMs) {
        this.cooldownMs = cooldownMs;
    }

    public int getMaxConcurrentMobs() {
        return maxConcurrentMobs;
    }

    public void setMaxConcurrentMobs(int maxConcurrentMobs) {
        this.maxConcurrentMobs = maxConcurrentMobs;
    }

    public Set<UUID> getSpawnedMobs() {
        return spawnedMobs;
    }

    public ArmorStand getDetectionEntity() {
        return detectionEntity;
    }

    public void setDetectionEntity(ArmorStand detectionEntity) {
        this.detectionEntity = detectionEntity;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    /**
     * Check if this spawn point is on cooldown.
     */
    public boolean isOnCooldown() {
        return System.currentTimeMillis() - lastSpawnTime < cooldownMs;
    }

    /**
     * Check if this spawn point has reached its max concurrent mob limit.
     */
    public boolean isAtMaxCapacity() {
        return spawnedMobs.size() >= maxConcurrentMobs;
    }

    /**
     * Get remaining cooldown time in seconds.
     */
    public long getRemainingCooldownSeconds() {
        long remaining = cooldownMs - (System.currentTimeMillis() - lastSpawnTime);
        return Math.max(0, remaining / 1000);
    }

    /**
     * Add a spawned mob to tracking.
     */
    public void addSpawnedMob(UUID mobId) {
        spawnedMobs.add(mobId);
    }

    /**
     * Remove a spawned mob from tracking.
     */
    public void removeSpawnedMob(UUID mobId) {
        spawnedMobs.remove(mobId);
    }

    /**
     * Get the current status for display purposes.
     */
    public String getStatus() {
        if (!isActive) {
            return "Inactive";
        } else if (isOnCooldown()) {
            return "Cooldown: " + getRemainingCooldownSeconds() + "s";
        } else if (isAtMaxCapacity()) {
            return "Max spawned";
        } else {
            return "Ready";
        }
    }

    /**
     * Set the associated mob configuration for enhanced visual feedback.
     */
    public void setMobConfig(DungeonMobConfig config) {
        this.mobConfig = config;
        if (config != null) {
            this.displayName = config.getDisplayName();
            this.cooldownMs = (config.getCooldownMin() + config.getCooldownMax()) / 2 * 1000L;
            this.maxConcurrentMobs = config.getMaxConcurrent();
        }
    }

    public DungeonMobConfig getMobConfig() {
        return mobConfig;
    }

    /**
     * Create builder-only visual feedback for this spawn point.
     */
    public void createBuilderDisplay() {
        if (builderDisplay != null) {
            builderDisplay.remove();
        }

        try {
            // Create floating text display above spawn point
            Location displayLoc = location.clone().add(0, 2.5, 0);
            builderDisplay = location.getWorld().spawn(displayLoc, TextDisplay.class);

            // Configure the display
            builderDisplay.setInvulnerable(true);
            builderDisplay.setGravity(false);
            builderDisplay.setPersistent(false);
            builderDisplay.setVisibleByDefault(false);

            updateBuilderDisplayText();

        } catch (Exception e) {
            // TextDisplay might not be available in older versions
            createLegacyBuilderDisplay();
        }
    }

    /**
     * Update the builder display text with current status.
     */
    public void updateBuilderDisplayText() {
        if (builderDisplay == null) return;

        StringBuilder text = new StringBuilder();
        text.append(ChatColor.GOLD).append("⚔ ").append(displayName).append("\n");

        if (mobConfig != null) {
            text.append(mobConfig.getDifficultyColor()).append(mobConfig.getDifficulty().toUpperCase()).append(" ");
            text.append(mobConfig.getCategoryColor()).append(mobConfig.getCategory().toUpperCase()).append("\n");
        }

        text.append(ChatColor.GRAY).append("Radius: ").append(ChatColor.WHITE).append((int)radius).append("m\n");
        text.append(ChatColor.GRAY).append("Cooldown: ").append(ChatColor.WHITE).append(cooldownMs/1000).append("s\n");
        text.append(ChatColor.GRAY).append("Max: ").append(ChatColor.WHITE).append(maxConcurrentMobs).append(" mobs\n");

        // Status indicator
        String status = getBuilderStatusText();
        text.append(status);

        builderDisplay.setText(text.toString());
    }

    /**
     * Legacy builder display using armor stand for older versions.
     */
    private void createLegacyBuilderDisplay() {
        if (detectionEntity != null) {
            detectionEntity.setCustomName(getBuilderDisplayName());
            detectionEntity.setCustomNameVisible(true);
        }
    }

    /**
     * Get status text for builder display.
     */
    private String getBuilderStatusText() {
        if (!isActive) {
            return ChatColor.GRAY + "⏸ INACTIVE";
        }

        long timeSinceSpawn = System.currentTimeMillis() - lastSpawnTime;
        if (timeSinceSpawn < cooldownMs) {
            long remaining = (cooldownMs - timeSinceSpawn) / 1000;
            return ChatColor.RED + "⏳ Cooldown: " + remaining + "s";
        }

        if (spawnedMobs.size() >= maxConcurrentMobs) {
            return ChatColor.YELLOW + "⚠ Max spawned (" + spawnedMobs.size() + ")";
        }

        return ChatColor.GREEN + "✓ Ready to spawn";
    }

    /**
     * Get builder display name for legacy display.
     */
    private String getBuilderDisplayName() {
        String status = getBuilderStatusText();
        return ChatColor.GOLD + displayName + " " + status;
    }

    /**
     * Show builder visual feedback to a specific player.
     */
    public void showBuilderFeedback(Player player) {
        if (builderDisplay != null) {
            // For newer versions with entity visibility API
            // This would need to be implemented with the plugin instance
            builderDisplay.setVisibleByDefault(false);
        }

        // Show particle effects for spawn radius (builder only)
        showRadiusParticles(player);
    }

    /**
     * Hide builder visual feedback from a specific player.
     */
    public void hideBuilderFeedback(Player player) {
        if (builderDisplay != null) {
            // For newer versions with entity visibility API
            // This would need to be implemented with the plugin instance
            builderDisplay.setVisibleByDefault(false);
        }
    }

    /**
     * Show radius particles for builders.
     */
    private void showRadiusParticles(Player player) {
        // Create circle of particles to show spawn radius
        int points = 32;
        for (int i = 0; i < points; i++) {
            double angle = 2 * Math.PI * i / points;
            double x = location.getX() + radius * Math.cos(angle);
            double z = location.getZ() + radius * Math.sin(angle);
            Location particleLoc = new Location(location.getWorld(), x, location.getY() + 0.1, z);

            Particle particle = isBoss ? Particle.FLAME : Particle.HAPPY_VILLAGER;
            player.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }

    /**
     * Remove all visual feedback elements.
     */
    public void removeBuilderDisplay() {
        if (builderDisplay != null) {
            builderDisplay.remove();
            builderDisplay = null;
        }
    }

    /**
     * Check if a player should see builder feedback.
     */
    public boolean shouldShowBuilderFeedback(Player player) {
        return player.hasPermission("apexdungeons.builder") ||
               player.hasPermission("apexdungeons.admin") ||
               player.getGameMode() == org.bukkit.GameMode.CREATIVE;
    }

    @Override
    public String toString() {
        return String.format("MobSpawnPoint{mob=%s, location=%s, radius=%.1f, boss=%s, status=%s}",
            mobName, location, radius, isBoss, getStatus());
    }
}
