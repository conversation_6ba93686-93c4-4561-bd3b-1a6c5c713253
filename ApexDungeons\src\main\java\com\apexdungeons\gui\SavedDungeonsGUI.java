package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.saved.SavedDungeon;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * GUI for browsing and loading saved dungeons.
 */
public class SavedDungeonsGUI {
    private static final String GUI_NAME = ChatColor.DARK_PURPLE + "📚 Saved Dungeons";
    private static boolean listenerRegistered = false;
    
    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory(null, 54, GUI_NAME);
        
        // Fill background
        fillBackground(inv);
        
        // Add saved dungeons
        addSavedDungeons(inv, plugin);
        
        // Add navigation buttons
        addNavigationButtons(inv);
        
        player.openInventory(inv);
        
        // Register event listener if not already registered
        if (!listenerRegistered) {
            registerEventListener(plugin);
            listenerRegistered = true;
        }
    }
    
    private static void fillBackground(Inventory inv) {
        ItemStack background = new ItemStack(Material.PURPLE_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        
        // Fill border
        int[] borderSlots = {0, 1, 2, 3, 4, 5, 6, 7, 8, 45, 46, 47, 48, 49, 50, 51, 52, 53};
        for (int slot : borderSlots) {
            inv.setItem(slot, background);
        }
    }
    
    private static void addSavedDungeons(Inventory inv, ApexDungeons plugin) {
        Map<String, SavedDungeon> savedDungeons = plugin.getSavedDungeonManager().getSavedDungeons();
        
        if (savedDungeons.isEmpty()) {
            // No saved dungeons message
            ItemStack noSaved = new ItemStack(Material.BARRIER);
            ItemMeta noSavedMeta = noSaved.getItemMeta();
            noSavedMeta.setDisplayName(ChatColor.RED + "No Saved Dungeons");
            List<String> noSavedLore = new ArrayList<>();
            noSavedLore.add(ChatColor.GRAY + "You haven't saved any dungeons yet.");
            noSavedLore.add("");
            noSavedLore.add(ChatColor.YELLOW + "To save a dungeon:");
            noSavedLore.add(ChatColor.AQUA + "1. " + ChatColor.WHITE + "Create or enter a dungeon");
            noSavedLore.add(ChatColor.AQUA + "2. " + ChatColor.WHITE + "Use " + ChatColor.YELLOW + "/dgn save <name>");
            noSavedLore.add(ChatColor.AQUA + "3. " + ChatColor.WHITE + "Your dungeon will appear here!");
            noSavedMeta.setLore(noSavedLore);
            noSaved.setItemMeta(noSavedMeta);
            inv.setItem(22, noSaved);
            return;
        }
        
        // Display saved dungeons (max 36 slots: 9-44 excluding borders)
        int slot = 9;
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy");
        
        for (Map.Entry<String, SavedDungeon> entry : savedDungeons.entrySet()) {
            if (slot >= 45) break; // Don't overflow into bottom border
            
            SavedDungeon savedDungeon = entry.getValue();
            
            // Choose icon based on dungeon features
            Material icon = Material.STRUCTURE_BLOCK;
            if (!savedDungeon.getBossSpawns().isEmpty()) {
                icon = Material.WITHER_SKELETON_SKULL;
            } else if (!savedDungeon.getMobSpawns().isEmpty()) {
                icon = Material.ZOMBIE_HEAD;
            } else if (!savedDungeon.getChestSpawns().isEmpty()) {
                icon = Material.CHEST;
            }
            
            ItemStack dungeonItem = new ItemStack(icon);
            ItemMeta dungeonMeta = dungeonItem.getItemMeta();
            dungeonMeta.setDisplayName(ChatColor.LIGHT_PURPLE + "📚 " + savedDungeon.getName());
            
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + savedDungeon.getDescription());
            lore.add("");
            lore.add(ChatColor.YELLOW + "📊 Dungeon Info:");
            lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Creator: " + ChatColor.GRAY + savedDungeon.getCreator());
            lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Created: " + ChatColor.GRAY + dateFormat.format(new Date(savedDungeon.getCreatedTime())));
            if (savedDungeon.getOriginalName() != null && !savedDungeon.getOriginalName().isEmpty()) {
                lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Original: " + ChatColor.GRAY + savedDungeon.getOriginalName());
            }
            lore.add("");
            lore.add(ChatColor.YELLOW + "🏗 Structure:");
            lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Start Blocks: " + ChatColor.GREEN + savedDungeon.getStartBlocks().size());
            lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "End Blocks: " + ChatColor.GREEN + savedDungeon.getEndBlocks().size());
            lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Mob Spawns: " + ChatColor.GREEN + savedDungeon.getMobSpawns().size());
            lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Boss Spawns: " + ChatColor.GREEN + savedDungeon.getBossSpawns().size());
            lore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Chest Spawns: " + ChatColor.GREEN + savedDungeon.getChestSpawns().size());
            lore.add("");
            lore.add(ChatColor.GREEN + "▶ Left-click to load as new dungeon!");
            lore.add(ChatColor.RED + "▶ Right-click to delete (Admin only)");
            
            dungeonMeta.setLore(lore);
            dungeonItem.setItemMeta(dungeonMeta);
            inv.setItem(slot, dungeonItem);
            
            slot++;
            if (slot % 9 == 0) slot += 2; // Skip border columns
        }
    }
    
    private static void addNavigationButtons(Inventory inv) {
        // Back button
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(ChatColor.YELLOW + "← Back to Main Menu");
        List<String> backLore = new ArrayList<>();
        backLore.add(ChatColor.GRAY + "Return to the main menu");
        backMeta.setLore(backLore);
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        
        // Refresh button
        ItemStack refresh = new ItemStack(Material.CLOCK);
        ItemMeta refreshMeta = refresh.getItemMeta();
        refreshMeta.setDisplayName(ChatColor.AQUA + "🔄 Refresh");
        List<String> refreshLore = new ArrayList<>();
        refreshLore.add(ChatColor.GRAY + "Refresh the saved dungeons list");
        refreshMeta.setLore(refreshLore);
        refresh.setItemMeta(refreshMeta);
        inv.setItem(49, refresh);
        
        // Help button
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(ChatColor.GREEN + "❓ Help");
        List<String> helpLore = new ArrayList<>();
        helpLore.add(ChatColor.GRAY + "Learn about saved dungeons");
        helpLore.add("");
        helpLore.add(ChatColor.YELLOW + "How to use:");
        helpLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Save: " + ChatColor.GRAY + "/dgn save <name>");
        helpLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Load: " + ChatColor.GRAY + "Click a saved dungeon");
        helpLore.add(ChatColor.AQUA + "• " + ChatColor.WHITE + "Delete: " + ChatColor.GRAY + "Right-click (Admin)");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(53, help);
    }
    
    private static void registerEventListener(ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (!e.getView().getTitle().equals(GUI_NAME) || !(e.getWhoClicked() instanceof Player)) {
                    return;
                }
                
                e.setCancelled(true);
                Player player = (Player) e.getWhoClicked();
                int slot = e.getRawSlot();
                ItemStack item = e.getCurrentItem();
                
                if (item == null || item.getType() == Material.AIR) return;
                
                switch (slot) {
                    case 45: // Back button
                        player.closeInventory();
                        EnhancedMainGUI.open(player, plugin);
                        break;
                    case 49: // Refresh button
                        player.closeInventory();
                        open(player, plugin);
                        break;
                    case 53: // Help button
                        player.sendMessage("");
                        player.sendMessage(ChatColor.GREEN + "=== Saved Dungeons Help ===");
                        player.sendMessage(ChatColor.YELLOW + "Commands:");
                        player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "/dgn save <name> " + ChatColor.GRAY + "- Save current dungeon");
                        player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "/dgn load <saved_name> <new_name> " + ChatColor.GRAY + "- Load saved dungeon");
                        player.sendMessage("");
                        player.sendMessage(ChatColor.YELLOW + "GUI Actions:");
                        player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Left-click " + ChatColor.GRAY + "- Load dungeon with auto-generated name");
                        player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + "Right-click " + ChatColor.GRAY + "- Delete saved dungeon (Admin only)");
                        break;
                    default:
                        // Check if it's a saved dungeon item
                        if (slot >= 9 && slot < 45 && item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                            String displayName = item.getItemMeta().getDisplayName();
                            if (displayName.startsWith(ChatColor.LIGHT_PURPLE + "📚 ")) {
                                String savedName = ChatColor.stripColor(displayName).substring(2); // Remove "📚 "
                                
                                if (e.isLeftClick()) {
                                    // Load saved dungeon
                                    handleLoadSavedDungeon(player, savedName, plugin);
                                } else if (e.isRightClick() && player.hasPermission("apexdungeons.admin")) {
                                    // Delete saved dungeon
                                    handleDeleteSavedDungeon(player, savedName, plugin);
                                } else if (e.isRightClick()) {
                                    player.sendMessage(ChatColor.RED + "You need admin permission to delete saved dungeons!");
                                }
                            }
                        }
                        break;
                }
            }
        }, plugin);
    }
    
    private static void handleLoadSavedDungeon(Player player, String savedName, ApexDungeons plugin) {
        // Generate unique dungeon name
        String newDungeonName = savedName + "_" + System.currentTimeMillis();
        
        player.closeInventory();
        player.sendMessage(ChatColor.YELLOW + "Loading saved dungeon '" + savedName + "'...");
        
        boolean success = plugin.getSavedDungeonManager().loadSavedDungeon(savedName, newDungeonName, player);
        if (!success) {
            player.sendMessage(ChatColor.RED + "Failed to load saved dungeon. It may not exist or there was an error.");
        }
    }
    
    private static void handleDeleteSavedDungeon(Player player, String savedName, ApexDungeons plugin) {
        boolean success = plugin.getSavedDungeonManager().deleteSavedDungeon(savedName);
        if (success) {
            player.sendMessage(ChatColor.GREEN + "Successfully deleted saved dungeon '" + savedName + "'!");
            // Refresh the GUI
            open(player, plugin);
        } else {
            player.sendMessage(ChatColor.RED + "Failed to delete saved dungeon '" + savedName + "'!");
        }
    }
}
