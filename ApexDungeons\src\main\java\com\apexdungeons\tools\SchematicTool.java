package com.apexdungeons.tools;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.schematics.SchematicData;
import com.apexdungeons.schematics.SchematicPreview;
import org.bukkit.*;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;

/**
 * Tool for manually placing schematics with preview/ghost outline functionality.
 * Allows players to see exactly where structures will be placed before confirming.
 */
public class SchematicTool implements Listener {
    private final ApexDungeons plugin;
    private final NamespacedKey schematicToolKey;
    private final NamespacedKey schematicNameKey;
    
    // Track active previews and placement sessions
    private final Map<UUID, com.apexdungeons.schematics.SchematicPreview> activePreviews = new HashMap<>();
    private final Map<UUID, BukkitTask> previewTasks = new HashMap<>();
    
    public SchematicTool(ApexDungeons plugin) {
        this.plugin = plugin;
        this.schematicToolKey = new NamespacedKey(plugin, "schematic_tool");
        this.schematicNameKey = new NamespacedKey(plugin, "schematic_name");
        
        // Register event listener
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Create a schematic placement tool for a specific schematic.
     */
    public ItemStack createSchematicTool(String schematicName) {
        SchematicData schematic = plugin.getSchematicManager().getSchematic(schematicName);
        if (schematic == null) {
            return null;
        }
        
        ItemStack tool = new ItemStack(Material.GOLDEN_SHOVEL);
        ItemMeta meta = tool.getItemMeta();
        if (meta != null) {
            // Count non-air blocks for complexity indicator
            int blockCount = countNonAirBlocks(schematic);
            String complexity = getComplexityLevel(blockCount);

            meta.setDisplayName(ChatColor.GOLD + "📐 Schematic Tool: " + ChatColor.YELLOW + schematicName);

            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "Advanced schematic placement with 3D preview");
            lore.add("");
            lore.add(ChatColor.AQUA + "📊 Schematic Info:");
            lore.add(ChatColor.YELLOW + "  Size: " + ChatColor.WHITE + schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getDepth());
            lore.add(ChatColor.YELLOW + "  Blocks: " + ChatColor.WHITE + blockCount);
            lore.add(ChatColor.YELLOW + "  Complexity: " + complexity);
            lore.add("");
            lore.add(ChatColor.GREEN + "🎮 Basic Controls:");
            lore.add(ChatColor.AQUA + "  Right-click: " + ChatColor.WHITE + "Start 3D preview");
            lore.add(ChatColor.AQUA + "  Left-click: " + ChatColor.WHITE + "Quick place (no preview)");
            lore.add(ChatColor.AQUA + "  Shift+Right-click: " + ChatColor.WHITE + "Cancel preview");
            lore.add("");
            lore.add(ChatColor.GREEN + "⌨️ Preview Controls:");
            lore.add(ChatColor.AQUA + "  W/A/S/D: " + ChatColor.WHITE + "Move horizontally");
            lore.add(ChatColor.AQUA + "  Space/Shift: " + ChatColor.WHITE + "Move up/down");
            lore.add(ChatColor.AQUA + "  R: " + ChatColor.WHITE + "Rotate 90° clockwise");
            lore.add(ChatColor.AQUA + "  Q/E: " + ChatColor.WHITE + "Rotate left/right");
            lore.add(ChatColor.AQUA + "  Enter: " + ChatColor.WHITE + "Confirm placement");
            lore.add(ChatColor.AQUA + "  Escape: " + ChatColor.WHITE + "Cancel preview");
            lore.add("");
            lore.add(ChatColor.GRAY + "💡 Type control keys in chat during preview!");

            meta.setLore(lore);
            meta.getPersistentDataContainer().set(schematicToolKey, PersistentDataType.BYTE, (byte) 1);
            meta.getPersistentDataContainer().set(schematicNameKey, PersistentDataType.STRING, schematicName);
            tool.setItemMeta(meta);
        }
        return tool;
    }

    /**
     * Count non-air blocks in a schematic.
     */
    private int countNonAirBlocks(SchematicData schematic) {
        int count = 0;
        Material[][][] blocks = schematic.getBlocks();

        for (Material[][] layer : blocks) {
            for (Material[] row : layer) {
                for (Material block : row) {
                    if (block != Material.AIR) {
                        count++;
                    }
                }
            }
        }

        return count;
    }

    /**
     * Get complexity level based on block count.
     */
    private String getComplexityLevel(int blockCount) {
        if (blockCount < 50) {
            return ChatColor.GREEN + "Simple";
        } else if (blockCount < 200) {
            return ChatColor.YELLOW + "Medium";
        } else if (blockCount < 500) {
            return ChatColor.GOLD + "Complex";
        } else {
            return ChatColor.RED + "Very Complex";
        }
    }

    /**
     * Handle player interactions with the schematic tool.
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        
        if (!isSchematicTool(item)) {
            return;
        }
        
        event.setCancelled(true);
        
        String schematicName = getSchematicName(item);
        if (schematicName == null) {
            player.sendMessage(ChatColor.RED + "Invalid schematic tool!");
            return;
        }
        
        Block targetBlock = player.getTargetBlockExact(10);
        if (targetBlock == null || targetBlock.getType() == Material.AIR) {
            player.sendMessage(ChatColor.YELLOW + "Look at a block to target placement location!");
            return;
        }
        
        Location targetLocation = targetBlock.getLocation().add(0, 1, 0);
        
        if (event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            if (player.isSneaking()) {
                // Cancel preview
                cancelPreview(player);
            } else {
                // Show preview
                showSchematicPreview(player, schematicName, targetLocation);
            }
        } else if (event.getAction() == Action.LEFT_CLICK_AIR || event.getAction() == Action.LEFT_CLICK_BLOCK) {
            // Place schematic
            placeSchematic(player, schematicName, targetLocation);
        }
    }

    /**
     * Show a ghost preview of the schematic at the target location.
     */
    private void showSchematicPreview(Player player, String schematicName, Location location) {
        SchematicData schematic = plugin.getSchematicManager().getSchematic(schematicName);
        if (schematic == null) {
            player.sendMessage(ChatColor.RED + "Schematic not found: " + schematicName);
            return;
        }
        
        // Cancel existing preview
        cancelPreview(player);
        
        // Create enhanced preview
        com.apexdungeons.schematics.SchematicPreview preview = new com.apexdungeons.schematics.SchematicPreview(plugin, player, schematic, location);
        activePreviews.put(player.getUniqueId(), preview);

        // Register with input handler for keyboard controls
        plugin.getPreviewInputHandler().registerPreview(player, preview);

        // Start the enhanced preview
        preview.startPreview();

        player.sendMessage(ChatColor.GREEN + "Enhanced preview started for: " + ChatColor.YELLOW + schematicName);
        player.sendMessage(ChatColor.AQUA + "Size: " + ChatColor.WHITE + schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getDepth());
        player.playSound(location, Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.2f);
    }



    /**
     * Show particles at the corners of the schematic.
     */
    private void showCornerParticles(Player player, Location base, int width, int height, int depth) {
        World world = base.getWorld();
        if (world == null) return;
        
        // Define corner positions
        Location[] corners = {
            base.clone(),
            base.clone().add(width-1, 0, 0),
            base.clone().add(0, 0, depth-1),
            base.clone().add(width-1, 0, depth-1),
            base.clone().add(0, height-1, 0),
            base.clone().add(width-1, height-1, 0),
            base.clone().add(0, height-1, depth-1),
            base.clone().add(width-1, height-1, depth-1)
        };
        
        for (Location corner : corners) {
            world.spawnParticle(Particle.END_ROD, corner.add(0.5, 0.5, 0.5), 1, 0, 0, 0, 0);
        }
    }

    /**
     * Show particles along the edges of the schematic.
     */
    private void showEdgeParticles(Player player, Location base, int width, int height, int depth) {
        World world = base.getWorld();
        if (world == null) return;
        
        // Show particles along bottom edges (every 2 blocks to reduce lag)
        for (int x = 0; x < width; x += 2) {
            world.spawnParticle(Particle.ENCHANT, base.clone().add(x + 0.5, 0.5, 0.5), 1, 0, 0, 0, 0);
            world.spawnParticle(Particle.ENCHANT, base.clone().add(x + 0.5, 0.5, depth - 0.5), 1, 0, 0, 0, 0);
        }
        for (int z = 0; z < depth; z += 2) {
            world.spawnParticle(Particle.ENCHANT, base.clone().add(0.5, 0.5, z + 0.5), 1, 0, 0, 0, 0);
            world.spawnParticle(Particle.ENCHANT, base.clone().add(width - 0.5, 0.5, z + 0.5), 1, 0, 0, 0, 0);
        }
    }

    /**
     * Show particles for key blocks in the schematic.
     */
    private void showKeyBlockParticles(Player player, SchematicData schematic, Location base) {
        World world = base.getWorld();
        if (world == null) return;
        
        // Show particles for some non-air blocks (sample to avoid lag)
        int sampleRate = Math.max(1, schematic.getSolidBlocks() / 50); // Limit to ~50 particles
        int count = 0;
        
        for (int y = 0; y < schematic.getHeight() && count < 50; y++) {
            for (int z = 0; z < schematic.getDepth() && count < 50; z += sampleRate) {
                for (int x = 0; x < schematic.getWidth() && count < 50; x += sampleRate) {
                    Material material = schematic.getBlockAt(x, y, z);
                    if (material != Material.AIR) {
                        Location blockLoc = base.clone().add(x + 0.5, y + 0.5, z + 0.5);
                        world.spawnParticle(Particle.HAPPY_VILLAGER, blockLoc, 1, 0, 0, 0, 0);
                        count++;
                    }
                }
            }
        }
    }

    /**
     * Place the schematic at the target location.
     */
    private void placeSchematic(Player player, String schematicName, Location location) {
        // Cancel preview first
        cancelPreview(player);
        
        player.sendMessage(ChatColor.YELLOW + "Placing schematic " + ChatColor.AQUA + schematicName + ChatColor.YELLOW + "...");
        
        plugin.getSchematicManager().placeSchematic(schematicName, location).thenAccept(success -> {
            if (success) {
                player.sendMessage(ChatColor.GREEN + "Schematic placed successfully!");
                player.playSound(location, Sound.BLOCK_ANVIL_USE, 1.0f, 1.2f);
            } else {
                player.sendMessage(ChatColor.RED + "Failed to place schematic!");
            }
        });
    }

    /**
     * Cancel the preview for a player.
     */
    private void cancelPreview(Player player) {
        UUID playerId = player.getUniqueId();

        // Stop enhanced preview
        com.apexdungeons.schematics.SchematicPreview preview = activePreviews.remove(playerId);
        if (preview != null) {
            preview.stopPreview();
        }

        // Unregister from input handler
        plugin.getPreviewInputHandler().unregisterPreview(player);

        // Cancel any remaining tasks
        BukkitTask task = previewTasks.remove(playerId);
        if (task != null) {
            task.cancel();
        }

        player.sendMessage(ChatColor.GRAY + "Preview cancelled.");
    }

    /**
     * Handle player quit to clean up previews.
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        cancelPreview(event.getPlayer());
    }

    /**
     * Check if an item is a schematic tool.
     */
    private boolean isSchematicTool(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().has(schematicToolKey, PersistentDataType.BYTE);
    }

    /**
     * Get the schematic name from a schematic tool.
     */
    private String getSchematicName(ItemStack item) {
        if (!isSchematicTool(item)) {
            return null;
        }
        
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().get(schematicNameKey, PersistentDataType.STRING);
    }

    /**
     * Get all active previews.
     */
    public Map<UUID, com.apexdungeons.schematics.SchematicPreview> getActivePreviews() {
        return new HashMap<>(activePreviews);
    }

    /**
     * Shutdown and cleanup.
     */
    public void shutdown() {
        // Cancel all preview tasks
        for (BukkitTask task : previewTasks.values()) {
            task.cancel();
        }
        previewTasks.clear();
        activePreviews.clear();
        
        plugin.getLogger().info("SchematicTool shutdown complete.");
    }

}
