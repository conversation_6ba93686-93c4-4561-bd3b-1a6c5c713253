package org.bukkit.configuration.file;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.bukkit.configuration.InvalidConfigurationException;

/**
 * Stub for FileConfiguration.  Provides minimal methods used by ApexDungeons.
 */
public class FileConfiguration {
    public void load(File file) throws java.io.IOException, InvalidConfigurationException {}
    public String getString(String path) { return null; }
    public String getString(String path, String def) { return def; }
    public int getInt(String path) { return 0; }
    public int getInt(String path, int def) { return def; }
    public double getDouble(String path) { return 0.0; }
    public double getDouble(String path, double def) { return def; }
    public boolean getBoolean(String path) { return false; }
    public boolean getBoolean(String path, boolean def) { return def; }
    public List<String> getStringList(String path) { return new ArrayList<>(); }
    public List<Integer> getIntegerList(String path) { return new ArrayList<>(); }
    public List<?> getList(String path) { return new ArrayList<>(); }
    public List<?> getList(String path, List<?> def) { return def; }

    /**
     * Returns whether the value at the given path is a list.  Always false in stub.
     */
    public boolean isList(String path) { return false; }
    public boolean isConfigurationSection(String path) { return false; }
    public FileConfiguration getConfigurationSection(String path) { return this; }
    public Set<String> getKeys(boolean deep) { return java.util.Collections.emptySet(); }
    public Map<String, Object> getValues(boolean deep) { return java.util.Collections.emptyMap(); }
}